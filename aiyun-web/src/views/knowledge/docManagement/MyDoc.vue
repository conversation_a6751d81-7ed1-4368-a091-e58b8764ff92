<template>
  <div id="container"></div>
  <DocView ref="docViewRef" @success="handleSearch" />
  <ImgView ref="imgViewRef" />
</template>

<script setup>
import { Graph, Polyline, register, ExtensionCategory, treeToGraphData } from '@antv/g6';
import TreeNode from './components/TreeNode';
import DocView from './components/DocView.vue'
import ImgView from './components/ImgView.vue';

import { getMyIndividual } from '@/api/knowledge/docmangement/index';

const docViewRef = ref(null);
const imgViewRef = ref()

class AnimatedPolyline extends Polyline {
  onCreate() {
    // 获取边的 path 图形
    const path = this.shapeMap.path;
    if (path) {
      // 设置虚线样式
      path.attr('lineDash', [20, 20]);

      // 添加蚂蚁线动画
      path.animate(
        [{ lineDashOffset: -20 }, { lineDashOffset: 0 }],
        {
          duration: 100000,
          iterations: Infinity,
        }
      );
    }
  }
}

const handleView = (row) => {
  if (!row.fileUrl) {
    message.warning('文件生成中， 请稍后')
    return
  }
  const imgType = ['.jpg', '.jpeg', '.png',]
  const fileType = row.fileUrl.substring(row.fileUrl.lastIndexOf('.'))
  if (imgType.includes(fileType)) {
    imgViewRef.value.open(row)
  } else {
    docViewRef.value.open('view', {}, row, true, '知识库');
  }
};

const transformData = (input) => {
  const nodes = [];
    const edges = [];
    let nodeIdCounter = 0;

    function traverse(node, parentId = null, level = 1) {
        // const nodeId = `node_${nodeIdCounter++}`;
        nodes.push({
            ...node,
            data: { label: node.name, width: 100 + node.name.length * 10, height: 100 },
            level: level,
            children: node.children ? node.children.map(child => (child.id)) : []
        });

        if (parentId) {
            edges.push({
                id: `${parentId}->${node.id}`,
                source: parentId,
                target: node.id
            });
        }
        if (node.children) {
            node.children.forEach(child => traverse(child, node.id, level + 1));
        }
    }

    traverse(input);

    return { nodes, edges };
}

onMounted(async() => {
  const treeList = await getMyIndividual()
  // const treeList = {
  //   id: 'root',
  //   label: '知识管理',
  //   name: '知识管理',
  //   children: [
  //     {
  //       id: 'kspacey',
  //       label: '管理员',
  //       name: '管理员',
  //       children: [
  //         {
  //           id: 'swilliams',
  //           name: '超级管理员',
  //           label: '超级管理员',
  //         },
  //         {
  //           id: 'bpitt',
  //           name: '普通角色',
  //           label: '普通角色',
  //         },
  //         {
  //           id: 'hford',
  //           name: '正式员工',
  //           label: '正式员工',
  //         }
  //       ]
  //     }
  //   ]
  // }
  console.log('treeList', treeList);
  const data = transformData(treeList);
  console.log('data', data);
  
  // const data = {
  //   nodes: [
  //     { id: 'kspacey', data: { label: '管理员', width: 144, height: 100 } , level: 1 },
  //     { id: 'swilliams', data: { label: '超级管理员', width: 160, height: 100 }, level: 2 },
  //     { id: 'bpitt', data: { label: '普通角色', width: 108, height: 100 } , level: 2},
  //     { id: 'hford', data: { label: '正式员工', width: 168, height: 100 }, level: 2 },
  //     { id: 'lwilson', data: { label: '参考知识', width: 144, height: 100 }, level: 3 },
  //     { id: 'kbacon', data: { label: '规章制度', width: 121, height: 100 }, level: 3 },
  //   ],
  //   edges: [
  //     { id: 'kspacey->swilliams', source: 'kspacey', target: 'swilliams' },
  //     { id: 'kspacey->bpitt', source: 'kspacey', target: 'bpitt' },
  //     { id: 'kspacey->hford', source: 'kspacey', target: 'hford' },
  //     { id: 'bpitt->lwilson', source: 'bpitt', target: 'lwilson' },
  //     { id: 'bpitt->kbacon', source: 'bpitt', target: 'kbacon' },
  //   ],
  // };

  // register(ExtensionCategory.EDGE, 'ant-line', AnimatedPolyline, );
  register(ExtensionCategory.NODE, 'tree-node', TreeNode);
  
  const graph = new Graph({
    container: document.getElementById('container'),
    behaviors: ['drag-canvas', 'zoom-canvas', 'click-select'],
    plugins: ['grid', {
      type: 'tooltip',
      getContent: (e) => {
        if (e.target.type === 'node'){
          const nodeData = graph.getNodeData(e.target.id);
          if(nodeData.level === 3){
            console.log('节点数据:', nodeData);
          }
          return `<div>节点：${e.target.id}</div><div>已阅：60%</div>`
        }
      },
      key: 'my-tooltip', // 为插件指定key，便于后续更新
    }],
    autoFit: {
      type: 'center', // 自适应类型：'view' 或 'center'
      options: {
        // 仅适用于 'view' 类型
        when: 'overflow', // 何时适配：'overflow'(仅当内容溢出时) 或 'always'(总是适配)
        direction: 'x', // 适配方向：'x'、'y' 或 'both'
      },
      animation: {
        // 自适应动画效果
        duration: 1000, // 动画持续时间(毫秒)
        easing: 'ease-in-out', // 动画缓动函数
      },
    },
    autoResize: true,
    data,
    node: {
      type: 'tree-node',
      style: {
        size: [150, 100],
        radius: 10,
        iconText: (d) => d.data.label,
        iconFontSize: 14,
        shadowColor: '#000000', // 默认阴影颜色
        shadowBlur: 0, // 默认阴影模糊度
      },
      state: {
        selected: {
          stroke: '#14925f', // 选中时的边框颜色
          lineWidth: 1, // 边框宽度
        },
      },
      behaviors: [
        {
          type: 'click-select',
          degree: 2,
          state: 'selected',
          neighborState: 'neighborActive', // 相邻节点附着状态
          unselectedState: 'inactive', // 未选中节点状态
          multiple: true,
          trigger: ['shift'],
        },
        'drag-element',
      ],
      palette: {
        type: 'group',
        field: 'label',
      },
    },
    edge: {
      type: 'polyline',
      style: {
        router: {
          type: 'orth',
        },
        stroke: '#1890ff', // 边颜色
        lineWidth: 2, // 边宽度
        lineDash: [2, 2], // 虚线
        endArrow: true, // 终止箭头
      },
    },
    layout: {
      type: 'dagre',
      ranksep: 180,
      nodesep: 80, // 同一层级中节点之间的间距（水平方向）
      rankdir: 'LR', // 将布局方向改为从左到右
    },
  });

  graph.render();

  graph.on('node:click', (event) => {
    // 获取节点数据
    const nodeData = graph.getNodeData(event.target.id);
    if (nodeData.level > 2 && nodeData.fileUrl) {
      handleView(nodeData)
      console.log('节点数据:', nodeData);
    }
  });
});
</script>
