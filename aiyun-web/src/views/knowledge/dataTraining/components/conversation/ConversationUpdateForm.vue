<template>
  <Dialog title="设定" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="130px" v-loading="formLoading">
      <el-form-item label="角色设定" prop="role">
        <el-input type="textarea" v-model="formData.role" :rows="4" placeholder="请输入角色设定" />
      </el-form-item>
      <el-form-item label="会话类型" prop="configType" style="display: none;">
        <el-radio-group v-model="formData.configType" @change="handleChatTypeChange">
          <el-radio-button value="1">模型</el-radio-button>
          <el-radio-button value="2">RAG</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="formData.configType === '1'" :label="formData.configType === '1' ? '模型' : 'RAG名称'" prop="configId">
        <el-select 
          v-model="formData.configId" 
          :placeholder="formData.configType === '1' ? '请选择模型' : '请选择RAG'"
          filterable 
          clearable 
          :filter-method="filterOptions" 
          :remote="false">
          <el-option 
            v-for="item in filteredList" 
            :key="item.id"
            :label="formData.configType === '1' ? item.modelName : item.chatName" 
            :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="Token数" prop="tokenNum">
        <el-input-number v-model="formData.tokenNum" :min="1"  />
      </el-form-item>
      <el-form-item label="上下文数量" prop="contextNum">
        <el-input-number v-model="formData.contextNum" :min="1"  />
      </el-form-item>
      <!-- <el-form-item label="温度参数" prop="temperature">
        <el-input-number v-model="formData.temperature" placeholder="请输入温度参数" :min="0" :max="2" :precision="2" />
      </el-form-item>
      <el-form-item label="回复数 Token 数" prop="maxTokens">
        <el-input-number v-model="formData.maxTokens" placeholder="请输入回复数 Token 数" :min="0" :max="4096" />
      </el-form-item>
      <el-form-item label="上下文数量" prop="maxContexts">
        <el-input-number v-model="formData.maxContexts" placeholder="请输入上下文数量" :min="0" :max="20" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CommonStatusEnum } from '@/utils/constants'
import { ChatModelApi, ChatModelVO } from '@/api/ai/model/chatModel'
import { ChatConversationApi, ChatConversationVO } from '@/api/ai/chat/conversation'
import { getArrangeModelPage } from '@/api/knowledge/aiSettings/model'
import { getklklArrangeChatPage } from '@/api/knowledge/aiSettings/knowledge'
import { saveKlMessageConfig } from '@/api/knowledge/model'

/** AI 聊天对话的更新表单 */
defineOptions({ name: 'ChatConversationUpdateForm' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  role: undefined,
  configType: '1', // 默认选择模型类型
  configId: undefined,
  tokenNum: 1,
  contextNum: 1,
})
const formRules = reactive({
  role: [{ required: true, message: '角色设定不能为空', trigger: 'blur' }],
  configType: [{ required: true, message: '会话类型不能为空', trigger: 'change' }],
  configId: [{ required: true, message: '模型/RAG不能为空', trigger: 'change' }],
  tokenNum: [{ required: true, message: 'Token数不能为空', trigger: 'blur' }],
  contextNum: [{ required: true, message: '上下文数量不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const modelList = ref([] as ChatModelVO[]) // 模型列表
const ragList = ref([] as any[]) // RAG列表
const filteredList = ref([] as any[]) // 过滤后的列表

/** 过滤选项列表 */
const filterOptions = (query: string) => {
  if (query) {
    // 根据输入的关键字过滤列表
    const sourceList = formData.value.configType === '1' ? modelList.value : ragList.value
    const field = formData.value.configType === '1' ? 'modelName' : 'chatName'
    filteredList.value = sourceList.filter(item =>
      item[field].toLowerCase().includes(query.toLowerCase())
    )
  } else {
    // 如果没有输入关键字，显示全部
    filteredList.value = formData.value.configType === '1' ? modelList.value : ragList.value
  }
}

/** 处理会话类型变更 */
const handleChatTypeChange = () => {
  formData.value.configId = undefined // 清空选择
  filteredList.value = formData.value.configType === '1' ? modelList.value : ragList.value
}

/** 打开弹窗 */
const open = async (modelConfig: any, modelLists: any, ragLists: any) => {
  dialogVisible.value = true
  resetForm()
  formLoading.value = true
  // 获取模型列表
  // const { records: modelRecords } = await getArrangeModelPage({
  //   pageNum: 1,
  //   pageSize: 1000
  // })
  modelList.value = modelLists
  // // 获取RAG列表
  // const { records: ragRecords } = await getklklArrangeChatPage({
  //   pageNum: 1,
  //   pageSize: 1000
  // })
  ragList.value = ragLists
  // 初始化过滤后的列表为模型列表
  // formData.configType === '1'
  filteredList.value = modelConfig.configType === '1' ? modelList.value : ragList.value

  formData.value = { 
    ...modelConfig, 
    // configType: type, 
    configId: modelConfig.configId, 
    tokenNum: modelConfig.configType ? modelConfig.tokenNum : 4096,
    contextNum: modelConfig.configType ? modelConfig.contextNum : 10 
  }
  formLoading.value = false

}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success', 'cancel']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  if ( formLoading.value) return
  // 提交请求
  formLoading.value = true
  try {
    // 获取选中项的名称
    const selectedItem = (formData.value.configType === '1' ? modelList.value : ragList.value)
      .find(item => item.id === formData.value.configId)

    const data = {
      ...formData.value,
      modelName: formData.value.configType === '1'
        ? selectedItem?.modelName
        : selectedItem?.chatName
    } as unknown as ChatConversationVO

    await saveKlMessageConfig(data)
    message.success('对话配置已更新')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    role: undefined,
    configType: '1', // 重置为默认模型类型
    configId: undefined,
    tokenNum: 1,
    contextNum: 1,
  }
  formRef.value?.resetFields()
}
</script>
