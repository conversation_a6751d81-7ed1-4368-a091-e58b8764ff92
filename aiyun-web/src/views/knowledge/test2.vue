<template>
  <div class="test-box">
    <!-- 搜索表单 -->
    <el-form :model="searchForm" ref="searchForm" label-width="120px">
      <!-- 第一行：区域、省份、客户名称 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="客户所在区域">
            <el-input v-model="searchForm.region" placeholder="请输入客户所在区域" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户所在省份">
            <el-input v-model="searchForm.province" placeholder="请输入客户所在省份" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户名称">
            <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：状态、线索创建时间、预估利润（默认隐藏） -->
      <el-row :gutter="20" v-if="isExpanded">
        <el-col :span="8">
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="线索创建时间">
            <el-date-picker
              v-model="searchForm.createTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预估利润">
            <el-select v-model="searchForm.profit" placeholder="请选择预估利润" clearable>
              <el-option
                v-for="item in profitOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <div style="margin-bottom: 10px; overflow: hidden;">
        <!-- 左侧按钮：新增、导出 -->
        <div style="float: left;">
          <el-button  @click="onAdd">新增</el-button>
          <el-button  @click="onExport">导出</el-button>
        </div>
        <!-- 右侧按钮：搜索、重置、展开/收起 -->
        <div style="float: right;">
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
          <el-button type="text" @click="toggleExpand">
            {{ isExpanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </div>
    </el-form>

    <!-- 表格 -->
    <el-table
      :data="pagedTableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      :header-cell-class-name="headerCellClassName"
      :row-class-name="rowClassName"
    >
      <!-- 选择框列 -->
      <el-table-column type="selection" width="55" />
      <!-- 序号列 -->
      <el-table-column type="index" label="序号" width="80" />
      <!-- 数据列 -->
      <el-table-column prop="region" label="客户所在区域" />
      <el-table-column prop="province" label="客户所在省份" />
      <el-table-column prop="customerName" label="客户名称" />
      <el-table-column prop="status" label="状态" />
      <el-table-column prop="createTime" label="线索创建时间" />
      <el-table-column prop="profit" label="预估利润" />
    </el-table>

    <!-- 分页 -->
    <el-pagination
      style="margin-top: 20px; text-align: right;"
      background
      size="small"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="tableData.length"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script>
import { ref, computed } from 'vue';

export default {
  setup() {
    // 搜索表单数据
    const searchForm = ref({
      region: '', // 客户所在区域
      province: '', // 客户所在省份
      customerName: '', // 客户名称
      status: '', // 状态
      createTime: '', // 线索创建时间
      profit: '', // 预估利润
    });

    // 状态下拉框选项
    const statusOptions = ref([
      { value: '1', label: '有效' },
      { value: '2', label: '无效' },
      { value: '3', label: '待确认' },
    ]);

    // 预估利润下拉框选项
    const profitOptions = ref([
      { value: 'high', label: '高' },
      { value: 'medium', label: '中' },
      { value: 'low', label: '低' },
    ]);

    // 表格数据
    const tableData = ref([
      // 示例数据
      { region: '华东', province: '上海', customerName: '客户A', status: '有效', createTime: '2023-10-01', profit: '高' },
      { region: '华南', province: '广东', customerName: '客户B', status: '无效', createTime: '2023-09-15', profit: '中' },
      { region: '华北', province: '北京', customerName: '客户C', status: '待确认', createTime: '2023-08-20', profit: '低' },
      { region: '华中', province: '湖北', customerName: '客户D', status: '有效', createTime: '2023-07-10', profit: '高' },
      { region: '华东', province: '浙江', customerName: '客户E', status: '无效', createTime: '2023-06-05', profit: '中' },
      { region: '华南', province: '广西', customerName: '客户F', status: '待确认', createTime: '2023-05-01', profit: '低' },
      { region: '华北', province: '天津', customerName: '客户G', status: '有效', createTime: '2023-04-15', profit: '高' },
      { region: '华中', province: '湖南', customerName: '客户H', status: '无效', createTime: '2023-03-10', profit: '中' },
      { region: '华东', province: '江苏', customerName: '客户I', status: '待确认', createTime: '2023-02-05', profit: '低' },
      { region: '华南', province: '福建', customerName: '客户J', status: '有效', createTime: '2023-01-01', profit: '高' },
    ]);

    // 分页相关数据
    const currentPage = ref(1); // 当前页码
    const pageSize = ref(10); // 每页显示条数

    // 计算分页后的表格数据
    const pagedTableData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return tableData.value.slice(start, end);
    });

    // 是否展开
    const isExpanded = ref(false);

    // 切换展开/收起
    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value;
    };

    // 搜索方法
    const onSearch = () => {
      console.log('搜索条件:', searchForm.value);
      // 这里可以根据搜索条件过滤表格数据
    };

    // 重置方法
    const onReset = () => {
      searchForm.value = {
        region: '',
        province: '',
        customerName: '',
        status: '',
        createTime: '',
        profit: '',
      };
      console.log('表单已重置');
    };

    // 新增方法
    const onAdd = () => {
      console.log('点击了新增按钮');
      // 这里可以跳转到新增页面或打开新增弹窗
    };

    // 导出方法
    const onExport = () => {
      console.log('点击了导出按钮');
      // 这里可以调用导出接口或实现导出逻辑
    };

    // 表格选择框事件
    const handleSelectionChange = (selection) => {
      console.log('选中的数据:', selection);
    };

    // 分页大小改变事件
    const handleSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1; // 重置到第一页
    };

    // 页码改变事件
    const handlePageChange = (page) => {
      currentPage.value = page;
    };
    // 表头单元格类名
    const headerCellClassName = () => {
      return 'custom-header-cell';
    };

    // 表格行类名
    const rowClassName = ({ rowIndex }) => {
      return rowIndex % 2 === 1 ? 'even-row row-calss' : 'row-calss';
    };

    return {
      searchForm,
      statusOptions,
      profitOptions,
      tableData,
      pagedTableData,
      isExpanded,
      currentPage,
      pageSize,
      toggleExpand,
      onSearch,
      onReset,
      onAdd,
      onExport,
      handleSelectionChange,
      handleSizeChange,
      handlePageChange,
      headerCellClassName,
      rowClassName,
    };
  },
};
</script>

<style scoped>
/* 可以在这里添加一些自定义样式 */
.test-box {
  background-color: #fff;
  padding: 10px;
}

/* 搜索表单字体大小 */
.search-form :deep(.el-form-item__label),
.search-form :deep(.el-input__inner),
.search-form :deep(.el-select__placeholder),
.search-form :deep(.el-range-input),
.search-form :deep(.el-button) {
  font-size: 12px;
}

.custom-table :deep(.el-table th .cell) {
  color: #000000;
}

/* 表格字体大小 */
.custom-table :deep(.el-table th),
.custom-table :deep(.el-table td) {
  font-size: 12px;
}
/* 表头置灰 */
:deep(.custom-header-cell) {
  background-color: #eef1f0 !important;
  color: #636361;
  font-weight: 600;
  font-size: 12px;
}

/* 偶数行置灰 */
:deep(.even-row) {
  background-color: #eef1f0;
}
:deep(.row-calss) {
  font-size: 12px;
}
</style>

<style >
.el-form-item__label {
  font-size: 12px;
  color: #636361;
  font-weight: 550;
}
.el-input__inner {
  font-size: 12px;
}
.el-select__placeholder {
  font-size: 12px;
}
.el-range-input {
  font-size: 12px !important;
}
.el-input__wrapper, .el-select__wrapper {
  border-radius: 0;
}
</style>