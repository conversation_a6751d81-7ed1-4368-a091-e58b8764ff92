<template>
    <el-dialog
      v-model="dialogVisible" 
      width="700"
      title="设置">
      <div class="content">
        <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="140px">
          <el-form-item label="角色" prop="role">
            <el-select v-model="formData.role" placeholder="请选择角色">
              <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="入职时间" prop="entryTime">
            <el-date-picker
              v-model="formData.entryTime"
              clearable
              placeholder="请选择入职时间"
              type="date"
              style="width: 100%;"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>  
          <el-form-item label="岗位" prop="postName">
              <el-select v-model="formData.postName" placeholder="请选择">
                <el-option
                  v-for="item in postList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="岗位开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                clearable
                placeholder="请选择岗位开始时间"
                type="date"
                style="width: 100%;"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            
            <el-form-item label="问题" prop="question">
              <el-input v-model="formData.question" placeholder="请输入问题" />
            </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </el-dialog>  
  </template>
  <script setup>
  import * as PostApi from '@/api/system/post'
  import * as RoleApi from '@/api/system/role'
  import * as HomeApi from '@/api/home/<USER>'
  
  import { v4 as uuidv4 } from 'uuid'
  
  defineOptions({ name: 'LearnForm' })
  
  const emit = defineEmits(['success'])
  
  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  
  const defaultTags = ref([])
  
  const dialogVisible = ref(false) // 弹窗的是否展示
  const formLoading = ref(false)
  const formRef = ref()
  const formData = ref({
    role: '',
    postName: '',
    question: '',
    entryTime: '',
    startTime: ''
  })
  const formRules = reactive({
    role: [{ required: true, message: '请选择角色', trigger: 'change' }],
    postName: [{ required: true, message: '请选择岗位', trigger: 'change' }],
    entryTime: [{ required: true, message: '请选择入职时间', trigger: 'change' }],
    startTime: [{  required: true, message: '请选择岗位开始时间', trigger: 'change' }]
  })
  const postList = ref([]) // 岗位列表
  const roleList = ref([]) // 角色的列表
  /** 打开弹窗 */
  const open = async () => {
    resetForm()
    dialogVisible.value = true
    formLoading.value = true
    postList.value = await PostApi.getSimplePostList()
    roleList.value = await RoleApi.getSimpleRoleList()
    // 获取详情
    const res = await HomeApi.getStudyConfig()
    formData.value = res
    formLoading.value = false
  }
  defineExpose({ open }) // 提供 open 方法，用于打开弹窗
  
  const submitForm = async () => {
    if (formLoading.value) return
    if (!formRef) return
    const valid = await formRef.value.validate()
    if (!valid) return
    const data = formData.value
    const params = {
      ...data,
    }
    try  {
      formLoading.value = true
      const res = await HomeApi.createStudyConfig(params)
      dialogVisible.value = false
      message.success('添加成功')
      emit('success')
    } catch (error) {
      message.error(error.message)
      formLoading.value = false
      console.log(error)
    }
  }

  /** 重置表单 */
  const resetForm = () => {
    formData.value = {
      role: '',
      postName: '',
      question: '',
      entryTime: '',
      startTime: ''
    }
    formRef.value?.resetFields()
  }
  
  /** 提交表单 */
  
  </script>
  
  <style lang="scss" scoped>
    .breadcrumb-wrapper {
      display: flex;
      height: 32px;
      align-items: center;
      padding: 0 20px 0 0;
      .back-button {
        padding: 0;
        font-size: 14px;
      }
    }
    .file-item {
      line-height: 32px;
      padding: 8px;
      border-bottom: 1px solid #ccc;
      cursor: pointer;
    }
  </style>
  