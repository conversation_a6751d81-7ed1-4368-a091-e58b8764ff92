<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>Chat</title>
        <script type="text/javascript" src="scripts/sdkjs/plugins.js"></script>
        <script type="text/javascript" src="scripts/sdkjs/plugins-ui.js"></script>
        <link rel="stylesheet" href="styles/sdkjs/plugins.css" />
        <link rel="stylesheet" href="styles/style.css" />
        <link rel="stylesheet" href="styles/chat-footer.css" />
        <script src="scripts/sdkjs/jsrsasign-latest-all-min.js"></script>
        <script type="text/javascript">
            if (!/MSIE \d|Trident.*rv:/.test(navigator.userAgent)) {
                document.write("<script type='text\/javascript' src='scripts\/main.js'><\/script>");
                document.write("<script type='text\/javascript' src='scripts\/yunzhi_ai.js'><\/script>");
                document.write("<script type='text\/javascript' src='scripts\/prompt.js'><\/script>");
                document.write("<script type='text\/javascript' src='scripts\/chat-footer.js'><\/script>");
            } else {
                alert('This plugin is not supported by IE');
            }
        </script>
    </head>

    <body>
        <div class="chat-container">
            <div class="chat-header">
                <span id="chat-title">云智</span>
                <button class="clear-button" id="clear-button">
                    &#x1F5D1;
                    <!-- Trash icon -->
                </button>
            </div>
            <div class="chat-body" id="chatBody">
                <div class="message-history">
                    <!-- Historical messages are displayed here -->
                    <div class="message ai-message">
                        <div class="avatar">
                            <img src="resources/yunzhi-avatar.png" />
                            <!--                    <div class="load-icon">&#x1F504;</div>-->
                        </div>
                        <div class="bubble">
                            <div class="message-content" id="greeting"></div>
                            <div class="actions">
                                <button type="button" id="first-insert" title="insert document" onclick="">&#x2398;</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="chat-footer footer-container">
                    <!-- 已移除 RAG/模型单选，改由“智能体/知识库”按钮组控制 -->

                    <!-- 选中文件列表（与 Vue 版一致的视觉与交互） -->
                    <div class="selected-files" id="selected-files" style="display: none">
                        <div class="file-header">
                            <span class="file-title">已选择文件</span>
                            <button type="button" class="clear-files-btn" id="clear-files-btn" title="清空">清空</button>
                        </div>
                        <div class="file-list" id="file-list"></div>
                    </div>

                    <!-- 输入与按钮区域（表单容器） -->
                    <form class="prompt-from" onsubmit="return false;">
                        <textarea id="userInput" class="prompt-input" placeholder="问我任何问题...（Shift+Enter 换行，按下 Enter 发送）"></textarea>
                        <div class="prompt-btns">
                            <div class="left-section">
                                <div class="btn-group" id="agent-btn-group">
                                    <button type="button" class="agent-btn" data-type="1" id="agent-model-btn">智能体</button>
                                    <button type="button" class="agent-btn" data-type="2" id="agent-rag-btn">知识库</button>
                                </div>
                            </div>
                            <div class="right-section">
                                <button type="button" class="file-btn" id="choose-file-btn" title="选择文件">&#9729;</button>
                                <button class="send-button" id="send-button" title="发送">
                                    <img src="resources/send.png" alt="Send" width="20" height="20" />
                                </button>
                                <button class="stop-button" id="stop-button"><span>&#x25A0;</span>stop</button>
                                <button class="regenerate-button" id="regenerate-button"><span>&#x21BA;</span>regenerate</button>
                            </div>
                        </div>
                    </form>

                    <!-- 隐藏的文件选择 input，用于模拟“选择文件”弹窗的选择结果 -->

                    <!-- 文件选择对话框 -->
                    <div class="file-dialog-overlay" id="file-dialog-overlay" style="display: none">
                        <div class="file-dialog">
                            <div class="file-dialog-header">
                                <h3>选择文件</h3>
                                <button type="button" class="close-btn" id="file-close-btn">&times;</button>
                            </div>
                            <div class="file-dialog-body">
                                <div class="file-tabs">
                                    <button type="button" class="tab-btn active" data-tab="knowledge">知识库</button>
                                    <button type="button" class="tab-btn" data-tab="local">本地文件</button>
                                </div>

                                <!-- 知识库标签页 -->
                                <div class="tab-content" id="knowledge-tab">
                                    <!-- 知识库卡片网格 -->
                                    <div class="kl-content" id="kl-content">
                                        <div class="knowledge-grid" id="knowledge-grid">
                                            <!-- 动态生成知识库卡片 -->
                                        </div>
                                    </div>

                                    <!-- 文件浏览区域 -->
                                    <div class="file-browser-content" id="file-browser-content" style="display: none">
                                        <div class="breadcrumb-wrapper">
                                            <button type="button" class="back-button" id="back-btn" style="display: none">返回上一级</button>
                                            <span class="breadcrumb-divider" id="breadcrumb-divider" style="display: none">|</span>
                                            <div class="breadcrumb" id="breadcrumb">
                                                <!-- 动态生成面包屑 -->
                                            </div>
                                        </div>

                                        <!-- 文件列表 - 表格形式但不显示表头 -->
                                        <div class="kb-file-table-container">
                                            <table class="kb-file-table" id="kb-file-table">
                                                <tbody id="kb-file-tbody">
                                                    <!-- 动态生成文件行 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- 本地文件标签页 -->
                                <div class="tab-content" id="local-tab" style="display: none">
                                    <div class="upload-section">
                                        <input type="file" id="file-input" multiple style="display: none" />
                                        <button type="button" class="upload-btn" id="upload-btn">
                                            <span id="upload-btn-text">选择文件</span>
                                        </button>
                                        <div class="upload-tip">文件大小不能超过 10MB</div>
                                    </div>
                                    <div class="local-file-table-container">
                                        <table class="local-file-table" id="local-file-table">
                                            <thead>
                                                <tr>
                                                    <th>名称</th>
                                                    <th>解析状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="local-file-tbody">
                                                <!-- 动态生成文件行 -->
                                            </tbody>
                                        </table>
                                        <div class="empty-state" id="local-empty-state" style="display: none">
                                            <div style="text-align: center; color: #909399; padding: 40px">暂无上传文件</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="file-dialog-footer">
                                <button type="button" class="confirm-btn" id="file-confirm-btn">确认</button>
                                <button type="button" class="cancel-btn" id="file-cancel-btn">取消</button>
                            </div>
                        </div>
                    </div>

                    <!-- 样式已外置至 styles/chat-footer.css -->

                    <!-- 行为脚本已外置至 scripts/chat-footer.js -->
                </div>
            </div>
        </div>
    </body>
</html>
