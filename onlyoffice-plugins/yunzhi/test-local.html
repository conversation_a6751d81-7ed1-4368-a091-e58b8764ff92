<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Yun<PERSON>hi Plugin Local Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }
            .status {
                padding: 10px;
                margin: 10px 0;
                border-radius: 4px;
            }
            .success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .warning {
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            .plugin-frame {
                border: 1px solid #ddd;
                border-radius: 4px;
                margin-top: 20px;
                height: 600px;
                width: 100%;
            }
            button {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin: 5px;
            }
            button:hover {
                background-color: #0056b3;
            }
            .log {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
                max-height: 200px;
                overflow-y: auto;
                font-family: monospace;
                font-size: 12px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>YunZhi Plugin 本地测试环境</h1>

            <div id="status" class="status warning">正在初始化测试环境...</div>

            <div>
                <h3>测试操作：</h3>
                <button onclick="testPluginInit()">测试插件初始化</button>
                <button onclick="testPluginMethods()">测试插件方法</button>
                <button onclick="clearLog()">清空日志</button>
            </div>

            <div>
                <h3>控制台日志：</h3>
                <div id="log" class="log"></div>
            </div>

            <div>
                <h3>插件界面：</h3>
                <iframe id="pluginFrame" class="plugin-frame" src="index.html"></iframe>
            </div>
        </div>

        <script>
            // 模拟 OnlyOffice 插件环境
            window.Asc = window.Asc || {};
            window.Asc.plugin = window.Asc.plugin || {};

            // 模拟插件信息
            window.Asc.plugin.info = {
                guid: 'asc.{4B5EFD7F-1234-1EF4-FEAB-BFBF2E1D86AC}',
                userName: '测试用户',
                lang: 'zh-CN',
                editorType: 'word',
            };

            // 模拟插件方法
            window.Asc.plugin.executeMethod = function (method, params, callback) {
                log(`执行方法: ${method}`, params);
                if (callback) {
                    setTimeout(() => callback('模拟返回结果'), 100);
                }
            };

            window.Asc.plugin.executeCommand = function (command, data) {
                log(`执行命令: ${command}`, data);
            };

            window.Asc.plugin.callCommand = function (func, isAsync, isReturnValue, callback) {
                log('调用命令函数');
                try {
                    const result = func();
                    if (callback) callback(result);
                } catch (e) {
                    log('命令执行错误: ' + e.message);
                }
            };

            window.Asc.plugin.tr = function (text) {
                return text; // 简单的翻译函数
            };

            // 日志函数
            function log(message, data) {
                const logDiv = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}`;

                if (data) {
                    console.log(logEntry, data);
                    logDiv.innerHTML += `${logEntry} ${JSON.stringify(data)}\n`;
                } else {
                    console.log(logEntry);
                    logDiv.innerHTML += `${logEntry}\n`;
                }

                logDiv.scrollTop = logDiv.scrollHeight;
            }

            // 测试函数
            function testPluginInit() {
                log('开始测试插件初始化...');

                const iframe = document.getElementById('pluginFrame');
                const iframeWindow = iframe.contentWindow;

                // 检查 iframe 中是否有插件对象
                if (iframeWindow && iframeWindow.Asc && iframeWindow.Asc.plugin) {
                    log('在 iframe 中找到插件对象');

                    if (iframeWindow.Asc.plugin.init) {
                        try {
                            // 设置插件环境到 iframe 中
                            iframeWindow.Asc.plugin.info = window.Asc.plugin.info;
                            iframeWindow.Asc.plugin.executeMethod = window.Asc.plugin.executeMethod;
                            iframeWindow.Asc.plugin.executeCommand = window.Asc.plugin.executeCommand;
                            iframeWindow.Asc.plugin.callCommand = window.Asc.plugin.callCommand;
                            iframeWindow.Asc.plugin.tr = window.Asc.plugin.tr;

                            // 调用插件初始化
                            iframeWindow.Asc.plugin.init();
                            updateStatus('插件初始化成功！', 'success');
                            log('插件初始化函数已调用');
                        } catch (e) {
                            updateStatus('插件初始化失败: ' + e.message, 'error');
                            log('初始化错误: ' + e.message);
                        }
                    } else {
                        updateStatus('iframe 中未找到插件初始化函数', 'error');
                        log('iframe.Asc.plugin.init 不存在');
                    }
                } else {
                    updateStatus('iframe 中未找到插件对象', 'error');
                    log('iframe 中没有 Asc.plugin 对象');

                    // 尝试直接在当前窗口测试
                    if (window.Asc.plugin.init) {
                        try {
                            window.Asc.plugin.init();
                            updateStatus('在主窗口中插件初始化成功！', 'success');
                        } catch (e) {
                            updateStatus('主窗口插件初始化失败: ' + e.message, 'error');
                            log('主窗口初始化错误: ' + e.message);
                        }
                    } else {
                        updateStatus('主窗口中也未找到插件初始化函数', 'error');
                    }
                }
            }

            function testPluginMethods() {
                log('测试插件方法...');

                // 测试各种插件方法
                if (window.Asc.plugin.executeMethod) {
                    window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
                        log('获取选中文本: ' + text);
                    });
                }
            }

            function clearLog() {
                document.getElementById('log').innerHTML = '';
            }

            function updateStatus(message, type) {
                const statusDiv = document.getElementById('status');
                statusDiv.textContent = message;
                statusDiv.className = `status ${type}`;
            }

            // 页面加载完成后的初始化
            window.addEventListener('load', function () {
                log('测试环境加载完成');
                updateStatus('测试环境已准备就绪，点击按钮开始测试', 'success');

                // 等待 iframe 加载完成后尝试初始化
                const iframe = document.getElementById('pluginFrame');
                iframe.addEventListener('load', function () {
                    log('插件 iframe 加载完成');

                    // 等待 iframe 中的脚本加载完成
                    setTimeout(() => {
                        log('开始检查 iframe 中的插件对象...');
                        const iframeWindow = iframe.contentWindow;

                        if (iframeWindow) {
                            log('iframe window 可访问');

                            // 检查是否有 Asc 对象
                            if (iframeWindow.Asc) {
                                log('iframe 中找到 Asc 对象');
                                if (iframeWindow.Asc.plugin) {
                                    log('iframe 中找到 Asc.plugin 对象');
                                    if (iframeWindow.Asc.plugin.init) {
                                        log('iframe 中找到 init 函数，准备调用...');
                                    } else {
                                        log('iframe 中没有 init 函数');
                                    }
                                } else {
                                    log('iframe 中没有 Asc.plugin 对象');
                                }
                            } else {
                                log('iframe 中没有 Asc 对象');
                            }
                        } else {
                            log('无法访问 iframe window');
                        }

                        testPluginInit();
                    }, 2000); // 增加等待时间
                });
            });

            // 监听来自 iframe 的消息
            window.addEventListener('message', function (event) {
                log('收到来自插件的消息: ' + event.data);
            });
        </script>
    </body>
</html>
