<template>
  <div ref="dom" class="charts chart-bar" :style="{height: height}"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'ChartBar',
  props: {
    option: Object,
    height: String
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    resize () {
      this.dom.resize()
    },
    handleClick(param) {
      this.$emit('onClick', param)
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.dom = echarts.init(this.$refs.dom)
      this.dom.showLoading()
      this.dom.setOption(this.option)
      this.dom.hideLoading()
    })
  },
  
}
</script>
<style lang="scss" scoped>
.chart-bar{
  width: 100%;
  height: 300px;
}
</style>
