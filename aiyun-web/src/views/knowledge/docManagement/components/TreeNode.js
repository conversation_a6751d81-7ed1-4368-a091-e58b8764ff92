import { Rect as GRect, Text as GText } from '@antv/g';
import {
  Badge,
  CommonEvent,
  ExtensionCategory,
  Graph,
  GraphEvent,
  iconfont,
  Label,
  Rect,
  register,
  treeToGraphData,
} from '@antv/g6';

const COLORS = {
    B: '#1783FF',
    R: '#F46649',
    Y: '#DB9D0D',
    G: '#60C42D',
    DI: '#A7A7A7',
};
const GREY_COLOR = '#CED4D9';

export default class TreeNode extends Rect {
    get data() {
      return this.context.model.getNodeLikeDatum(this.id);
    }
  
    get childrenData() {
      return this.context.model.getChildrenData(this.id);
    }
  
    getCollapseStyle(attributes) {
        if (this.childrenData.length === 0 || this.data.level < 3 ) return false;
        const { collapsed } = attributes;
        const [width, height] = this.getSize(attributes);
        return {
          backgroundFill: '#fff',
          backgroundHeight: 16,
          backgroundLineWidth: 1,
          backgroundRadius: 0,
          backgroundStroke: GREY_COLOR,
          backgroundWidth: 16,
          cursor: 'pointer',
          fill: GREY_COLOR,
          fontSize: 16,
          text: collapsed ? '+' : '-',
          textAlign: 'center',
          textBaseline: 'middle',
          x: width / 2,
          y: 0,
        };
      }
    
      drawCollapseShape(attributes, container) {
        const collapseStyle = this.getCollapseStyle(attributes);
        const btn = this.upsert('collapse', Badge, collapseStyle, container);
    
        if (btn && !Reflect.has(btn, '__bind__')) {
          Reflect.set(btn, '__bind__', true);
          btn.addEventListener(CommonEvent.CLICK, () => {
            const { collapsed } = this.attributes;
            const graph = this.context.graph;
            if (collapsed) graph.expandElement(this.id);
            else graph.collapseElement(this.id);
          });
        }
      }
  
    render(attributes = this.parsedAttributes, container) {
      super.render(attributes, container);
      this.drawCollapseShape(attributes, container);
    }
  }