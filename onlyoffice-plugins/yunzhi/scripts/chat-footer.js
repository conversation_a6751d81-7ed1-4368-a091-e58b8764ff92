// Chat footer functionality - replicated from Vue footer behavior
(function () {
    function onReady(fn) {
        if (document.readyState !== 'loading') {
            fn();
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    }

    onReady(function () {
        var userInput = document.getElementById('userInput');
        var sendBtn = document.getElementById('send-button');
        var stopBtn = document.getElementById('stop-button');
        var regenBtn = document.getElementById('regenerate-button');
        var agentModelBtn = document.getElementById('agent-model-btn');
        var agentRagBtn = document.getElementById('agent-rag-btn');

        var chooseFileBtn = document.getElementById('choose-file-btn');
        var fileDialogOverlay = document.getElementById('file-dialog-overlay');
        var fileInput = document.getElementById('file-input');
        var fileConfirmBtn = document.getElementById('file-confirm-btn');
        var fileCancelBtn = document.getElementById('file-cancel-btn');
        var fileCloseBtn = document.getElementById('file-close-btn');
        var selectedFilesEl = document.getElementById('selected-files');
        var fileListEl = document.getElementById('file-list');
        var clearFilesBtn = document.getElementById('clear-files-btn');

        var fileData = [];
        var showFileList = true;
        var isComposing = false;
        var chooseFileLoading = false;
        var conversationInProgress = false;
        var currentTab = 'knowledge';
        var selectedKnowledgeFiles = [];
        var selectedLocalFiles = [];
        var currentPath = [{ id: '', fileName: '根目录' }];
        var isShowKL = true; // 控制是否显示知识库列表

        function renderFileList() {
            if (fileData.length && showFileList) {
                selectedFilesEl.style.display = '';
                fileListEl.innerHTML = '';
                fileData.forEach(function (f, idx) {
                    var item = document.createElement('div');
                    item.className = 'file-item';
                    var tag = document.createElement('span');
                    tag.className = 'file-tag';
                    tag.textContent = f.fileName || f.name;
                    var close = document.createElement('button');
                    close.type = 'button';
                    close.className = 'file-tag-close';
                    close.innerHTML = '\u00D7';
                    close.addEventListener('click', function () {
                        fileData.splice(idx, 1);
                        renderFileList();
                    });
                    item.appendChild(tag);
                    item.appendChild(close);
                    fileListEl.appendChild(item);
                });
            } else {
                selectedFilesEl.style.display = 'none';
            }
        }

        if (clearFilesBtn)
            clearFilesBtn.addEventListener('click', function () {
                fileData = [];
                renderFileList();
            });

        // 文件选择对话框功能
        if (chooseFileBtn)
            chooseFileBtn.addEventListener('click', function () {
                if (chooseFileBtn.disabled || chooseFileLoading) return;
                openFileDialog();
            });

        function openFileDialog() {
            selectedKnowledgeFiles = [];
            selectedLocalFiles = [];
            currentTab = 'knowledge';
            currentPath = [{ id: '', fileName: '根目录' }];
            isShowKL = true;
            updateTabDisplay();
            renderKnowledgeList();
            fileDialogOverlay.style.display = 'flex';
        }

        function closeFileDialog() {
            fileDialogOverlay.style.display = 'none';
            fileInput.value = '';
        }

        if (fileCloseBtn) fileCloseBtn.addEventListener('click', closeFileDialog);
        if (fileCancelBtn) fileCancelBtn.addEventListener('click', closeFileDialog);

        if (fileConfirmBtn)
            fileConfirmBtn.addEventListener('click', function () {
                var allSelectedFiles = [];

                // 合并知识库和本地文件
                selectedKnowledgeFiles.forEach(function (file) {
                    allSelectedFiles.push({
                        fileName: file.fileName,
                        name: file.fileName,
                        id: file.id,
                        type: 'knowledge',
                        ...file,
                    });
                });

                selectedLocalFiles.forEach(function (file) {
                    // 只包含已解析且被选中的文件
                    if (file.parseStatus === 'done' && file.selected !== false) {
                        allSelectedFiles.push({
                            fileName: file.name,
                            name: file.name,
                            size: file.size,
                            type: 'local',
                            ...file,
                        });
                    }
                });

                if (allSelectedFiles.length > 5) {
                    alert('最多选择5个文件');
                    return;
                }

                // 更新文件数据
                fileData = allSelectedFiles;
                renderFileList();
                closeFileDialog();
            });

        // 标签页切换
        function updateTabDisplay() {
            var tabBtns = document.querySelectorAll('.tab-btn');
            var knowledgeTab = document.getElementById('knowledge-tab');
            var localTab = document.getElementById('local-tab');

            tabBtns.forEach(function (btn) {
                btn.classList.toggle('active', btn.getAttribute('data-tab') === currentTab);
            });

            knowledgeTab.style.display = currentTab === 'knowledge' ? 'block' : 'none';
            localTab.style.display = currentTab === 'local' ? 'block' : 'none';
        }

        // 绑定标签页点击事件
        document.addEventListener('click', function (e) {
            if (e.target.classList.contains('tab-btn')) {
                currentTab = e.target.getAttribute('data-tab');
                updateTabDisplay();
                if (currentTab === 'local') {
                    renderLocalFileList();
                }
            }
        });

        // 渲染知识库列表
        function renderKnowledgeList() {
            var klContent = document.getElementById('kl-content');
            var fileBrowserContent = document.getElementById('file-browser-content');
            var knowledgeGrid = document.getElementById('knowledge-grid');

            if (isShowKL) {
                // 显示知识库卡片网格
                klContent.style.display = 'block';
                fileBrowserContent.style.display = 'none';

                // 模拟知识库数据
                var mockKnowledgeBases = [
                    { id: 'kb1', name: '技术文档库', createTime: '2024-01-01' },
                    { id: 'kb2', name: '产品手册库', createTime: '2024-01-02' },
                    { id: 'kb3', name: '用户指南库', createTime: '2024-01-03' },
                ];

                knowledgeGrid.innerHTML = '';
                mockKnowledgeBases.forEach(function (kb) {
                    var item = document.createElement('div');
                    item.className = 'knowledge-item';
                    item.setAttribute('data-id', kb.id);
                    item.innerHTML = '<div class="knowledge-header">' + '<span class="knowledge-icon">👤</span>' + '<span></span>' + '</div>' + '<div class="knowledge-title">' + kb.name + '</div>' + '<div class="knowledge-footer">' + '<p>🕐 ' + kb.createTime + '</p>' + '</div>';

                    item.addEventListener('click', function () {
                        isShowKL = false;
                        currentPath = [
                            { id: '', fileName: '根目录' },
                            { id: kb.id, fileName: kb.name },
                        ];
                        showFileBrowser(kb.id);
                    });

                    knowledgeGrid.appendChild(item);
                });
            } else {
                // 显示文件浏览器
                klContent.style.display = 'none';
                fileBrowserContent.style.display = 'block';
            }
        }

        // 显示文件浏览器
        function showFileBrowser(kbId) {
            var breadcrumb = document.getElementById('breadcrumb');
            var backBtn = document.getElementById('back-btn');
            var breadcrumbDivider = document.getElementById('breadcrumb-divider');

            // 更新面包屑 - 支持点击
            breadcrumb.innerHTML = '';
            currentPath.forEach(function (pathItem, index) {
                var span = document.createElement('span');
                span.className = 'breadcrumb-item';
                span.textContent = pathItem.fileName;
                span.setAttribute('data-index', index);

                // 添加点击事件
                span.addEventListener('click', function () {
                    handleBreadcrumbClick(index);
                });

                breadcrumb.appendChild(span);
            });

            // 显示/隐藏返回按钮和分隔符
            var showBackBtn = currentPath.length > 2;
            backBtn.style.display = showBackBtn ? 'inline-block' : 'none';
            breadcrumbDivider.style.display = showBackBtn ? 'inline' : 'none';

            // 渲染文件列表
            renderKnowledgeFileList(kbId);

            // 确保显示文件浏览器
            renderKnowledgeList();
        }

        // 处理面包屑点击
        function handleBreadcrumbClick(index) {
            if (index === 0) {
                // 点击根目录，返回知识库列表
                currentPath = [{ id: '', fileName: '根目录' }];
                isShowKL = true;
                renderKnowledgeList();
            } else {
                // 点击其他路径项，截取路径并加载对应内容
                currentPath = currentPath.slice(0, index + 1);
                var targetPath = currentPath[index];
                if (targetPath.id) {
                    showFileBrowser(targetPath.id);
                } else {
                    isShowKL = true;
                    renderKnowledgeList();
                }
            }
        }

        // 返回上一级按钮
        document.addEventListener('click', function (e) {
            if (e.target.id === 'back-btn') {
                handleGoBack();
            }
        });

        // 返回上一级处理函数
        function handleGoBack() {
            if (currentPath.length > 2) {
                // 返回到上一级文件夹
                currentPath.pop();
                var parentPath = currentPath[currentPath.length - 1];
                showFileBrowser(parentPath.id);
            } else if (currentPath.length === 2) {
                // 返回到知识库列表
                currentPath = [{ id: '', fileName: '根目录' }];
                isShowKL = true;
                renderKnowledgeList();
            }
        }

        // 渲染知识库文件列表
        function renderKnowledgeFileList(kbId) {
            var tbody = document.getElementById('kb-file-tbody');

            // 模拟文件数据
            var mockFiles = [
                { id: 'f1', fileName: '国际通用的主要能源品种标准煤折算系数.pdf', filePrefix: true, type: 'file' },
                { id: 'f2', fileName: '12312.docx', filePrefix: true, type: 'file' },
                { id: 'f3', fileName: '常用法规', filePrefix: false, type: 'folder' },
                { id: 'f4', fileName: '国务院', filePrefix: false, type: 'folder' },
                { id: 'f5', fileName: '工信部', filePrefix: false, type: 'folder' },
                { id: 'f6', fileName: '国资委', filePrefix: false, type: 'folder' },
                { id: 'f7', fileName: '其它部委', filePrefix: false, type: 'folder' },
            ];

            tbody.innerHTML = '';
            mockFiles.forEach(function (file) {
                var row = document.createElement('tr');

                // 文件名列
                var nameCell = document.createElement('td');
                nameCell.className = 'file-name-cell';

                var nameContent = document.createElement('div');
                nameContent.className = 'file-name-content';

                // 图标
                var iconSpan = document.createElement('span');
                iconSpan.className = 'file-icon';
                if (file.filePrefix) {
                    iconSpan.innerHTML = '📄'; // 文档图标
                } else {
                    iconSpan.innerHTML = '📁'; // 文件夹图标
                }

                // 文件名文本
                var textSpan = document.createElement('span');
                textSpan.className = 'file-text';
                textSpan.textContent = file.fileName;
                textSpan.title = file.fileName; // 添加 tooltip 效果

                nameContent.appendChild(iconSpan);
                nameContent.appendChild(textSpan);
                nameCell.appendChild(nameContent);
                row.appendChild(nameCell);

                // 操作列
                if (file.filePrefix) {
                    // 文件：显示复选框
                    var actionCell = document.createElement('td');
                    actionCell.className = 'action-cell';

                    var checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.addEventListener('change', function () {
                        if (this.checked) {
                            selectedKnowledgeFiles.push(file);
                        } else {
                            selectedKnowledgeFiles = selectedKnowledgeFiles.filter(function (f) {
                                return f.id !== file.id;
                            });
                        }
                    });

                    actionCell.appendChild(checkbox);
                    row.appendChild(actionCell);
                } else {
                    // 文件夹：空的操作列，整行可点击
                    var actionCell = document.createElement('td');
                    actionCell.className = 'action-cell';
                    row.appendChild(actionCell);

                    row.style.cursor = 'pointer';
                    row.addEventListener('click', function () {
                        currentPath.push({ id: file.id, fileName: file.fileName });
                        showFileBrowser(file.id);
                    });
                }

                tbody.appendChild(row);
            });
        }

        // 本地文件上传功能
        var uploadBtn = document.getElementById('upload-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', function () {
                fileInput.click();
            });
        }

        // 解析状态映射（与Vue版本保持一致）
        var RunningStatusMap = {
            unstart: {
                label: 'UNSTART',
                color: 'cyan',
                name: '未启动',
                type: 'info',
            },
            running: {
                label: 'RUNNING',
                color: 'blue',
                name: '解析中',
                type: 'primary',
            },
            done: {
                label: 'SUCCESS',
                color: 'geekblue',
                name: '成功',
                type: 'success',
            },
            fail: {
                label: 'FAIL',
                color: 'red',
                name: '失败',
                type: 'danger',
            },
        };

        // 创建状态标签
        function createStatusTag(status) {
            var statusInfo = RunningStatusMap[status] || RunningStatusMap['unstart'];
            var tag = document.createElement('span');
            tag.className = 'status-tag status-tag-' + statusInfo.type;
            tag.textContent = statusInfo.name;
            return tag;
        }

        // 渲染本地文件表格
        function renderLocalFileList() {
            var tbody = document.getElementById('local-file-tbody');
            var table = document.getElementById('local-file-table');
            var emptyState = document.getElementById('local-empty-state');
            var uploadBtnText = document.getElementById('upload-btn-text');

            tbody.innerHTML = '';

            if (selectedLocalFiles.length === 0) {
                // 表格始终显示，只显示空状态提示
                emptyState.style.display = 'block';
                return;
            }

            // 有数据时隐藏空状态
            emptyState.style.display = 'none';

            selectedLocalFiles.forEach(function (file, index) {
                var row = document.createElement('tr');

                // 名称列
                var nameCell = document.createElement('td');
                nameCell.innerHTML = '<span style="margin-right: 8px;">📄</span>' + file.name;
                row.appendChild(nameCell);

                // 解析状态列
                var statusCell = document.createElement('td');
                statusCell.style.textAlign = 'center';
                var statusTag = createStatusTag(file.parseStatus || 'done');
                statusCell.appendChild(statusTag);
                row.appendChild(statusCell);

                // 操作列
                var actionCell = document.createElement('td');
                actionCell.style.textAlign = 'center';

                if (file.parseStatus === 'running') {
                    // 解析中显示加载状态
                    var loadingSpan = document.createElement('span');
                    loadingSpan.innerHTML = '⏳';
                    loadingSpan.title = '解析中...';
                    loadingSpan.style.color = '#409eff';
                    actionCell.appendChild(loadingSpan);
                } else if (file.parseStatus === 'done') {
                    // 解析完成显示复选框（与Vue版本一致的样式）
                    var checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.className = 'file-checkbox';
                    checkbox.checked = file.selected === true; // 不默认勾选，只有明确设置为true才勾选
                    checkbox.addEventListener('change', function () {
                        file.selected = this.checked;
                        // 更新选中状态，不需要从列表中移除，因为文件始终在列表中
                        console.log('文件选择状态更新:', file.name, '选中:', this.checked);
                    });
                    actionCell.appendChild(checkbox);
                } else if (file.parseStatus === 'fail') {
                    // 解析失败显示重试按钮
                    var retryBtn = document.createElement('button');
                    retryBtn.className = 'action-btn retry-btn';
                    retryBtn.innerHTML = '🔄';
                    retryBtn.title = '重新解析';
                    retryBtn.addEventListener('click', function () {
                        file.parseStatus = 'running';
                        renderLocalFileList();
                        startAutoParseFile(file);
                    });
                    actionCell.appendChild(retryBtn);
                }

                row.appendChild(actionCell);
                tbody.appendChild(row);
            });

            // 更新上传按钮状态
            if (uploadBtnText) {
                uploadBtnText.textContent = chooseFileLoading ? '上传中...' : '选择文件';
            }
        }

        function getStatusText(status) {
            var statusMap = {
                done: '已解析',
                unstart: '未开始',
                running: '解析中',
                error: '解析失败',
            };
            return statusMap[status] || '未知';
        }

        // 点击对话框外部关闭
        if (fileDialogOverlay) {
            fileDialogOverlay.addEventListener('click', function (e) {
                if (e.target === fileDialogOverlay) {
                    closeFileDialog();
                }
            });
        }
        if (fileInput)
            fileInput.addEventListener('change', function (e) {
                var files = Array.prototype.slice.call(e.target.files || []);
                if (!files.length) return;

                // 检查文件大小限制
                for (var i = 0; i < files.length; i++) {
                    if (files[i].size > 10 * 1024 * 1024) {
                        // 10MB
                        alert('文件 "' + files[i].name + '" 大小超过 10MB 限制');
                        fileInput.value = '';
                        return;
                    }
                }

                chooseFileLoading = true;
                updateFileButtonState();

                // 模拟文件上传处理
                setTimeout(function () {
                    var newFiles = Array.prototype.slice.call(files).map(function (f) {
                        return {
                            ...f,
                            parseStatus: 'running', // 上传成功后自动开始解析
                            selected: false, // 解析成功后不默认勾选，需要用户手动选择
                            id: 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                        };
                    });

                    // 过滤重复文件
                    var newOnes = newFiles.filter(function (f) {
                        return !selectedLocalFiles.some(function (ef) {
                            return ef.name === f.name && ef.size === f.size;
                        });
                    });

                    if (newOnes.length !== newFiles.length) {
                        console.warn('部分文件重复，已过滤');
                    }

                    selectedLocalFiles = selectedLocalFiles.concat(newOnes);

                    // 自动开始解析新上传的文件
                    newOnes.forEach(function (file) {
                        startAutoParseFile(file);
                    });

                    // 如果当前在本地文件标签页，更新显示
                    if (currentTab === 'local') {
                        renderLocalFileList();
                    }

                    fileInput.value = '';
                    chooseFileLoading = false;
                    updateFileButtonState();
                }, 500);
            });

        // 自动解析文件函数（模拟Vue版本的自动解析流程）
        function startAutoParseFile(file) {
            // 清除之前的定时器（如果有）
            if (file.parseTimer) {
                clearTimeout(file.parseTimer);
            }

            // 模拟解析过程，类似Vue版本的定时器轮询
            var parseTime = 2000 + Math.random() * 3000; // 2-5秒随机解析时间
            var parseTimer = setTimeout(function () {
                // 模拟解析结果（90%成功率）
                var success = Math.random() > 0.1;
                file.parseStatus = success ? 'done' : 'fail';

                // 更新显示
                if (currentTab === 'local') {
                    renderLocalFileList();
                }

                console.log('文件解析' + (success ? '完成' : '失败') + ':', file.name);
            }, parseTime);

            // 存储定时器ID，用于取消解析
            file.parseTimer = parseTimer;
        }

        function updateFileButtonState() {
            if (chooseFileBtn) {
                chooseFileBtn.disabled = chooseFileLoading;
                chooseFileBtn.textContent = chooseFileLoading ? '加载中...' : '☁';
            }
        }

        function setBusy(busy) {
            conversationInProgress = busy;

            if (chooseFileBtn) chooseFileBtn.disabled = busy || chooseFileLoading;
            if (agentModelBtn) agentModelBtn.disabled = busy;
            if (agentRagBtn) agentRagBtn.disabled = busy;
            updateSendButtonState();
        }

        function updateSendButtonState() {
            if (sendBtn) {
                var hasContent = userInput && userInput.value.trim() !== '';
                // 根据内容和对话状态设置按钮样式
                sendBtn.className = sendBtn.className.replace(/\b(primary|info|disabled)\b/g, '');
                if (conversationInProgress) {
                    sendBtn.style.display = 'none';
                    if (stopBtn) stopBtn.style.display = 'block';
                } else {
                    sendBtn.style.display = 'block';
                    if (stopBtn) stopBtn.style.display = 'none';
                    sendBtn.className += hasContent ? ' primary' : ' info';
                    sendBtn.disabled = !hasContent;
                }
            }
        }

        function preSend() {
            showFileList = false;
            renderFileList();
            setBusy(true);
        }

        if (sendBtn)
            sendBtn.addEventListener('click', function () {
                if (userInput && userInput.value.trim() === '') return;
                if (conversationInProgress) return;
                preSend();
            });

        if (userInput) {
            userInput.addEventListener(
                'compositionstart',
                function () {
                    isComposing = true;
                },
                true
            );
            userInput.addEventListener(
                'compositionend',
                function () {
                    setTimeout(function () {
                        isComposing = false;
                    }, 200);
                },
                true
            );
            userInput.addEventListener(
                'keydown',
                function (e) {
                    if (e.key === 'Enter' && isComposing) {
                        e.stopPropagation();
                        e.preventDefault();
                    }
                },
                true
            );
            userInput.addEventListener('keydown', function (e) {
                if (e.key === 'Enter' && !e.shiftKey && !isComposing && !conversationInProgress) {
                    preSend();
                }
            });
            function refreshSendEnabled() {
                updateSendButtonState();
            }
            userInput.addEventListener('input', refreshSendEnabled);
            refreshSendEnabled();
        }

        // Agent mode management (智能体/知识库)
        window.chatFooterAgentMode = '1'; // 1=智能体, 2=知识库 - exposed for main.js access
        function updateAgentButtons() {
            if (agentModelBtn) agentModelBtn.classList.toggle('active', window.chatFooterAgentMode === '1');
            if (agentRagBtn) agentRagBtn.classList.toggle('active', window.chatFooterAgentMode === '2');
        }
        if (agentModelBtn)
            agentModelBtn.addEventListener('click', function () {
                window.chatFooterAgentMode = '1';
                updateAgentButtons();
            });
        if (agentRagBtn)
            agentRagBtn.addEventListener('click', function () {
                window.chatFooterAgentMode = '2';
                updateAgentButtons();
            });
        updateAgentButtons();

        if (stopBtn) {
            var obs = new MutationObserver(function () {
                var stopVisible = window.getComputedStyle(stopBtn).display !== 'none';
                if (stopVisible) {
                    setBusy(true);
                } else {
                    setBusy(false);
                    showFileList = true;
                    fileData = [];
                    renderFileList();
                }
            });
            obs.observe(stopBtn, { attributes: true, attributeFilter: ['style', 'class'] });
        }

        renderFileList();
        updateFileButtonState();
    });
})();
