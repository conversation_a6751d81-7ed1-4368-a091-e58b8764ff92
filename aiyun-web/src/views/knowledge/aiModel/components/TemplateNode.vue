<script setup lang="ts">
import { Handle, Position, useVueFlow } from "@vue-flow/core";
import { ref, markRaw } from 'vue';
import { ArrowUp, ArrowDown, Delete } from '@element-plus/icons-vue';
import { NodeToolbar } from '@vue-flow/node-toolbar';

const { updateNodeData, removeNodes } = useVueFlow();

const isExpanded = ref(true);

// 使用 markRaw 标记不需要响应式的组件
const Icons = {
  ArrowUp: markRaw(ArrowUp),
  ArrowDown: markRaw(ArrowDown),
  Delete: markRaw(Delete)
};

defineOptions({ name : 'TemplateNode' })

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const props = defineProps<{
  id: string;
  data: {
    label: string;
    template?: string;
    inputParams?: Array<{ name: string; type: string }>;
    outputParams?: Array<{ name: string; type: string }>;
    toolbarVisible?: boolean;
  }
}>();

const updateData = (key: string, value: any) => {
  updateNodeData(props.id, {
    ...props.data,
    [key]: value
  });
};

const addInputParam = () => {
  const inputParams = [...(props.data.inputParams || [])];
  inputParams.push({ name: '', type: 'string' });
  updateData('inputParams', inputParams);
};

const addOutputParam = () => {
  const outputParams = [...(props.data.outputParams || [])];
  outputParams.push({ name: '', type: 'string' });
  updateData('outputParams', outputParams);
};

const updateParam = (index: number, key: string, value: string, isInput: boolean) => {
  const params = isInput ? [...(props.data.inputParams || [])] : [...(props.data.outputParams || [])];
  params[index] = { ...params[index], [key]: value };
  updateData(isInput ? 'inputParams' : 'outputParams', params);
};

const removeParam = (index: number, isInput: boolean) => {
  const params = isInput ? [...(props.data.inputParams || [])] : [...(props.data.outputParams || [])];
  params.splice(index, 1);
  updateData(isInput ? 'inputParams' : 'outputParams', params);
};
</script>

<template>
  <div class="node-wrapper">
    <NodeToolbar
      :is-visible="data.toolbarVisible" 
      style="background-color: #fff;"
      :position="Position.Top"
      align="end"
      :offset="4"
    >
      <el-button @click.stop="removeNodes([props.id])">
        <el-icon><component :is="Icons.Delete" /></el-icon>
      </el-button>
    </NodeToolbar>
    <div class="template-node">
      <Handle
        type="target"
        :position="Position.Top"
        :id="`${props.id}-top`"
        class="horizontal-handle"
      />
      <Handle
        type="target"
        :position="Position.Left"
        :id="`${props.id}-left`"
        class="vertical-handle"
      />
      <div class="node-header">
        <span class="node-type">{{ data.label }}</span>
        <el-button
          type="primary"
          link
          @click="toggleExpand"
        >
          <el-icon>
            <component :is="isExpanded ? Icons.ArrowUp : Icons.ArrowDown" />
          </el-icon>
        </el-button>
      </div>
      <div v-show="isExpanded" class="node-content">
        <div class="form-item">
          <label>模板</label>
          <ElInput
            type="textarea"
            :model-value="data.template"
            @update:model-value="(val) => updateData('template', val)"
            :rows="4"
            placeholder="输入模板内容"
          />
        </div>
        <div class="params-section">
          <div class="params-header">
            <span>输入参数</span>
            <el-button type="primary" size="small" @click="addInputParam">添加</el-button>
          </div>
          <div v-for="(param, index) in data.inputParams" :key="index" class="param-item">
            <ElInput
              :model-value="param.name"
              @update:model-value="(val) => updateParam(index, 'name', val, true)"
              placeholder="参数名"
            />
            <ElSelect
              :model-value="param.type"
              @update:model-value="(val) => updateParam(index, 'type', val, true)"
              placeholder="类型"
            >
              <ElOption label="字符串" value="string" />
              <ElOption label="数字" value="number" />
              <ElOption label="布尔值" value="boolean" />
            </ElSelect>
            <el-button type="danger" size="small" @click="() => removeParam(index, true)">删除</el-button>
          </div>
        </div>
        <div class="params-section">
          <div class="params-header">
            <span>输出参数</span>
            <el-button type="primary" size="small" @click="addOutputParam">添加</el-button>
          </div>
          <div v-for="(param, index) in data.outputParams" :key="index" class="param-item">
            <ElInput
              :model-value="param.name"
              @update:model-value="(val) => updateParam(index, 'name', val, false)"
              placeholder="参数名"
            />
            <ElSelect
              :model-value="param.type"
              @update:model-value="(val) => updateParam(index, 'type', val, false)"
              placeholder="类型"
            >
              <ElOption label="字符串" value="string" />
              <ElOption label="数字" value="number" />
              <ElOption label="布尔值" value="boolean" />
            </ElSelect>
            <el-button type="danger" size="small" @click="() => removeParam(index, false)">删除</el-button>
          </div>
        </div>
      </div>
      <Handle
        type="source"
        :position="Position.Bottom"
        :id="`${props.id}-bottom`"
        class="horizontal-handle"
      />
      <Handle
        type="source"
        :position="Position.Right"
        :id="`${props.id}-right`"
        class="vertical-handle"
      />
    </div>
  </div>
</template>

<style scoped>
.node-wrapper {
  position: relative;
}

.template-node {
  width: 300px;
  background-color: white;
  border: 2px solid #e6a23c;
  border-radius: 8px;
  padding: 12px;
}

.node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.node-type {
  font-weight: bold;
  color: #e6a23c;
  text-align: left;
}

.node-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-item label {
  font-size: 14px;
  color: #606266;
}

.params-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.param-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.horizontal-handle {
  background-color: #409eff;
  width: 30px;
  height: 16px;
  border-radius: 5px;
}

.vertical-handle {
  background-color: #409eff;
  width: 16px;
  height: 30px;
  border-radius: 5px;
}

:deep(.el-button--text) {
  padding: 0;
  height: auto;
  color: #e6a23c;
}
</style> 