<script setup lang="ts">
import { Handle, Position, useVueFlow } from '@vue-flow/core';
import { NodeToolbar } from '@vue-flow/node-toolbar';
import { Delete, Share } from '@element-plus/icons-vue';
import { markRaw } from 'vue';

defineOptions({ name: 'ParallelNode' })

const { removeNodes } = useVueFlow();

const Icons = {
  Delete: markRaw(Delete),
  Share: markRaw(Share)
};

const props = defineProps<{
  id: string;
  data: {
    label: string;
    inputParams?: { name: string; type: string; key?: string; required?: boolean }[];
    outputParams?: { name: string; type: string }[];
    toolbarVisible?: boolean;
    parallelCount?: number;
  };
}>();

const emit = defineEmits(['updateNodeInternals']);
</script>

<template>
  <div class="node-wrapper">
    <NodeToolbar
      :is-visible="data.toolbarVisible"
      style="background-color: #fff;"
      :position="Position.Top"
      align="end"
      :offset="4"
    >
      <el-button
        @click.stop="removeNodes([props.id])"
      >
        <el-icon><component :is="Icons.Delete" /></el-icon>
      </el-button>
    </NodeToolbar>
    <div class="parallel-node">
      <Handle
        type="target"
        :position="Position.Top"
        :id="`${props.id}-top`"
        class="horizontal-handle"
      />
      <Handle
        type="target"
        :position="Position.Left"
        :id="`${props.id}-left`"
        class="vertical-handle"
      />
      <div class="node-content">
        <div class="node-header">
          <el-icon><component :is="Icons.Share" /></el-icon>
          <span class="node-type">{{ data.label }}</span>
        </div>
        <div class="node-body">
          <div class="parallel-lines">
            <div class="line" v-for="n in (data.parallelCount || 3)" :key="n"></div>
          </div>
        </div>
      </div>
      <Handle
        type="source"
        :position="Position.Bottom"
        :id="`${props.id}-bottom`"
        class="horizontal-handle"
      />
      <Handle
        type="source"
        :position="Position.Right"
        :id="`${props.id}-right`"
        class="vertical-handle"
      />
    </div>
  </div>
</template>

<style scoped>
.node-wrapper {
  position: relative;
}

.parallel-node {
  background: white;
  border: 2px solid #67c23a;
  border-radius: 8px;
  padding: 12px;
  min-width: 150px;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #67c23a;
  font-weight: bold;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.node-type {
  color: #67c23a;
  text-align: left;
}

.node-body {
  padding: 8px 0;
}

.parallel-lines {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 30px;
}

.line {
  width: 2px;
  height: 100%;
  background-color: #67c23a;
}

.horizontal-handle {
  background-color: #67c23a;
  width: 30px;
  height: 16px;
  border-radius: 5px;
}

.vertical-handle {
  background-color: #67c23a;
  width: 16px;
  height: 30px;
  border-radius: 5px;
}
</style> 