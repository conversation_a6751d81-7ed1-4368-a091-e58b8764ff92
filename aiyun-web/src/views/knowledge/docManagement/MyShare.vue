<template>
	<!-- 我的分享 -->
			<ContentWrap>
				<!-- 头部按钮和搜索框 -->
				<div class="header">
					<!-- 左侧按钮 -->
					<div>
					</div>
		
					<!-- 右侧搜索框和按钮 -->
					<div>
						<el-input v-model="queryParams.fileName" placeholder="请输入文件名" style="width: 200px; margin-right: 10px;" />
						<el-button type="primary" @click="handleSearch">搜索</el-button>
						<el-button @click="handleReset">重置</el-button>
					</div>
				</div>
				<el-tabs v-model="activeName" @tab-change="handleTabChange">
					<el-tab-pane label="我的分享" name="first" />
					<el-tab-pane label="分享给我" name="second" />
				</el-tabs>
		
				<!-- 文件列表 -->
				<el-table v-loading="tableLoading" :data="fileList">
					<el-table-column prop="klCloudRespVO.fileName" show-overflow-tooltip label="名称" />
					<el-table-column v-if="activeName === 'first'" prop="createTime" label="被分享人">
						<template #default="scope">
							{{ compareName(scope.row.shardTo) }}
						</template>
					</el-table-column>
					<el-table-column v-else prop="creator" label="分享人" />
					<el-table-column prop="createTime" label="分享时间" />
					<el-table-column prop="jurisdiction" label="权限" >
						<template #default="scope">
							<!-- 0只读1编辑2下载 -->
							{{ scope.row.jurisdiction	== 0 ? '只读' : scope.row.jurisdiction	== 1 ? '编辑' : '下载' }}
						</template>
					</el-table-column>
					<el-table-column prop="endTime" label="期限">
						<template #default="scope">
							{{ scope.row.endTime ? scope.row.endTime : '永久' }}
						</template>
					</el-table-column>
					<el-table-column prop="klCloudRespVO.createTime" label="创建时间" />
					<el-table-column prop="klCloudRespVO.creator" label="创建人" />
					<el-table-column prop="endTime" label="状态">
						<template #default="scope">
							<!-- 跟当前时间对比 -->
							 
							{{ compareTime(scope.row.endTime)  ? '正常' : '已过期' }}
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" width="260">
					<template #default="scope">
						<!-- 0只读1编辑2下载 -->
						<el-button
							link
							type="primary"
							v-if="activeName ==='first' || scope.row.jurisdiction == '1' || scope.row.jurisdiction =='2'"
							@click="handlePreview(scope.row)"
						>
							预览
						</el-button>
						<el-button
							link
							type="primary"
							v-if="activeName ==='first' || scope.row.jurisdiction == '1'"
							@click="handleEdit(scope.row.klCloudRespVO)"
						>
							编辑
						</el-button>
						<el-button
							link
							type="primary"
							v-if="activeName ==='first' || scope.row.jurisdiction == '1' || scope.row.jurisdiction == '2'"
							@click="handleDownload(scope.row.klCloudRespVO)"
						>
							下载
						</el-button>
						<el-button
							link
							type="primary"
							v-if="activeName ==='first'"
							@click="handleDel(scope.row)"
						>
							取消分享
						</el-button>
					</template>
				</el-table-column>
				</el-table>
		
				<Pagination
					:total="total"
					v-model:page="queryParams.pageNum"
					v-model:limit="queryParams.pageSize"
					@pagination="queryList"
				/>
			</ContentWrap>
			<DocView ref="docViewRef" @success="handleSearch" />
		</template>
		
		<script setup>
		import { ref, onMounted, computed } from 'vue';
		import { downloadFileFunction } from '@/utils/downloadFile'
		import * as FolderAPI from '@/api/knowledge/docmangement'
		import { useUserStore } from '@/store/modules/user'
		import DocView from './components/DocView.vue'
		
		const message = useMessage() // 消息弹窗
		const userStore = useUserStore()
	
		const tableLoading = ref(false);
		const activeName = ref('first');
		
		// 当前文件夹下的文件列表
		const fileList = ref([]);
		
		// 搜索查询
		const queryParams = ref({
			pageNum: 1,
			pageSize: 10,
			fileName: '',
		});
		const total = ref(0);

		const handleTabChange = (tab) => {
			if (tab === 'first') {
				handleSearch();
			} else {
				handleSearch();
			}
		}

		// 比较当前时间和传入时间
		const compareTime = (time) => {
			if(!time) {
				return true
			}
			const currentTime = new Date().getTime();
			const targetTime = new Date(time).getTime();
			return currentTime < targetTime ? true : false;
		}
		
		// 加载文件夹内容
		const loadFolderContent = async () => {
			console.log(activeName.value);
			tableLoading.value = true;
			const API = activeName.value === 'first' ? FolderAPI.shareMe : FolderAPI.shareWithMe;
			const response = await API({
				pageNum: queryParams.value.pageNum,
				pageSize: queryParams.value.pageSize,
				klCloudName: queryParams.value.fileName,
			});
			tableLoading.value = false;
			total.value = response.total;
			fileList.value = response.records;
		};

		const handleEdit = (row) => {
			if(!row.fileUrl) {
				message.warning('文件生成中， 请稍后')
				return
			} 
			
			docViewRef.value.open('update', {}, row, true, '我的分享');
		}

		const compareName = (arr) => {
			return arr.map(item => {
				return item.name
			}).join('、')
		}
		const handleDownload = async(row) => {
			console.log(row);
			const { fileUrl, fileName } = row
			if(!fileUrl) {
				message.warning('当前文件无法下载，请联系管理员')
				return
			}
			await downloadFileFunction(fileUrl, fileName)
			// window.open(row.fileUrl);
		};
		
		const queryList = () => {
			loadFolderContent();
		}
		
		const handleSearch = () => {
			queryParams.value.pageNum = 1;
			loadFolderContent();
		}
		
		const handleReset = () => {
			queryParams.value.fileName = ''
			queryParams.value.pageNum = 1;
			loadFolderContent();
		}
	
		const docViewRef = ref(null);
		const handlePreview = (row) => {
			docViewRef.value.open('view', {}, row.klCloudRespVO, true, '我的分享');
		}

		const handleDel = async (row) => {
			try {
				const response = await FolderAPI.deleteShareById(row.id)
				message.success('取消分享成功')
				handleSearch()
			} catch (error) {
				message.error('取消分享失败')
			}
		}
	
		// 初始化加载根目录
		onMounted(() => {
			handleSearch();
			// 开启定时器
			// setQueryInterval()
		});
		
		</script>
		
		<style scoped lang="scss">
		.header {
			display: flex;
			justify-content: space-between;
			margin-bottom: 20px;
		}
		
		.breadcrumb-wrapper {
			display: flex;
			height: 48px;
			align-items: center;
			margin-bottom: 10px;
			padding: 0 20px;
			.back-button {
				padding: 0;
				font-size: 14px;
			}
		}
		</style>