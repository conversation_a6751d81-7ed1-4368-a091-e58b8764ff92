<template>
  <el-container class="system-dashboard">
    <!-- 顶部信息 -->
    <el-header class="header">
      <div class="flex align-center">
        <ElAvatar :src="avatar" alt="" class="w-[calc(var(--logo-height)-25px)] rounded-[50%]" />
        <div class="ml-2">
          <h3>你好 管理员</h3>
          <p class="text-[13px]">祝你开心每一天！</p>
        </div>
      </div>
      <div class="stats">
        <el-statistic title="在线文档数" :value="klCloudcount" />
        <el-divider direction="vertical" />
        <el-statistic title="生成文档数" :value="klDoccount" />
        <el-divider direction="vertical" />
        <el-statistic title="对话数" :value="klModelSessioncount" />
      </div>
    </el-header>
    <!-- 主体内容 -->
    <el-main>
      <!-- 文档列表 -->
      <DocumentList />
      <!-- 近7日文档统计 -->
      <DocstatisticEcharts />
      <!-- 近7日系统访问 -->
      <SystemAccessEcharts />
    </el-main>
  </el-container>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import avatarImg from '@/assets/imgs/logo_1.png'
import DocstatisticEcharts from './components/DocstatisticEcharts.vue'
import SystemAccessEcharts from './components/SystemAccessEcharts.vue'
import DocumentList from './components/DocumentList.vue'
import * as homeApi from '@/api/home/<USER>'

const userStore = useUserStore()
const avatar = computed(() => userStore.user.avatar || avatarImg)
const klCloudcount = ref(0)
const klDoccount = ref(0)
const klModelSessioncount = ref(0)
const list = ref([])

const getHomeCount = async () => {
  const res = await homeApi.getHomeCount()
  console.log('res', res);
  klCloudcount.value = res.klCloudcount
  klDoccount.value = res.klDoccount
  klModelSessioncount.value = res.klModelSessioncount
}

onMounted(() => {
  getHomeCount()
})
</script>

<style lang="scss">
.system-dashboard {
  background-color: #f5f7fa;
  min-height: 100vh;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
  }

  .stats {
    display: flex;
    gap: 10px;
    .el-statistic__content {
      text-align: center;
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .more-btn {
    float: right;
    padding: 0;
  }

  .doc-section {
    margin-bottom: 20px;
  }

  .tips {
    color: #909399;
    font-size: 12px;
    margin-top: 10px;
  }

  .chart-section {
    margin-top: 20px;
  }
}
</style>
