<template>
  <Dialog
    v-model="dialogVisible"
    :scroll="true"
    :width="700"
    :before-close="beforeClose"
    title="选择文件"
  >
    <div class="content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="知识库" name="first" />
        <el-tab-pane label="本地文件" name="second" />
      </el-tabs>
      <div class="knowlage-wrapper" v-show="activeName === 'first'">
        <div class="kl-content" v-if="isShowKL">
          <el-row :gutter="12">
            <el-col :span="8" v-for="(item, index) in knowledgeList" :key="index">
              <div class="knowledge-list" @click.stop="handleClickKnowledge(item)">
                <div class="list-header">
                  <span>
                    <Icon icon="fa:user-o" :size="24" />
                  </span>
                  <span></span>
                </div>
                <div class="title">
                  <el-tooltip :content="item.name" placement="top-start">
                    <p class="ellipsiss">
                      {{ item.name }}
                    </p>
                  </el-tooltip>
                </div>
                <div class="footer">
                  <p><Icon icon="ep:clock" class="mr-4px" />{{ item.createTime }}</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="content" v-else>
          <div class="breadcrumb-wrapper">
            <!-- 返回上一级按钮 -->
            <el-button
              v-if="path.length > 2"
              @click="handleGoBack"
              type="primary"
              link
              class="back-button"
            >
              返回上一级
            </el-button>
            <el-divider direction="vertical" :style="computedStyle" />
            <!-- 路径导航 -->
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="(item, index) in path"
                :key="index"
                @click="handleBreadcrumbClick(index)"
              >
                <span style="cursor: pointer">{{ item.fileName }}</span>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <!-- 文件列表 -->
          <div>
            <div
              class="file-item"
              @click="handleNameClick(item)"
              v-for="(item, index) in fileListFilter"
              :key="index"
            >
              <span class="file-name">
                <!-- {{ item.fileName }} -->
                <div class="flex align-center" v-if="item.filePrefix">
                  <span>
                    <el-icon class="mr-[4px]">
                      <Document />
                    </el-icon>
                  </span>
                  <el-tooltip :content="item.fileName" placement="top">
                    <span class="ellipsis">
                      {{ item.fileName }}
                    </span>
                  </el-tooltip>
                </div>
                <div class="flex align-center" v-else>
                  <span>
                    <el-icon class="mr-[4px]">
                      <FolderOpened />
                    </el-icon>
                  </span>
                  <span class="ellipsis">
                    <el-tooltip :content="item.fileName" placement="top">
                      <span>
                        {{ item.fileName }}
                      </span>
                    </el-tooltip>
                  </span>
                </div>
              </span>

              <!-- <el-tooltip content="选择文件"> -->
              <!-- <el-button 
                    v-if="item.filePrefix && !item.choose" 
                    class="choose-button" 
                    type="primary"
                    @click="handleChooseKnowlage(item)"
                    link>
                    <Icon icon="ep:document-checked" />
                  </el-button> -->
              <el-checkbox
                v-if="item.filePrefix"
                v-model="item.choose"
                @change="handleChooseFile(item)"
                size="large"
              />
              <!-- </el-tooltip> -->
              <!-- <el-tooltip content="取消选择">
                  <el-button 
                    v-if="item.filePrefix && item.choose" 
                    class="choose-button" 
                    type="primary"
                    @click="handleKnowlageCancel(item)"
                    link>
                    <Icon icon="ep:document-delete" />
                  </el-button>
                </el-tooltip> -->
            </div>
          </div>
        </div>
      </div>
      <div v-if="activeName === 'second'">
        <div style="text-align: right">
          <el-space>
            <el-upload
              ref="uploadRef"
              :action="uploadUrl"
              :auto-upload="true"
              style="text-align: right"
              :on-success="submitFormSuccess"
              :before-upload="handleFileSelect"
              :headers="uploadHeaders"
              :show-file-list="false"
              accept=".doc, .docx, .xls, .xlsx, .ppt, .pptx, .pdf, .txt, .wps"
              :disabled="uploadLoading"
            >
              <el-button type="primary" :loading="uploadLoading">
                {{ uploadLoading ? '上传中...' : '选择文件' }}
              </el-button>
            </el-upload>
            <!-- <el-button type="primary" @click="getDocumentList">刷新</el-button> -->
          </el-space>
        </div>
        <el-table :data="documentListFilter">
          <el-table-column label="名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="解析状态" align="center" prop="run" width="250px">
            <template #default="scope">
              <div class="run-document">
                <el-popover placement="top" :width="400">
                  <template #reference>
                    <el-tag :type="RunningStatusMap[scope.row.run].type">{{
                      RunningStatusMap[scope.row.run].name
                    }}</el-tag>
                  </template>
                  <p>解析结果：{{ scope.row.progress_msg ? scope.row.progress_msg : '-' }}</p>
                </el-popover>
                <!-- <el-tag :type="RunningStatusMap[scope.row.run].type">{{RunningStatusMap[scope.row.run].name}}</el-tag> -->
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center">
            <template #default="{ row }">
              <el-tooltip content="解析">
                <el-button
                  class="choose-button"
                  type="primary"
                  v-if="row.run === 'UNSTART'"
                  @click="handleRunDocumentByIds(row.id)"
                  link
                >
                  <Icon icon="ep:refresh" />
                </el-button>
              </el-tooltip>
              <el-checkbox
                v-if="row.run === 'DONE'"
                v-model="row.choose"
                @change="chooseDoc(row)"
                size="large"
              />
              <!-- <el-tooltip content="选择文件">
                <el-button 
                  v-if="!row.choose && row.run === 'DONE'" 
                  class="choose-button" 
                  type="primary"
                  @click="handleChooseDocument(row)"
                  link>
                  <Icon icon="ep:document-checked" />
                </el-button>
              </el-tooltip> -->
              <!-- <el-tooltip content="取消选择">
                <el-button 
                  v-if="row.choose && row.run === 'DONE'" 
                  class="choose-button" 
                  type="primary"
                  @click="handleDocumentCancel(row)"
                  link>
                  <Icon icon="ep:document-delete" />
                </el-button>
              </el-tooltip> -->
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getDocumentList"
        />
      </div>
    </div>
    <template #footer>
      <el-button type="primary" @click="submitForm">确认</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as FolderAPI from '@/api/knowledge/docmangement'
import { uploadFolderFile } from '@/api/knowledge/docmangement'
import { useUserStore } from '@/store/modules/user'
import { getKlDataset } from '@/api/knowledge/docmangement'
import axios from 'axios'
import { computed } from 'vue'
import { FolderOpened, Document } from '@element-plus/icons-vue'

const userStore = useUserStore()
const apikeys = userStore.getAppConfig

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'MoveDialog' })

const emit = defineEmits(['success'])

const props = defineProps({
  limit: { type: Number, required: false },
  localDocName: { type: String, required: false, default: 'documentChooseList' },
  localKnowName: { type: String, required: false, default: 'knowledgeChooseList' }
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const uploadUrl = `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_temporary_data}/documents`
const uploadHeaders = {
  Authorization: `${apikeys.ragflow_token}`
}

const RunningStatusMap = {
  UNSTART: {
    label: 'UNSTART',
    color: 'cyan',
    name: '未启动',
    type: 'info'
  },
  RUNNING: {
    label: 'RUNNING',
    color: 'blue',
    name: '解析中',
    type: 'primary'
  },
  CANCEL: { label: 'CANCEL', color: 'orange', name: '取消', type: 'warning' },
  DONE: { label: 'SUCCESS', color: 'geekblue', name: '成功', type: 'success' },
  FAIL: { label: 'FAIL', color: 'red', name: '失败', type: 'danger' }
}

const total = ref(0)

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const dialogType = ref('')
// 当前路径
const path = ref([{ id: '', fileName: '知识库', level: '1' }])
// 当前文件夹下的文件列表
const fileList = ref([])
const knowledgeList = ref([])
const localKnowledgeList = ref([]) // 点击确认时保存的知识库选中集合
const localDocumentList = ref([]) // 点击确认时保存的本地文件选中集合
const isShowKL = ref(true)
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})
const documentList = ref([])

const activeName = ref('first')
// 加载文件夹内容
const loadFolderContent = async (row) => {
  // fileList.value = fakeData[folderId] || [];
  const { id, level, datasetId } = row
  const response = await FolderAPI.getCloudList({
    parentId: id,
    datasetId,
    level: id ? parseInt(level) + 1 : level,
    analysisStatus: '2'
  })
  fileList.value = response.map((item) => {
    return {
      ...item,
      choose: false
    }
  })
}

const handleClickKnowledge = (item) => {
  isShowKL.value = false
  path.value.push({
    id: '',
    datasetId: item.datasetId,
    fileName: item.name,
    level: '1'
  })
  setTimeout(() => {
    handleSearch()
  }, 100)
}

const handleFileSelect = async (rawFile) => {
  if (rawFile.size / 1024 / 1024 > 50) {
    message.error('上传文件大小不能超过 50M！')
    return false
  }
  if (rawFile.size < 1) {
    message.error('上传文件不能为空！')
    return false
  }
  return true
}

const handleSearchKlList = async () => {
  const response = await FolderAPI.getKlDatasetPermission()
  knowledgeList.value = response || []
}

const handleSearch = () => {
  loadFolderContent(path.value[path.value.length - 1])
}

const knowledgeChooseList = ref([])
const handleChooseKnowlage = (item) => {
  knowledgeChooseList.value.push(item)
}

const handleChooseFile = (item) => {
  if (item.choose) {
    handleChooseKnowlage(item)
  } else {
    handleKnowlageCancel(item)
  }
}

const chooseDoc = (item) => {
  if (item.choose) {
    handleChooseDocument(item)
  } else {
    handleDocumentCancel(item)
  }
}

const fileListFilter = computed(() => {
  // fileList 与knowledgeChooseList 与knowledgeChooseList中值与fileList中值相同的数据 将fileList中的那条数据choose改为true
  return fileList.value.map((item) => {
    const isChoose = knowledgeChooseList.value.find((chooseItem) => chooseItem.id === item.id)
    return {
      ...item,
      choose: !!isChoose
    }
  })
})

const handleKnowlageCancel = (item) => {
  knowledgeChooseList.value = knowledgeChooseList.value.filter(
    (chooseItem) => chooseItem.id !== item.id
  )
}

// 点击名称文本进入下一级
const handleNameClick = (row) => {
  if (!row.filePrefix) {
    path.value.push(row)
    loadFolderContent(row)
  }
}

const computedStyle = computed(() => {
  if (path.value.length > 1) {
    return 'margin: 0 8px;'
  }
  return 'margin: 0 8px 0 0;'
})

// 返回上一级
const handleGoBack = () => {
  if (path.value.length > 1) {
    path.value.pop() // 移除最后一项
    loadFolderContent(path.value[path.value.length - 1]) // 加载上一级文件夹内容
  }
}

// 点击路径导航返回上一级
const handleBreadcrumbClick = (index) => {
  total.value = 0
  if (index === 0) {
    isShowKL.value = true
    fileList.value = []
    path.value = [{ id: '', fileName: '知识库', level: '1' }]
  } else {
    path.value = path.value.slice(0, index + 1)
    loadFolderContent(path.value[index])
  }
}

/** 打开弹窗 */
const open = async (row) => {
  // 重置状态
  activeName.value = 'first' // 重置到知识库标签页
  isShowKL.value = true // 重置到知识库列表视图
  path.value = [{ id: '', fileName: '根目录', level: '1' }]
  fileList.value = [] // 清空文件列表
  // 重置分页参数
  queryParams.pageNum = 1
  queryParams.pageSize = 10

  // loadFolderContent(path.value[path.value.length - 1]);
  handleSearchKlList()
  // 打开弹窗时，将localStorage中的数据赋值给对应tab
  documentIdList.value = []
  documentChooseList.value = localStorage.getItem(props.localDocName)
    ? JSON.parse(localStorage.getItem(props.localDocName))
    : []
  knowledgeChooseList.value = localStorage.getItem(props.localKnowName)
    ? JSON.parse(localStorage.getItem(props.localKnowName))
    : []
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 添加上传 loading 状态
const uploadLoading = ref(false)
const documentIdList = ref([])

const parseTimers = ref(new Map())
// 清除所有定时器的方法
const clearAllTimers = () => {
  parseTimers.value.forEach((timer, id) => {
    clearInterval(timer)
    console.log(`已清除ID为 ${id} 的定时器`)
  })
  parseTimers.value.clear()
}

// 清除单个定时器的方法
const clearParseTimer = (id) => {
  if (parseTimers.value.has(id)) {
    clearInterval(parseTimers.value.get(id))
    parseTimers.value.delete(id)
  }
}
// const isParse = ref(false)
// 上传成功 修改documentList
const submitFormSuccess = async (response, file, fileList) => {
  if (response.code === 0) {
    try {
      uploadLoading.value = true
      console.log('response', response)
      const { data } = response
      // 1. 上传到临时知识库成功后，再上传到云文档
      const formData = new FormData()
      const newFile = new File(
        [file.raw], // 文件内容（Blob/ArrayBuffer）
        uuidv4() + file.raw.name, // 新文件名
        {
          type: file.raw.type, // 保持原 MIME 类型
          lastModified: file.raw.lastModified // 保持原修改时间
        }
      )
      const oldFile = {
        ...file,
        name: uuidv4() + file.name,
        raw: newFile
      }
      formData.append('file', oldFile.raw)

      // 2. 调用 uploadFolderFile 上传到云文档
      const response1 = await uploadFolderFile(formData)

      // 3. 保存文档ID和更新文档列表

      const params = {
        fileUrl: response1.data.fileUrl,
        type: '3',
        documentId: data[0].id
      }
      const analyResponse = await FolderAPI.getCloudAnalysisBack(params)
      clearParseTimer(analyResponse.documentId)
      const timer = setInterval(async () => {
        const resultResponse = await FolderAPI.getCloudAnalysisBackResults({
          ...analyResponse,
          type: '3'
        })
        if (resultResponse.analysisStatus !== '1') {
          getDocumentList()
          clearParseTimer(analyResponse.documentId)
        }
      }, 20000)
      parseTimers.value.set(analyResponse.documentId, timer)
      documentIdList.value.push(
        ...data.map((item) => {
          return {
            id: item.id,
            fileUrl: response1.data.fileUrl,
            documentId: data[0].id
          }
        })
      )
      await getDocumentList()
      message.success('上传成功')
    } catch (error) {
      console.error('上传文件失败:', error)
      message.error('上传文件失败')
    } finally {
      uploadLoading.value = false
    }
  }
}

const getDocumentList = async () => {
  try {
    const response = await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_temporary_data}/documents`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `${apikeys.ragflow_token}`,
        Accept: '*/*'
      },
      params: { offset: queryParams.pageNum, limit: queryParams.pageSize }
    })
    const { data } = response
    const filteredDocs = (data?.data?.docs ?? [])
      .map((doc) => {
        const matchedDoc = documentIdList.value.find((id) => id.id === doc.id)
        if (matchedDoc) {
          return {
            ...doc,
            fileUrl: matchedDoc.fileUrl,
            documentId: matchedDoc.documentId,
            content: matchedDoc.content
          }
        }
        return doc
      })
      .filter((doc) => documentIdList.value.some((id) => id.id === doc.id))
    documentList.value = filteredDocs
    total.value = filteredDocs.length
    // total.value = data?.data?.total ?? 0
  } finally {
  }
}

const handleChooseDocument = (item) => {
  documentChooseList.value.push(item)
}

const handleDocumentCancel = (item) => {
  documentChooseList.value = documentChooseList.value.filter(
    (chooseItem) => chooseItem.id !== item.id
  )
}

const documentChooseList = ref([])
const documentListFilter = computed(() => {
  return documentList.value.map((item) => {
    const isChoose = documentChooseList.value.find((chooseItem) => chooseItem.id === item.id)
    return {
      ...item,
      choose: !!isChoose
    }
  })
})

const handleRunDocumentByIds = async (id) => {
  try {
    await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_temporary_data}/chunks`,
      method: 'post',
      headers: {
        Authorization: `${apikeys.ragflow_token}`
      },
      data: {
        document_ids: [id]
      }
    })
    getDocumentList()
  } finally {
  }
}

// 删除知识库内容
const handleDelete = async () => {
  try {
    const response = await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_temporary_data}/documents`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `${apikeys.ragflow_token}`,
        Accept: '*/*'
      },
      params: { offset: queryParams.pageNum, limit: queryParams.pageSize }
    })
    const { data } = response
    const ids = data?.data?.docs?.map((item) => item.id)
    await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_temporary_data}/documents`,
      method: 'delete',
      headers: {
        Authorization: `${apikeys.ragflow_token}`
      },
      data: {
        ids: ids
      }
    })
    getDocumentList()
  } catch (error) {
    console.log(error)
  }
}

/** 提交表单 */
const submitForm = async () => {
  let formData = []
  documentChooseList.value.forEach((item) => {
    formData.push({
      id: item.id,
      name: item.name,
      content: item.content,
      documentId: item.documentId,
      datasetId: item.dataset_id,
      fileName: item.name
    })
  })
  knowledgeChooseList.value.forEach((item) => {
    formData.push({
      id: item.id,
      name: item.fileName,
      documentId: item.documentId,
      datasetId: item.datasetId,
      fileName: item.fileName
    })
  })

  // 点击确认时将数据直接保存到localStorage中
  localStorage.setItem(props.localDocName, JSON.stringify(documentChooseList.value))
  localStorage.setItem(props.localKnowName, JSON.stringify(knowledgeChooseList.value))
  if (props.limit && formData.length > props.limit) {
    message.error(`最多选择${props.limit}个文件`)
    return
  }
  emit('success', formData)
  dialogVisible.value = false
}

onUnmounted(() => {
  clearAllTimers()
})

const beforeClose = (done) => {
  clearAllTimers()
  done()
}
</script>

<style lang="scss" scoped>
.ellipsiss {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 170px;
  display: inline-block;
}
.breadcrumb-wrapper {
  display: flex;
  height: 32px;
  align-items: center;
  padding: 0 20px 0 0;
  .back-button {
    padding: 0;
    font-size: 14px;
  }
}
.file-item {
  line-height: 32px;
  padding: 8px;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
  display: flex;
  .file-name {
    flex: 1;
  }
  .choose-button {
    width: 60px;
  }
}
.choose-button {
  height: 32px;
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 520px;
}
.kl-content {
  padding-top: 10px;
  padding: 10px 10px 0;
  .knowledge-list {
    height: 251px;
    display: flex;
    margin-bottom: 10px;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 5%);
    cursor: pointer;
    .list-header {
      display: flex;
      justify-content: space-between;
    }
    .title {
      color: #000000e0;
      margin: 10px 0;
      font-size: 24px;
      line-height: 32px;
      font-weight: 600;
      word-break: break-all;
    }
    .footer {
      p {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 12px;
        line-height: 22px;
      }
    }
  }
}
</style>
