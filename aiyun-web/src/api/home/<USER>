import request from '@/config/axios'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getAccessToken } from '@/utils/auth'
import { config } from '@/config/axios/config'

// 统计查询 /admin-api/v1/kownledge/HomePage/getcount
export const getHomeCount = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/getcount',  params })
}

// 下载查询 v1/kownledge/HomePage/klCloudDownloadCount
export const getHomeDownloadCount = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/klCloudDownloadCount',  params })
}

// 点赞查询 /v1/kownledge/HomePage/klCloudLikesCount
export const getHomeLikesCount = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/klCloudLikesCount',  params })
}

// 推荐查询 v1/kownledge/HomePage/klCloudPushesCount
export const getHomePushesCount = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/klCloudPushesCount',  params })
}

// 收藏查询 v1/kownledge/HomePage/klCloudfavoritesCount
export const getHomeFavoritesCount = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/klCloudfavoritesCount',  params })
}

// 文档七日统计 v1/kownledge/HomePage/klCloudhebdomadCount
export const getHomeHebdomadCount = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/klCloudhebdomadCount',  params })
}

// 七日访问查询v1/kownledge/HomePage/loginCount
export const getHomeLoginCount = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/loginCount',  params })
}

// 获取知识库首页/v1/kownledge/HomePage/gethomeklDatset
export const getHomeKlDatset = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/gethomeklDatset',  params })
}

// 获取知识库标签云 /admin-api/v1/kownledge/HomePage/getklDatsetFileLabel
export const getHomeKlDatsetFileLabel = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/getklDatsetFileLabel',  params })
}

// 分页查询所有知识模型 - 搜索历史信息 /admin-api/v1/kownledge/klCloudSearchLog
export const getSearchLog = (params) => {
  return request.get({ url: '/v1/kownledge/klCloudSearchLog', params })
}

// 删除搜索历史v1/kownledge/klCloudSearchLog/{id}
export const deleteSearchLog = (id) => {
  return request.delete({ url: `/v1/kownledge/klCloudSearchLog/${id}` })
}
// 清空知识模型 - 搜索历史信息/kownledge/klCloudSearchLog/Delete
export const deleteSearchLogAll = (data) => {
  return request.delete({ url: `/v1/kownledge/klCloudSearchLog/Delete`, data })
}

// 搜索提示补全 v1/kownledge/HomePage/klCloudSearchTip/{prefix}
export const getSearchTip = (prefix) => {
  return request.get({ url: `/v1/kownledge/HomePage/klCloudSearchTip/${prefix}` })
}

// 查询知识模型 - 标签配置信息热度排序/v1/kownledge/klCloudLabel/page
export const getLabelPage = (params) => {
  return request.get({ url: '/v1/kownledge/klCloudLabel/page', params })
}

// 首页搜索v1/kownledge/HomePage/retrieval
export const getHomeRetrieval = (data) => {
  return request.post({ url: '/v1/kownledge/HomePage/retrieval', data })
}

// 查看记录/v1/kownledge/klCloudLog
export const createCloudLog = (data) => {
  return request.post({ url: '/v1/kownledge/klCloudLog', data })
}

// 根据知识库id、文件夹路径和标签 查询列表查询 /admin-api/v1/kownledge/klCloud/listBylabel
export const getListBylabel = (params) => {
  return request.get({ url: '/v1/kownledge/klCloud/listBylabel', params })
}

// 分页查询知识模型 - 文件记录信息/kownledge/klFileEventLog
export const getFileEventLog = (params) => {
  return request.get({ url: '/v1/kownledge/klFileEventLog', params })
}

// 新增知识模型 - 文件记录信息/v1/kownledge/klFileEventLog
export const createFileEventLog = (data) => {
  return request.post({ url: '/v1/kownledge/klFileEventLog', data })
}

// admin-api/v1/kownledge/klCloud/generatePromotionReports
export const generatePromotionReports = (data) => {
  return request.post({ url: '/v1/kownledge/klCloud/generatePromotionReports', data })
}

// 生成可研技术报告/kownledge/klAgent/technicalReports
export const getTechnicalReports = (data) => {
  return request.post({ url: `/v1/kownledge/klAgent/technicalReports`, data })
}


// 学习园地配置新增 v1/kownledge/klStudyConfig
export const createStudyConfig = (data) => {
  return request.post({ url: '/v1/kownledge/klStudyConfig', data })
}

// 查询学习园地/admin-api/v1/kownledge/HomePage/study
export const getStudy = (params) => {
  return request.get({ url: '/v1/kownledge/HomePage/study', params })
}
// 查询学习园地配置信息/admin-api/v1/kownledge/klStudyConfig
export const getStudyConfig = (params) => {
  return request.get({ url: '/v1/kownledge/klStudyConfig', params })
}

// 对话
export const homePageStream = ({
  data,
  onClose,
  onMessage,
  onError,
  ctrl
}) => {
  const token = getAccessToken()
  console.log('data', data);
  
  // /admin-api/v1/kownledge/HomePage/ask
  return fetchEventSource(`${config.base_url}/v1/kownledge/HomePage/ask`, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    },
    openWhenHidden: true,
    body: JSON.stringify(data),
    onmessage: onMessage,
    onerror: onError,
    onclose: onClose,
    signal: ctrl.signal
  })
}