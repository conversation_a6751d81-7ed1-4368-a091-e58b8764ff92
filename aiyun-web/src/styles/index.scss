@import './var.css';
@import './FormCreate/index.scss';
@import './theme.scss';
@import 'element-plus/theme-chalk/dark/css-vars.css';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

// 数据集问答popover样式
// .popover-answer {
//   padding: 20px;
//   max-height: 40vh;
// }
.answer-pop-content {
  max-height: 40vh;
  overflow: auto;
  table {
    border-collapse: collapse;
    tr, td {
      padding: 6px 13px;
      border: 1px solid #d1d9e0
    }
    tr:nth-child(2n) {
      background-color: #f2f2f2;
    }
  }
}

.ellipsis {
  white-space: nowrap;      
  overflow: hidden;        
  text-overflow: ellipsis; 
}

.chat-dots {
  display: inline-flex;
  align-items: flex-end;
}

.chat-dots .dot {
  display: inline-block;
  width: 4px;
  height: 4px;
  opacity: 0;
  margin-right: 2px;
  animation: dot-fade 1.5s infinite ease-in-out;
}

.chat-dots .dot:nth-child(1) {
  animation-delay: 0ms;
}

.chat-dots .dot:nth-child(2) {
  animation-delay: 0.5s;
}

.chat-dots .dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes dot-fade {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
