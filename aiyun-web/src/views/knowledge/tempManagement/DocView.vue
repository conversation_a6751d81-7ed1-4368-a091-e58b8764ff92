<template>
  <el-dialog 
    v-model="dialogVisible" 
    :scroll="true" 
    :fullscreen="true" 
    :destroy-on-close="true"
    :before-close="handleBeforeClose" 
    :title="dialogTitle">
    <div class="content" v-if="dialogVisible">
      <DocumentEditor 
        id="docEditor" 
        ref="documentEditorRef" 
        :events_onDocumentReady="onDocumentReady"
        :documentServerUrl="`${apikeys.onlyoffice_url}`" 
        :config="config" />
    </div>
    <!-- <el-dialog 
      v-model="innerVisible" 
      width="500" 
      title="提示" 
      :show-close="false" 
      :close-on-click-modal="false"
      :close-on-press-escape="false" 
      append-to-body>
      <span>文档保存中，将在 {{ countdown }} 秒后自动关闭...</span>
    </el-dialog> -->
  </el-dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { DocumentEditor, } from '@onlyoffice/document-editor-vue'
import { useUserStore } from '@/store/modules/user'
import * as TemplateAPI from '@/api/knowledge/tempmangement'
import * as UserAPI from '@/api/system/user'
import { createCloudLog } from '@/api/home/<USER>'

import { v4 as uuidv4 } from 'uuid'
import { before } from 'lodash'

defineOptions({ name: 'SystemUserForm' })
const userStore = useUserStore()
const apikeys = userStore.getAppConfig
// 新建： create
// 编辑： update
// 查看： view
const docObj = {
  create: '新建文档',
  update: '编辑文档',
  view: '查看文档'
}
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const emit = defineEmits(['success'])

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const dialogType = ref('')

// const config = ref({
//   document: {
//     fileType: 'docx',
//     key: uuidv4(),
//     title: '技术规范书',
//     url: 'http://rthl.88ic.cn:9000/yudao/init.docx'
//   },
//   documentType: 'word',
//   editorConfig: {
//     lang: "zh-CN",
//     mode: 'edit'
//   }
// })


const url = import.meta.env.VITE_DOCUMENT_URL

const getUserInfo = async () => {
  const response = await UserAPI.getUserProfileById(userStore.getUser.id)
  return response
}

const config = ref({
  document: {
    fileType: 'docx',
    key: uuidv4(),
    title: '技术规范书',
    url: `${apikeys.file_server_url}/init.docx`,
    permissions: {
      comment: true,
      download: true,
      modifyContentControl: true,
      modifyFilter: true,
      print: false,
      edit: true,
      fillForms: true,
      review: true
    }
  },
  documentType: "word",
  editorConfig: {
    lang: "zh-CN",
    // callbackUrl: import.meta.env.VITE_DOCUMENT_URL + "/admin-api/v1/kownledge/klCloud/onlyOfficeSave",
    callbackUrl: `${apikeys.file_callback_url}`,
    customization: {
      commentAuthorOnly: false,
      comments: true,
      compactHeader: false,
      compactToolbar: true,
      feedback: false,
      plugins: true
    },
    user: {
      id: '',
      name: ''
    },
  }
})


const docKey = ref('')
const docTile = ref('')
const docUrl = ref('')
const timeoutTimer = ref()
/** 打开弹窗 */
const open = async (type, path, row) => {
  console.log('模板');
  const tempFileName  = row.templateFileName ? row.templateFileName : row.fileName
  const arr = tempFileName.split('.')
  const fileType = tempFileName.split('.').pop()?.toLowerCase() || 'docx'
  // const fileType = tempFileName.split('.')?.toLowerCase() || 'docx'
  docKey.value = uuidv4() + '-klCloudTemplate'
  if (type === 'create') {
    docTile.value = '新建文件'
    docUrl.value = `${apikeys.file_server_url}/init.docx`
    const params = {
      fileKey: docKey.value,
      parentId: path.id ? path.id : null,
      templateFilePath: path.id ? path.templateFilePath + '/' + docTile.value + '.docx' : null,
      level: path.id ? parseInt(path.level) : '1',
      templateFileName: docKey.value + '.docx',
      // saveStatus: '0',
      templateFilePrefix: '.docx'
    }
    const response = await TemplateAPI.createFolder(params)
  } else if( type === 'update') {
    docTile.value = row.templateFileName ? row.templateFileName : row.fileName
    docUrl.value = row.templateFileUrl ? row.templateFileUrl : row.fileUrl ? row.fileUrl : `${apikeys.file_server_url}/init.docx`
    const response = await TemplateAPI.updateFolder({
      id: row.id,
      fileKey: docKey.value,
      // saveStatus: '0',
    })
  } else {
    docTile.value = row.templateFileName ? row.templateFileName : row.fileName
    docUrl.value = row.templateFileUrl ? row.templateFileUrl : row.fileUrl ? row.fileUrl : `${apikeys.file_server_url}/init.docx`
  }
  
  const { id: userId, username: userName } = await getUserInfo()
  config.value = {
    document: {
      fileType: fileType,
      key: docKey.value,
      title: docTile.value,
      url: docUrl.value,
      permissions: {
        comment: true,
        download: true,
        modifyContentControl: true,
        modifyFilter: type === 'view' ? false : true,
        print: false,
        edit: type === 'view' ? false : true,
        fillForms: true,
        review: type === 'view' ? false : true
      },
    },
    documentType: "word",
    editorConfig: {
      lang: "zh-CN",
      callbackUrl: `${apikeys.file_callback_url}`,
      // callbackUrl: `http://aiyun.frp.yn.asqy.net/admin-api/v1/kownledge/common/onlyOfficeSave`,
      customization: {
        commentAuthorOnly: false,
        comments: type === 'view' ? false : true,
        compactHeader: false,
        compactToolbar: type === 'view' ? false : true,
        feedback: false,
        plugins: type === 'view' ? false : true,
        autosave: type === 'view' ? false : true,
        forcesave: type === 'view' ? false : true,
        chat: false,
        help: type === 'view' ? false : true,
        toolbarNoTabs: false
      },
      user: {
        id: userId,
        name: userName
      },
      mode: type === 'view' ? 'view' : 'edit',
    },
    height: "100%",
    width: "100%",
    type: "desktop"
  }
  dialogVisible.value = true
  dialogTitle.value = type ? docObj[type] : '查看文档'
  dialogType.value = type
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const documentEditorRef = ref()

const submitForm = async () => {
  if (documentEditorRef.value) {
    const editor = documentEditorRef.value.getInstance();
    if (editor) {
      try {
        await editor.save();
        dialogVisible.value = false
        emit('success')
        message.success('文档保存成功');
      } catch (error) {
        console.error('保存文档失败', error);
        message.error('文档保存失败');
      }
    }
  }
}

const countdown = ref(0)
let timer = null

// 处理弹窗关闭前的回调
const handleBeforeClose = (done) => {
  done()
  // if (dialogType.value === 'view') {
  //   clearTimeout(timeoutTimer.value)
  //   done()
  // } else {

  // }
  // 不调用 done() 保持弹窗打开
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 10
  innerVisible.value = true
  timer = setInterval(() => {
    countdown.value -= 1
    if (countdown.value <= 0) {
      innerVisible.value = false
      closeDialog()
    }
  }, 1000)
}

// 取消倒计时
const cancelCountdown = () => {
  clearInterval(timer)
  countdown.value = 0
}

// 关闭弹窗并触发成功事件
const closeDialog = () => {
  clearInterval(timer)
  dialogVisible.value = false
  emit('success')
}


const onDocumentReady = () => {
  console.log('Document ready');
}


</script>

<style lang="scss" scoped>
.content {
  padding: 10px;
  height: calc(100vh - 36px);
}
</style>
