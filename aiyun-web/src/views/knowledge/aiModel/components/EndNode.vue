<script setup lang="ts">
import { Handle, Position, useVueFlow } from "@vue-flow/core";
import { NodeToolbar } from '@vue-flow/node-toolbar';
import { Delete } from '@element-plus/icons-vue';
import { markRaw } from 'vue';

defineOptions({ name: 'EndNode' })

const { removeNodes } = useVueFlow();

const Icons = {
  Delete: markRaw(Delete)
};

const props = defineProps<{
  id: string;
  data: {
    label: string;
    toolbarVisible?: boolean;
  }
}>();

const emit = defineEmits(['updateNodeInternals']);
</script>

<template>
  <div class="node-wrapper">
    <NodeToolbar
      :is-visible="data.toolbarVisible"
      style="background-color: #fff;"
      :position="Position.Top"
      align="end"
      :offset="4"
    >
      <el-button
        @click.stop="removeNodes([props.id])"
      >
        <el-icon><component :is="Icons.Delete" /></el-icon>
      </el-button>
    </NodeToolbar>
    <div class="end-node">
      <div class="node-content">
        <div class="node-header">
          <span class="node-type">{{ data.label }}</span>
        </div>
      </div>
      <Handle
        type="target"
        :position="Position.Top"
        :id="`${props.id}-top`"
        class="horizontal-handle"
      />
    </div>
  </div>
</template>

<style scoped>
.node-wrapper {
  position: relative;
}

.end-node {
  width: 120px;
  background-color: white;
  border: 2px solid #f56c6c;
  border-radius: 8px;
  padding: 12px;
}

.node-content {
  display: flex;
  flex-direction: column;
}

.node-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.node-type {
  font-weight: bold;
  color: #f56c6c;
  text-align: center;
}

.horizontal-handle {
  background-color: #f56c6c;
  width: 30px;
  height: 16px;
  border-radius: 5px;
}
</style> 