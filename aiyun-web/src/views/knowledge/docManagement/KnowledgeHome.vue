<template>
  <ContentWrap>
    <!-- 头部按钮和搜索框 -->
    <div class="header">
      <!-- 左侧按钮 -->
      <div>
        <el-button type="primary" @click="handleUploadFile" v-if="isOner || headBtn.includes('upload')">上传文件</el-button>
        <el-button 
          type="primary" 
          @click="handleNewFolder('folder')"
          v-if="isOner || headBtn.includes('createFolder')">新建文件夹</el-button>
        <!-- <el-button type="primary" @click="handleNewFolder('document')">新建在线文档</el-button> -->
        <!-- <el-button type="primary" @click="handleNewFile">新建在线文档</el-button> -->
      </div>

      <!-- 右侧搜索框和按钮 -->
      <div>
        <el-input v-model="queryParams.fileName" placeholder="请输入名称搜索" style="width: 200px; margin-right: 10px;" />
        <el-button type="primary" @click="handleSearchKnowledge">搜索</el-button>
        <!-- <el-button @click="handleReset">重置</el-button> -->
      </div>
    </div>

    <!-- 路径导航 -->
    <div class="breadcrumb-wrapper">
      <!-- 返回上一级按钮 -->
      <el-button v-if="path.length > 2" @click="handleGoBack" type="primary" link class="back-button">
        返回上一级
      </el-button>
      <el-divider direction="vertical" :style="computedStyle" />
      <!-- 路径导航 -->
      <el-breadcrumb separator="/">
        <el-breadcrumb-item v-for="(item, index) in path" :key="index" @click="handleBreadcrumbClick(index)">
          <span style="cursor: pointer;">{{ item.fileName }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 文件列表 -->
    <el-table v-loading="tableLoading" :data="fileList">
      <el-table-column label="名称" min-width="500">
        <template #default="{ row }">
          <div class="name-column">
            <!-- 名称文本 -->
            <!-- 重命名 文本框 确认 取消 -->
            <div class="name-wrapper">
              <div v-if="editId == row.id">
                <el-input v-model="editName" size="small" maxlength="40" style="width: 200px;" />
                <el-button 
                  type="primary" 
                  style="margin-left: 5px;" 
                  size="small"
                  @click="handleEditNameConfirm(row)">确认</el-button>
                <el-button type="primary" link size="small" @click="handleEditNameCancel(row)">取消</el-button>
              </div>
              <span v-else :class="row.filePrefix ? '' : 'name-text'" @click.stop="handleNameClick(row)">
                <div class="flex align-center" v-if="row.filePrefix">
                  <span>
                    <el-icon class="mr-[4px]">
                      <Document />
                    </el-icon>
                  </span>
                  <el-tooltip :content="row.fileName" placement="top">
                    <span class="ellipsis">
                      {{ row.fileName }}
                    </span>
                  </el-tooltip>
                </div>
                <div class="flex align-center" v-else>
                  <span>
                    <el-icon class="mr-[4px]">
                      <FolderOpened />
                    </el-icon>
                  </span>
                  <span>
                    <el-tooltip :content="row.fileName" placement="top">
                      <span class="ellipsis">
                        {{ row.fileName }}
                      </span>
                    </el-tooltip>
                  </span>
                </div>
              </span>
            </div>

            <!-- 操作按钮组 -->
            <div class="button-group" v-if="row.id != '1' && row.id != '-1'">
              <!-- 文件专属操作 -->
              <template v-if="row.filePrefix">
                <template v-if="isOner || (knowledgeisLikes && rowBtn[row.parentId] && rowBtn[row.parentId].includes('like'))">
                  <el-tooltip v-if="!row.isLikes" content="喜欢" placement="top">
                    <el-button type="primary" link @click.stop="handleLike(row)">
                      <!-- <el-icon><Star /></el-icon> -->
                      <Icon icon="fa:heart-o" />
                    </el-button>
                  </el-tooltip>
                  <!-- 取消喜欢 -->
                  <el-tooltip v-else content="取消喜欢" placement="top">
                    <el-button type="primary" link @click.stop="handleCancelLike(row)">
                      <!-- <el-icon><StarFilled /></el-icon> -->
                      <Icon icon="fa:heart" />
                    </el-button>
                  </el-tooltip>
                </template>
                <template v-if="isOner || (knowledgeisFavorites && rowBtn[row.parentId] && rowBtn[row.parentId].includes('collect'))">
                  <el-tooltip v-if="!row.isFavorites" content="收藏" placement="top">
                    <el-button type="primary" link @click.stop="handleCollect(row)">
                      <!-- <el-icon><Star /></el-icon> -->
                      <Icon icon="fa:star-o" />
                    </el-button>
                  </el-tooltip>
                  <el-tooltip v-else content="取消收藏" placement="top">
                    <el-button type="primary" link @click.stop="handleCancelCollect(row)">
                      <!-- <el-icon><Star /></el-icon> -->
                      <Icon icon="fa:star" />
                    </el-button>
                  </el-tooltip>
                </template>

                <template v-if="isOner || (knowledgeisShard && rowBtn[row.parentId] && rowBtn[row.parentId].includes('share'))">
                  <el-tooltip v-if="row.shardStatus === '1'" content="分享" placement="top">
                    <el-button type="primary" link @click.stop="handleShare(row)">
                      <!-- <el-icon><Share /></el-icon> -->
                      <Icon icon="fa:share-square-o" />
                    </el-button>
                  </el-tooltip>
                </template>
                <template v-if="isOner ||(knowledgeisPushes && rowBtn[row.parentId] && rowBtn[row.parentId].includes('recommend'))">
                  <el-tooltip v-if="!row.isPushes" content="推荐" placement="top">
                    <el-button type="primary" link @click.stop="handleRecommend(row)">
                      <!-- <Icon icon="fa:promotion" /> -->
                      <el-icon>
                        <Promotion />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip v-else content="取消推荐" placement="top">
                    <el-button type="primary" link @click.stop="handleCancelRecommend(row)">
                      <!-- <Icon icon="fa:promotion" /> -->
                      <el-icon>
                        <Promotion />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                </template>

                <template v-if="rowBtn[row.parentId] && rowBtn[row.parentId].includes('view')">
                  <!-- 查看文档 编辑文档 -->
                  <el-tooltip content="查看文档" placement="top">
                    <el-button type="primary" link @click.stop="handleView(row)">
                      <!-- <el-icon><View /></el-icon> -->
                      <Icon icon="fa:eye" />
                    </el-button>
                  </el-tooltip>
                </template>

                <template v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('edit'))">
                  <el-tooltip content="编辑文档" placement="top">
                    <el-button type="primary" link @click.stop="handleEdit(row)">
                      <!-- <el-icon><Edit /></el-icon> -->
                      <!-- <i class="far fa-calendar-edit" /> -->
                      <Icon icon="fa-solid:file-signature" />
                    </el-button>
                  </el-tooltip>
                </template>

                <template v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('download'))">
                  <el-tooltip content="下载" placement="top">
                    <el-button type="primary" link @click.stop="handleDownload(row)">
                      <!-- <el-icon><Download /></el-icon> -->
                      <Icon icon="fa:cloud-download" />
                    </el-button>
                  </el-tooltip>
                </template>
                <template v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('rename'))">
                  <el-tooltip content="重命名" placement="top">
                    <el-button type="primary" link @click.stop="handleRename(row)">
                      <!-- <el-icon><Edit /></el-icon> -->
                      <Icon icon="fa:edit" />
                    </el-button>
                  </el-tooltip>
                </template>


                <!-- 更多操作下拉菜单 -->
                <el-dropdown trigger="click" @command="handleCommand(row, $event)" v-if="isOner || row.moreBtnShow">
                  <span class="more-actions">
                    更多
                    <el-icon :size="12" style="margin-left: 5px;">
                      <ArrowDown />
                    </el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="move" v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('move'))">
                        <el-icon>
                          <FolderOpened />
                        </el-icon>移动
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('delete'))">
                        <el-icon>
                          <Delete />
                        </el-icon>删除
                      </el-dropdown-item>
                      <el-dropdown-item command="parse" v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('parse'))">
                        <el-icon>
                          <MagicStick />
                        </el-icon>解析
                      </el-dropdown-item>
                      <!-- <el-dropdown-item 
                        command="parseSettings"
                        v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('parseSettings'))">
                        <el-icon>
                          <Setting />
                        </el-icon>解析设置
                      </el-dropdown-item> -->
                      <!-- 查看报告 -->
                      <!-- <el-dropdown-item command="report" v-if="isOner || (rowBtn[row.parentId] && rowBtn[row.parentId].includes('report'))">
                        <el-icon>
                          <Reading />
                        </el-icon>查看报告
                      </el-dropdown-item> -->

                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
              <template v-else>
                <el-tooltip content="重命名" placement="top" v-if="isOner || (rowBtn[row.id] && rowBtn[row.id].includes('renamefolder'))">
                  <el-button type="primary" link @click.stop="handleRename(row)">
                    <Icon icon="fa:edit" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="移动" placement="top" v-if="isOner || (rowBtn[row.id] && rowBtn[row.id].includes('movefolder'))">
                  <el-button type="primary" link @click.stop="handleMove(row)">
                    <!-- <el-icon><FolderOpened /></el-icon> -->
                    <Icon icon="fa:folder-open" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="删除" placement="top" v-if="isOner || (rowBtn[row.id] && rowBtn[row.id].includes('deletefolder'))">
                  <el-button type="primary" link @click.stop="handleDelete(row)">
                    <Icon icon="fa:trash-o" />
                  </el-button>
                </el-tooltip>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传日期" width="200">
        <template #default="{ row }">
          <span style="display: inline-block;line-height: 32px;">{{ row.id === '1' || row.id === '-1' ? '-' :
            row.createTime }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="parseMethod" label="解析方式">
        <template #default="{ row }">
          <span>{{ row.id === '1' || row.id === '-1' ? '-' : row.parseMethod }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="分享">
        <template #default="{ row }">
          <span style="line-height: 32px;" v-if="row.id === '1' || row.id === '-1' || !row.filePrefix">-</span>
          <el-switch 
            v-else v-model="row.shardStatus" 
            :disabled="!(isOner || (knowledgeisShard && rowBtn[row.parentId] && rowBtn[row.parentId].includes('share')))"
            :active-value="'1'" :inactive-value="'0'"
            @change="handleShareChange(row)" />
        </template>
      </el-table-column> -->
      <el-table-column label="创建人" prop="ren" />
      <el-table-column prop="analysisStatus" label="解析状态" min-width="100">
        <template #default="{ row }">
          <!-- 0 未解析1解析中2解析完成 -->
          <span v-if="row.id === '1' || row.id === '-1' || !row.filePrefix">-</span>
          <el-popover placement="top" :width="400" >
            <template #reference>
              <el-tag v-if="row.filePrefix" :type="statusClass[row.analysisStatus]">{{ analysisObj[row.analysisStatus]
              }}</el-tag>
            </template>
            <p>解析结果：{{ row.analysisMessage ? row.analysisMessage : '-'}}</p>
          </el-popover>
          <!-- <el-button 
            v-if="row.filePrefix && row.analysisStatus === '1'" 
            type="primary" link 
            size="small"
            style="margin-left: 10px;"
            @click="handleDocumentRefresh(row)"><el-icon>
              <Refresh />
            </el-icon></el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <Pagination 
      :total="total" 
      v-model:page="queryParams.pageNum" 
      v-model:limit="queryParams.pageSize"
      @pagination="queryList" />
  </ContentWrap>
  <UploadForm 
    ref="uploadFormRef" 
    :path="path[path.length - 1]"
    :create-folder="FolderAPI.createFolder"
    :update-folder="FolderAPI.updateFolder"
    @success="handleSearch" />
  <UploadMultipleForm 
    ref="UploadMultipleFormRef" 
    :path="path[path.length - 1]"
    :create-folder="FolderAPI.uploadFolderFileMore"
    :update-folder="FolderAPI.updateFolder"
    @success="handleSearch" />
  <DocView ref="docViewRef" @success="handleSearch" />
  <ParseSettingsForm ref="parseSettingsFormRef" @success="handleSearch" />
  <MoveDialog ref="moveDialogRef" :isCommon="false" @success="handleSearch" />
  <ShareDialog ref="shareDialogRef" @success="handleSearch" />
  <ShareList ref="shareListRef" />
  <ReportView ref="reportViewRef" />
  <KnowledgeDialog ref="knowledgeDialogRef" />
  <ImgView ref="imgViewRef" />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
// 新增图标引入
import {
  Star,
  StarFilled,
  Share,
  Download,
  Edit,
  FolderOpened,
  Delete,
  Promotion,
  MagicStick,
  Setting,
  View,
  EditPen,
  MoreFilled,
  ArrowDown, Document, Refresh, Reading
} from '@element-plus/icons-vue'
import * as FolderAPI from '@/api/knowledge/docmangement'
import { CommonStatusEnum } from '@/utils/constants'
import { useUserStore } from '@/store/modules/user'
import { v4 as uuidv4 } from 'uuid'
import { downloadFileFunction } from '@/utils/downloadFile'
import UploadForm from './components/UploadForm.vue';
import UploadMultipleForm from './components/UploadMultipleForm.vue';
import ParseSettingsForm from './components/ParseSettingsForm.vue';
import { knowledgeFileDownload } from "@/api/knowledge/docmangement";
import axios from 'axios'
import DocView from './components/DocView.vue'
import MoveDialog from './components/MoveDialog.vue';
import ShareDialog from './components/ShareDialog.vue';
import ShareList from './components/ShareList.vue';
import ReportView from './components/ReportView.vue';
import KnowledgeDialog from './components/KnowledgeDialog.vue';
import ImgView from './components/ImgView.vue';

const route = useRoute() 
const router = useRouter() 
const message = useMessage() // 消息弹窗

// 当前路径
const path = ref([{ id: '', fileName: '知识库', level: '1' }]);
// 0 未解析1解析中2解析完成
const analysisObj = ref({
  '0': '未解析',
  '1': '解析中',
  '2': '解析完成',
  '3': '解析失败'
});

const statusClass = ref({
  '0': 'info',
  '1': 'primary',
  '2': 'success',
  '3': 'danger',
})


// 当前文件夹下的文件列表
const fileList = ref([]);

// 编辑id
const editId = ref('-1');
const editName = ref('');
const tableLoading = ref(false);

// 搜索查询
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileName: '',
  keyword: ''
});
const total = ref(0);
const parseSettingsFormRef = ref(null);
const docViewRef = ref(null);
const moveDialogRef = ref(null);
const shareDialogRef = ref(null);

const buttonRole = ref([])
const userStore = useUserStore()
const apikeys = userStore.getAppConfig
const userInfo = userStore.getUser
const emit = defineEmits(['pathBack'])

const headBtn = ref([]);
const rowBtn = ref({});
const ids = ref([]);

const props = defineProps({
  chooseKnowledge: {
    type: Object,
    default: () => {
    }
  }
})

const showMore = (row) => {
  const id = row.parentId;
  const actions = ['move', 'delete', 'parse', 'parseSettings', 'report'];
  
  if (rowBtn.value[id]) {
    return actions.some(action => rowBtn.value[id].includes(action));
  }
  
  return false;
};


const getButtonRole = async () => {
  const role = await FolderAPI.getUserRole()
  const roleIdList = (role || []).map(item => item.id)
  const response = await FolderAPI.getCloudButtonRole({
    roleIdList: roleIdList.join(',')
  })
  const filteredRes = response.filter(item => item?.roleContent?.length > 0)

  const newArr = filteredRes.flatMap(item => {
    return item?.roleContent?.map(content => ({
      ...content,
      klCloudId: item.klCloudId,
      roleId: item.roleId
    }))
  })
  
  ids.value = newArr.map(item => item.klCloudId).filter(item => item !== null)
  // headBtn.value = newArr.filter(item => item.klCloudId === '0').map(item => item.code);
  
  const obj = {};
  newArr.forEach(item => {
    if (obj[item.klCloudId]) {
      obj[item.klCloudId].push(item.code)
    } else {
      obj[item.klCloudId] = [item.code]
    }
  })
  rowBtn.value = obj;
  // buttonRole.value = response.data
}

// 加载文件夹内容
const loadFolderContent = async (row) => {
  // fileList.value = fakeData[folderId] || [];
  const { id, level,datasetId } = row
  // tableLoading.value = true;
  await getButtonRole();
  const response = await FolderAPI.getListByRole({
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    fileName: queryParams.value.fileName,
    parentId: id,
    level: id ? parseInt(level) + 1 : level,
    datasetId
  });
  tableLoading.value = false;
  total.value = response.total;
  if(timer.value) {
    clearInterval(timer.value);
  }
  const isAnalysising = response.records.some(item => item.analysisStatus === '1');
  if(isAnalysising) {
    timer.value = setInterval(() => {
      loadFolderContent(path.value[path.value.length - 1]);
    }, 10000);
  }
  fileList.value = response.records.map((item) => {
    return {
     ...item,
     moreBtnShow: showMore(item),
   };
  });
  console.log('fileList', fileList.value);
};

const queryList = () => {
  loadFolderContent(path.value[path.value.length - 1]);
}

const knowledgeId = ref(null)

// isFavorites isPushes isShard
const knowledgeisLikes = ref(false)
const knowledgeisFavorites = ref(false)
const knowledgeisPushes = ref(false)
const knowledgeisShard = ref(false)
const isOner = ref(false)

watch(() => props.chooseKnowledge, async(newValue, oldValue) => {
  if (newValue) {
    const { id, name, datasetId, creatorId } = newValue
    knowledgeId.value = id
    if (creatorId === '' + userInfo.id) {
      isOner.value = true
    } else {
      // 获取权限
      const response = await FolderAPI.getUserKlDatasetPermission({
        datasetId
      })
      const permission = response[0]?.permission ? response[0].permission : []
      headBtn.value = permission && permission.map((item) => item.buttonCode)
      // 获取知识库
      const knowledgeRes = await FolderAPI.getKlDatasetById(id)
      if (knowledgeRes) {
        // 喜欢
        knowledgeisLikes.value = knowledgeRes.isLikes
        // 收藏
        knowledgeisFavorites.value = knowledgeRes.isFavorites
        // 推荐
        knowledgeisPushes.value = knowledgeRes.isPushes
        // 分享
        knowledgeisShard.value = knowledgeRes.isShard
      }
    }
    if(datasetId) {
      path.value.push({
        id: '',
        datasetId,
        knowledgeId: id,
        fileName: name,
        level: '1'
      })
      queryList()
    }
  }
},{ immediate: true })

const handleSearch = () => {
  queryParams.value.pageNum = 1;
  loadFolderContent(path.value[path.value.length - 1]);
}

const handleReset = () => {
  queryParams.value.fileName = ''
  queryParams.value.pageNum = 1;
  loadFolderContent(path.value[path.value.length - 1]);
}

// 新建文件夹
const handleNewFolder = (type) => {
  clearInterval(timer.value)
  fileList.value.unshift({ id: '-1', fileName: '', type, });
};

// 点击名称文本进入下一级
const handleNameClick = (row) => {
  if (!row.filePrefix) {
    path.value.push({
      ...row,
      knowledgeId: knowledgeId.value,
    });
    loadFolderContent(row);
  }
};

// 点击路径导航返回上一级
const handleBreadcrumbClick = (index) => {
  clearInterval(timer.value)
  if(index === 0) { 
    // router.push({
		// 	path: '/knowledge/publicDoc'
		// })
    emit('pathBack')
  } else {
    path.value = path.value.slice(0, index + 1);
    loadFolderContent(path.value[index]);
    setQueryInterval()
  }
};



// 重命名
const handleRename = (row) => {
  const fileName = row.fileName
  const lastDotIndex = fileName.lastIndexOf('.');
  editName.value = lastDotIndex === -1 ? row.fileName : fileName.substring(0, lastDotIndex);
  if(!row.filePrefix) {
    editName.value = fileName
    row.type = 'folder'
  }
  editId.value = row.id;
  clearInterval(timer.value)
};

// 删除
const handleDelete = async (row) => {
  await message.delConfirm()
  const response = await FolderAPI.deleteFolder(row.id)
  if (row.documentId) {
    await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_data}/documents`,
      method: 'delete',
      headers: {
        Authorization: `${apikeys.ragflow_token}`,
      },
      data: {
        ids: [row.documentId],
      }
    })
  }
  if (response.code === 200) {
    message.success('删除成功')
    handleSearch()
  }
};

// 喜欢
const handleLike = async (row) => {
  const response = await FolderAPI.saveFolderLikes({ klCloudId: row.id, klCloudName: row.fileName })
  if (response.code === 200) {
    message.success('喜欢成功')
    handleSearch()
  }
};

// 取消喜欢
const handleCancelLike = async (row) => {
  const response = await FolderAPI.deleteFolderLikes(row.id)
  if (response) {
    message.success('取消喜欢成功')
    handleSearch()
  }
};

// 收藏
const handleCollect = async (row) => {
  const response = await FolderAPI.saveFolderFavorites({ klCloudId: row.id, klCloudName: row.fileName })
  if (response.code === 200) {
    message.success('收藏成功')
    handleSearch()
  }
};

// 取消收藏
const handleCancelCollect = async (row) => {
  const response = await FolderAPI.deleteFolderFavorites(row.id)
  if (response) {
    message.success('取消收藏成功')
    handleSearch()
  }
};

// 推荐
const handleRecommend = async (row) => {
  const response = await FolderAPI.saveFolderPushes({ klCloudId: row.id, klCloudName: row.fileName })
  if (response.code === 200) {
    message.success('推荐成功')
    handleSearch()
  }
};

// 取消推荐
const handleCancelRecommend = async (row) => {
  const response = await FolderAPI.deleteFolderPushes(row.id)
  if (response) {
    message.success('取消推荐成功')
    handleSearch()
  }
};
const shareListRef = ref()
// 共享文件
const handleShareFile = () => {
  shareListRef.value.open('my')
}

const handleMyShare = () => {
  shareListRef.value.open('other')
}

const handleEditNameConfirm = async (row) => {
  const fileName = row.fileName
  if (!/^[\u4e00-\u9fa5a-zA-Z0-9-]+$/.test(editName.value)) {
    message.warning('名称只能包含中文、英文、数字、-')
    return
  }
  const lastDotIndex = fileName.lastIndexOf('.');
  let editType = lastDotIndex === -1 ? '' : '.' + fileName.substring(lastDotIndex + 1)
  if(row.type === 'folder') {
    editType = ''
  }
  const params = {
    id: row.id === '-1' ? null : editId.value,
    fileName: editName.value + editType,
    parentId: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].id : null,
    filePath: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].filePath + '/' + editName.value : null,
    isCommon: '0',
    datasetId: path.value[path.value.length - 1].datasetId,
    level: path.value[path.value.length - 1].id ? parseInt(path.value[path.value.length - 1].level) + 1 : '1'
  }
  if(row.filePath && !path.value[path.value.length - 1].id) {
    params.filePath = row.filePath.split('/')[0] + '/' + editName.value + editType
  }
  if (row.type !== 'folder') {
    params.filePrefix = editType ? editType : '.docx'
    params.fileKey = uuidv4() + '-klCloud'
  }
  const msgTip = row.type === 'folder' ? '文件夹' : '文件';
  if (editName.value === '') {
    message.error(msgTip + '名称不能为空');
    return;
  }
  try {
    tableLoading.value = true;
    const response = row.id === '-1' ? await FolderAPI.createFolder(params) : await FolderAPI.updateFolder(params);
    if (response.code === 200) {
      editName.value = '';
      editId.value = '-1';
      message.success(row.id === '-1' ? msgTip + '新建成功' : '重命名成功');
      handleSearch()
      setQueryInterval()
    } else {
      message.error(response.msg);
    }
  } catch (error) {
    tableLoading.value = false;
    console.log('handleEditNameConfirm error:', error);
  }
}

const handleEditNameCancel = (row) => {
  if (row.id === '-1') {
    // 删除filelist 第一项
    fileList.value.shift();
  } else {
    editName.value = '';
    editId.value = '-1';
  }
  setQueryInterval()
};

const setQueryInterval = () => {
  timer.value = setInterval(() => {
    loadFolderContent(path.value[path.value.length - 1]);
  }, 10000);
}

// 返回上一级
const handleGoBack = () => {
  if (path.value.length > 1) {
    path.value.pop(); // 移除最后一项
    const currentFolderId = path.value[path.value.length - 1].id; // 获取上一级的文件夹 ID
    loadFolderContent(path.value[path.value.length - 1]); // 加载上一级文件夹内容
  }
};

// 处理操作按钮点击
const handleCommand = (row, command) => {
  console.log('操作:', command, row);
  switch (command) {
    case 'move':
      console.log('移动:', row.name);
      handleMove(row)
      break;
    case 'rename':
      console.log('重命名:', row.name);
      break;
    case 'delete':
      console.log('删除:', row.name);
      handleDelete(row)
      break;
    case 'favorite':
      console.log('收藏:', row.name);
      break;
    case 'share':
      console.log('分享:', row.name);
      break;
    case 'download':
      handleDownload(row)
      console.log('下载:', row.name);
      break;
    case 'view':
      console.log('查看:', row.name);
      break;
    case 'edit':
      console.log('编辑:', row.name);
      break;
    case 'recommend':
      console.log('推荐:', row.name);
      handleRecommend(row);
      break;
    case 'unRecommend':
      console.log('取消推荐:', row.name);
      handleCancelRecommend(row);
      break;
    case 'parse':
      handleParse(row)
      break;
    case 'parseSettings':
      parseSettingsFormRef.value.open(row, 'public');
      break;
    case 'report':
      handleReportView(row)
      break
    default:
      break;
  }
};

const parseTimers = ref(new Map())

// 清除所有定时器的方法
const clearAllTimers = () => {
  parseTimers.value.forEach((timer, id) => {
    clearInterval(timer);
    console.log(`已清除ID为 ${id} 的定时器`);
  });
  parseTimers.value.clear();
};

// 清除单个定时器的方法
const clearParseTimer = (id) => {
  if (parseTimers.value.has(id)) {
    clearInterval(parseTimers.value.get(id));
    parseTimers.value.delete(id);
  }
};

// 解析
const handleParse = async (row) => {
  clearParseTimer(row.id);
  if(!row.fileUrl) {
    message.warning('文件上传中，请稍候');
    return
  }
  if(row.analysisStatus === '1') {
    message.warning('文件解析中，请稍候');
    return
  }
  row.parseLoading = true;
  const params = {
    id: row.id,
    type: '1',
    documentId: row.documentId, 
    klCloudAnalysisId: row.klCloudAnalysisId ? row.klCloudAnalysisId : null
  }
  try {
    const analysResponse = await FolderAPI.getCloudAnalysisBackConfig(params);
    row.analysisStatus = '1';
    const { klCloudAnalysisId, documentId, datasetId, id} = analysResponse.data;
    const _timer = setInterval(async () => {
      const resultResponse= await FolderAPI.getCloudAnalysisBackResultsConfig({
        klCloudAnalysisId,
        datasetId,
        documentId,
        id,
				type: '1',
      })
      if (resultResponse.analysisStatus !== '1') {
        row.analysisStatus = resultResponse.analysisStatus
				row.parseLoading = false;
        clearParseTimer(row.id);
      }
    }, 20000)
		parseTimers.value.set(row.id, _timer);
  } catch {
    clearParseTimer(row.id);
    row.parseLoading = false;
    message.error('解析失败')
  } finally {
  }
  return
  const blobResponse = await knowledgeFileDownload({
    fileUrl: row.fileUrl
  });
  const formData = new FormData();
  formData.append('file', blobResponse, row.fileName);
  formData.append('path', row.fileName)
  fetch(`${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_data}/documents`, {
    method: 'POST',
    body: formData,
    headers: {
      // 如需认证，添加如： 'Authorization': 'Bearer token'
      Authorization: `${apikeys.ragflow_token}`,
    }
  }).then((response) => {
    return response.json();
  }).then(async (Obj) => {
    const { data, code } = Obj
    if (code === 0) {
      const dataSetId = data[0].id
      handleRunDocumentByIds(dataSetId)
      const documentResponse = await handleDocumentList(row.fileName)
      if (documentResponse.code === 0) {
        const documentData = documentResponse.data.docs
        const chunkMethod = documentData && documentData[0] ? documentData[0].chunk_method : '-'
        handleDocumentId(row.id, dataSetId, chunkMethod)
      }
    }
  }).catch((error) => {
    message.error('解析失败！！！')
    console.log(error);
  })
};

const handleDocumentRefresh = async (row) => {
  tableLoading.value = true
  const response = await handleDocumentList(row.fileName)
  tableLoading.value = false
  message.success('刷新成功！！！')
  if (response.code === 0) {
    const documentData = response.data.docs
    const chunkRun = documentData[0]?.run
    if (chunkRun === 'DONE' || 'FAIL') {
      const params = {
        id: row.id,
        analysisStatus: chunkRun === 'DONE' ? '2' : '3',
      }
      const response = await FolderAPI.updateFolder(params)
      handleSearch()
    }
  }
}

const handleDocumentList = async (name) => {
  const response = await axios({
    url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_data}/documents`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `${apikeys.ragflow_token}`,
      Accept: '*/*'
    },
    params: { offset: 1, limit: 10, keywords: name }
  })
  const { data } = response
  return data
};

const handleDocumentId = async (id, documentId, method) => {
  // documentId
  const params = {
    id,
    documentId,
    analysisStatus: '1',
    klCloudAnalysisMethod: method
  }
  const response = await FolderAPI.updateFolder(params)
  handleSearch()
}

const handleRunDocumentByIds = async (id) => {
  try {
    const response = await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_data}/chunks`,
      method: 'post',
      headers: {
        Authorization: `${apikeys.ragflow_token}`,
      },
      data: {
        document_ids: [id]
      }
    })
  } finally {
  }
}

// 处理分享状态变化
const handleShareChange = async (row) => {
  console.log('分享状态变化:', row.name, row.shardStatus);
  try {
    // 修改状态的二次确认
    const text = row.shardStatus === '1' ? '分享' : '取消分享'
    await message.confirm('确认要' + text + '吗?')
    // 发起修改状态
    const response = row.shardStatus === '1' ? await FolderAPI.createCloudShare(row.id) : await FolderAPI.deleteCloudShare(row.id)

    // await FolderAPI.updateUserStatus(row.id, row.shardStatus)
    // 刷新列表
    handleSearch()
  } catch {
    // 取消后，进行恢复按钮
    row.shardStatus =
      row.shardStatus === '0' ? '1' : '0'
  }
  // 这里可以添加分享状态变化的逻辑
};

const handleShare = (row) => {
  console.log('分享:', row.name);
  // 这里可以添加分享逻辑
  shareDialogRef.value.open(row);
}

// 过滤文件列表
// const filteredFileList = computed(() => {
//   if (!searchQuery.value) {
//     return fileList.value;
//   }
//   return fileList.value.filter(item => item.name.includes(searchQuery.value));
// });
const computedStyle = computed(() => {
  if (path.value.length > 1) {
    return 'margin: 0 8px;';
  }
  return 'margin: 0 8px 0 0;';
});

const uploadFormRef = ref();
const UploadMultipleFormRef = ref();

const handleUploadFile = () => {
  console.log('上传文件');
  const nowPath = path.value[path.value.length - 1]
  if (nowPath.id) {
    UploadMultipleFormRef.value.open('0');
  } else {
    message.warning('根目录下不能上传文件！！！')
  }
};

// 下载文件
const handleDownload = async (row) => {
  const { fileUrl, fileName } = row
  if(!fileUrl) {
    message.warning('文件处理中， 请稍后')
    return
  }
  await downloadFileFunction(fileUrl, fileName)
  // window.open(row.fileUrl);
};

const imgViewRef = ref()
const handleView = (row) => {
  console.log('查看:', row.name, path.value[path.value.length - 1]);
  if (!row.fileUrl) {
    message.warning('文件处理中， 请稍后')
    return
  }
  const imgType = ['.jpg', '.jpeg', '.png',]
  const fileType = row.fileUrl.substring(row.fileUrl.lastIndexOf('.'))
  if (imgType.includes(fileType)) {
    imgViewRef.value.open(row)
  } else {
    docViewRef.value.open('view', path.value[path.value.length - 1], row, true, '维护知识库');
  }
};

const handleEdit = (row) => {
  console.log('编辑:', row.name);
  // if (row.filePrefix !== '.docx') {
  //   message.warning('暂时仅支持在线编辑docx文件！！！')
  //   return
  // }
  if(!row.fileUrl) {
    message.warning('文件处理中， 请稍后')
    return
  }
  docViewRef.value.open('update', path.value[path.value.length - 1], row, true, '维护知识库');
}

const handleNewFile = () => {
  console.log('新建文件');
  docViewRef.value.open('create', path.value[path.value.length - 1]);
}

const handleMove = (row) => {
  console.log('移动:', row.name);
  moveDialogRef.value.open(row);
}

const reportViewRef = ref();
// 查看报告
const handleReportView = (row) => {
  console.log('查看报告:', row.name);
  reportViewRef.value.open(row);
}
const knowledgeDialogRef = ref()
// 搜索知识库
const handleSearchKnowledge = () => {
  // if (!queryParams.value.fileName) {
  //   message.warning('请输入搜索内容')
  //   return
  // }
  handleSearch();
  // knowledgeDialogRef.value.open(
  //   queryParams.value.fileName,
  //   {
  //     ragflow_url: apikeys.ragflow_url,
  //     ragflow_token: apikeys.ragflow_token,
  //     kl_cloud_data: apikeys.kl_cloud_data
  //   }
  // )
}

const timer = ref()

// 初始化加载根目录
// onMounted(() => {
//   const { id, name, datasetId } = route.query
//   if(datasetId) {
//     path.value.push({
//       id: '',
//       datasetId,
//       fileName: name,
//       level: '1'
//     })
//   }
//   handleSearch();
//   // 开启定时器
//   // setQueryInterval()
// });
onUnmounted(() => {
  clearInterval(timer.value)
  clearAllTimers();
});
// 销毁时清楚定时器
onBeforeUnmount(() => {
  clearInterval(timer.value)
});
</script>

<style scoped lang="scss">
.align-center {
  align-items: center;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.breadcrumb-wrapper {
  display: flex;
  height: 48px;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 10px;

  .back-button {
    padding: 0;
    font-size: 14px;
  }
}

.name-column {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .name-wrapper {
    width: 400px;
  }

  .name-text {
    cursor: pointer;
    align-items: center;
    display: flex;

    &:hover {
      color: #409eff;
    }
  }

  .more-actions {
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-left: 12px;

    &:hover {
      color: #409eff;
    }
  }
}

.button-group {
  flex: 1;
  display: flex;
  align-items: center;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 320px;
  display: inline-block;
}
</style>