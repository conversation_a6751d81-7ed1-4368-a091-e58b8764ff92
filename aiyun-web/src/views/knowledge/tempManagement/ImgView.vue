<template>
  <Dialog v-model="dialogVisible" :destroy-on-close="true"
    title="图片预览">
    <div class="content">
      <img :src="dialogUrl" alt="" />
    </div>
  </Dialog>
</template>
<script setup>

defineOptions({ name: 'SystemUserForm' })
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogUrl = ref('')

/** 打开弹窗 */
const open = async (row) => {
  dialogUrl.value = row.templateFileUrl
  dialogVisible.value = true
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

</script>

<style lang="scss" scoped>
.content {
  padding: 10px;
}
</style>
