import request from '@/config/axios'

// 查询文件列表分页 v1/kownledge/klArrangeChat/page
export const getArrangeChatPage = (params) => {
  return request.get({ url: '/v1/kownledge/klArrangeChat/page', params })
}

// 新增
export const addArrangeChat = (data) => {
  return request.post({ url: '/v1/kownledge/klArrangeChat', data })
}

// 新增
export const updateArrangeChat = (data) => {
  return request.put({ url: '/v1/kownledge/klArrangeChat', data })
}

// 删除
export const deleteArrangeChat = (id) => {
  return request.delete({ url: `/v1/kownledge/klArrangeChat/${id}` })
}

// 删除
export const getArrangeChat = (id) => {
  return request.get({ url: `/v1/kownledge/klArrangeChat/${id}` })
}

