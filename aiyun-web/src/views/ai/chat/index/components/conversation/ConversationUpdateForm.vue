<template>
  <Dialog title="设定" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="130px" v-loading="formLoading">
      <el-form-item label="角色设定" prop="systemMessage">
        <el-input type="textarea" v-model="formData.systemMessage" rows="4" placeholder="请输入角色设定" />
      </el-form-item>
      <el-form-item label="会话类型" prop="chatType">
        <el-radio-group v-model="formData.chatType" @change="handleChatTypeChange">
          <el-radio-button value="model">模型</el-radio-button>
          <el-radio-button value="rag">RAG</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="formData.chatType === 'model' ? '模型' : 'RAG名称'" prop="modelId">
        <el-select v-model="formData.modelId" :placeholder="formData.chatType === 'model' ? '请选择模型' : '请选择RAG'"
          filterable clearable :filter-method="filterOptions" :remote="false">
          <el-option v-for="item in filteredList" :key="item.modelId"
            :label="formData.chatType === 'model' ? item.modelName : item.baseName" :value="item.modelId" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="温度参数" prop="temperature">
        <el-input-number v-model="formData.temperature" placeholder="请输入温度参数" :min="0" :max="2" :precision="2" />
      </el-form-item>
      <el-form-item label="回复数 Token 数" prop="maxTokens">
        <el-input-number v-model="formData.maxTokens" placeholder="请输入回复数 Token 数" :min="0" :max="4096" />
      </el-form-item>
      <el-form-item label="上下文数量" prop="maxContexts">
        <el-input-number v-model="formData.maxContexts" placeholder="请输入上下文数量" :min="0" :max="20" />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CommonStatusEnum } from '@/utils/constants'
import { ChatModelApi, ChatModelVO } from '@/api/ai/model/chatModel'
import { ChatConversationApi, ChatConversationVO } from '@/api/ai/chat/conversation'
import { getArrangeModelPage } from '@/api/knowledge/aiSettings/model'
import { getklklArrangeBasePage } from '@/api/knowledge/aiSettings/knowledge'

/** AI 聊天对话的更新表单 */
defineOptions({ name: 'ChatConversationUpdateForm' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  systemMessage: undefined,
  chatType: 'model', // 默认选择模型类型
  modelId: undefined,
  temperature: undefined,
  maxTokens: undefined,
  maxContexts: undefined
})
const formRules = reactive({
  chatType: [{ required: true, message: '会话类型不能为空', trigger: 'change' }],
  modelId: [{ required: true, message: '模型/RAG不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  temperature: [{ required: true, message: '温度参数不能为空', trigger: 'blur' }],
  maxTokens: [{ required: true, message: '回复数 Token 数不能为空', trigger: 'blur' }],
  maxContexts: [{ required: true, message: '上下文数量不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const modelList = ref([] as ChatModelVO[]) // 模型列表
const ragList = ref([] as any[]) // RAG列表
const filteredList = ref([] as any[]) // 过滤后的列表

/** 过滤选项列表 */
const filterOptions = (query: string) => {
  if (query) {
    // 根据输入的关键字过滤列表
    const sourceList = formData.value.chatType === 'model' ? modelList.value : ragList.value
    const field = formData.value.chatType === 'model' ? 'modelName' : 'baseName'
    filteredList.value = sourceList.filter(item =>
      item[field].toLowerCase().includes(query.toLowerCase())
    )
  } else {
    // 如果没有输入关键字，显示全部
    filteredList.value = formData.value.chatType === 'model' ? modelList.value : ragList.value
  }
}

/** 处理会话类型变更 */
const handleChatTypeChange = () => {
  formData.value.modelId = undefined // 清空选择
  filteredList.value = formData.value.chatType === 'model' ? modelList.value : ragList.value
}

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  resetForm()

  // 获取模型列表
  const { records: modelRecords } = await getArrangeModelPage({
    pageNum: 1,
    status: '0',
    pageSize: 1000
  })
  modelList.value = modelRecords

  // 获取RAG列表
  const { records: ragRecords } = await getklklArrangeBasePage({
    pageNum: 1,
    pageSize: 1000
  })
  ragList.value = ragRecords

  // 初始化过滤后的列表为模型列表
  filteredList.value = modelList.value

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await ChatConversationApi.getChatConversationMy(id)
      formData.value = Object.keys(formData.value).reduce((obj, key) => {
        if (data.hasOwnProperty(key)) {
          obj[key] = data[key]
        }
        return obj
      }, {})
      // 根据获取的数据设置正确的选项列表
      filteredList.value = formData.value.chatType === 'model' ? modelList.value : ragList.value
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ChatConversationVO
    await ChatConversationApi.updateChatConversationMy(data)
    message.success('对话配置已更新')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    systemMessage: undefined,
    chatType: 'model', // 重置为默认模型类型
    modelId: undefined,
    temperature: undefined,
    maxTokens: undefined,
    maxContexts: undefined
  }
  formRef.value?.resetFields()
}
</script>
