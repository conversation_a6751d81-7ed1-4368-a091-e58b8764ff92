<template>
    <el-dialog 
      v-model="dialogVisible" 
      fullscreen
      :close-on-click-modal="false"
      title="文档生成">
			<div class="chat-list-dialog">
        <NewTem ref="NewTemRef" :isAdmin="isAdmin" />
      </div>
    </el-dialog>
  </template>
  <script setup>

	import NewTem from '@/views/knowledge/tempManagement/newTem.vue'
  
  defineOptions({ name: 'DocListDialog' })

  const emit = defineEmits(['success'])

  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  
  const dialogVisible = ref(false) // 弹窗的是否展示

	const NewTemRef = ref(null)
  const isAdmin = ref(false)
  /** 打开弹窗 */
  const open = async (type = false) => {
    console.log(type)
		dialogVisible.value = true;
    isAdmin.value = type
  }

  defineExpose({ open }) // 提供 open 方法，用于打开弹窗
  
  </script>
  
  <style lang="scss" scoped>
  .chat-list-dialog {
    height: 600px; 
    position: relative;
    background-color: var(--el-color-white);
  }
  </style>
