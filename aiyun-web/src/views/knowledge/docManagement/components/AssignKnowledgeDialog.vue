<template>
  <Dialog v-model="dialogVisible"  width="500" title="分配权限">
    <!-- 左侧角色列表 -->
    <el-form
      ref="formRef"
      class="form-container"
      v-loading="loading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      label-position="top"
    >
      <el-form-item  label="权限设置" prop="disclosure">
        <el-radio-group v-model="formData.disclosure">
          <el-radio key="true" :value="'3'" border>公开</el-radio>
          <el-radio key="false" :value="'2'" border>角色</el-radio>
          <el-radio key="3" :value="'1'" border>用户</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-input
        v-model="filterText"
        v-show="formData.disclosure === '1'"
        placeholder="请输入用户名称"
      /> -->
      <el-scrollbar height="300px">
        <div v-show="formData.disclosure === '3'">
          <el-tree
            ref="publicTreeRef"
            style="max-width: 460px"
            :data="publicPermissionTree"
            show-checkbox
            node-key="id"
            highlight-current
            :props="defaultProps"
          />
        </div>
        <div v-show="formData.disclosure === '2'">
          <el-tree
            ref="roleTreeRef"
            style="max-width: 460px"
            :data="rolePermissionTree"
            show-checkbox
            node-key="id"
            highlight-current
            :props="defaultProps"
          />
        </div>
        <div v-show="formData.disclosure === '1'">
          
          <el-tree
            ref="userTreeRef"
            style="max-width: 460px"
            :data="userPermissionTree"
            show-checkbox
            node-key="id"
            highlight-current
            :props="defaultProps"
          />
        </div>
      </el-scrollbar>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
    </template>
  </Dialog>
</template>

<script setup>
import { ref } from 'vue'
import { getRolePage } from '@/api/system/role'
import { useMessage } from '@/hooks/web/useMessage'
import * as UserApi from '@/api/system/user'
import { saveCloudButtonRole, getCloudButtonRole, deleteCloudButtonRole } from '@/api/knowledge/docPersonal'
import { createKlDatasetPermission, getKlDatasetPermissionById } from '@/api/knowledge/docmangement'
const emit = defineEmits(['success'])

const message = useMessage()
const dialogVisible = ref(false)
const roleList = ref([])
// const selectedRoles = ref([])
const currentRow = ref(null)
const loading = ref(false)
// const filterText = ref('')
const operations = ref([])

// 公共角色权限列表
const baseOperations = [
  { id: 'upload', name: '上传文件', },
  { id: 'createFolder', name: '新建文件夹', },
  { id: 'createDoc', name: '新建文档', },
  { id: 'createXlxs', name: '新建表格', },
  { id: 'createPpt', name: '新建幻灯片', },
]

// watch(filterText, (val) => {
//   userTreeRef.value && userTreeRef.value.filter(val)
// })

// const filterNode = (value, data, node) => {
//   if (!value) return true
//   return node.level === 1 && data.name.includes(value)
// }

const getRoleOptions = (baseItem,) => {
  return baseOperations.map((item) => {
    return {
      ...item,
      id: `${item.id}-public-role-${baseItem.id}`,
      name: `${item.id}-public-role-${baseItem.id}`,
      buttonCode: `${item.id}`,
      bottomName: item.name,
      baseId: baseItem.id,
      type: '2',
      level: '2'
    }
  })
}

const getUserOptions = (baseItem,) => {
  return baseOperations.map((item) => {
    return {
      ...item,
      id: `${item.id}-public-user-${baseItem.id}`,
      name: `${item.id}-public-user-${baseItem.id}`,
      buttonCode: `${item.id}`,
      bottomName: item.name,
      baseId: baseItem.id,
      type: '1',
      level: '2'
    }
  })
}

const publicPermissionTree = computed(() => {
  return baseOperations.map((item) => {
    return {
      ...item,
      id: `${item.id}-public-public`,
      name: `${item.id}-public-public`,
      buttonCode: `${item.id}`,
      bottomName: item.name,
      type: '3'
    }
  })
})

const rolePermissionTree = computed(() => {
  return roleList.value.map((role) => {
    return {
      ...role,
      id: `${role.id}`,
      bottomName: role.name,
      level: '1',
      children: getRoleOptions(role,)
    }
  })
})

const userPermissionTree = computed(() => {
  return userList.value.map((role) => {
    return {
      ...role,
      id: `${role.id}`,
      bottomName: role.nickname,
      level: '1',
      children: getUserOptions(role, )
    }
  })
})


const defaultProps = {
  children: 'children',
  label: 'bottomName',
}

const searchLoading = ref(false)

const formRef = ref(null)

const formData = ref({
  disclosure: null
})
const formRules = ref({
  disclosure: [
    { required: true, message: '请选择权限设置', trigger: 'change' }
  ],
})

// 获取角色列表
const getRoleList = async () => {
  try {
    const res = await getRolePage({
      pageNo: 1,
      pageSize: 100
    })
    roleList.value = res.list
  } catch (error) {
    console.error('获取角色列表失败:', error)
    message.error('获取角色列表失败')
  }
}

const userList = ref([])
// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getAllUser()
    userList.value = data
  } finally {
    loading.value = false
  }
}

const treeRef = ref(null)
const publicTreeRef = ref(null)
const roleTreeRef = ref(null)
const userTreeRef = ref(null)
// 打开弹窗
const open = async (row) => {
  currentRow.value = row ? row : { id: 0 }
  dialogVisible.value = true
  // 重置选择
  // selectedRoles.value = []
  searchLoading.value = true
  await getRoleList()
  await getUserList()
  await getRolePermission()
  searchLoading.value = false
}

const rolePermissionList = ref([])
const rolePermissionIds = ref([])

// 获取知识库权限
const getRolePermission = async () => {
  try {
    const res = await getKlDatasetPermissionById({
      datasetId: currentRow.value ? currentRow.value.datasetId : ''
    })
    const permissionList = res[0]?.permission || []
    const type = permissionList[0]?.type
    const checkIds = permissionList.map((item) => {
      return item.buttonName
    })
    if (permissionList.length > 0) {
      formData.value.disclosure = type
      if(type === '1') {
        userTreeRef.value.setCheckedKeys(checkIds)
      } else if(type === '2') {
        roleTreeRef.value.setCheckedKeys(checkIds)
      } else if(type === '3') {
        publicTreeRef.value.setCheckedKeys(checkIds)
      }
    }
  } catch (error) {
    console.error('获取角色权限失败:', error)
    message.error('获取角色权限失败')
  }
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  const disclosure = formData.value.disclosure
  let checkedNodes = []
  if (disclosure === '3') {
    checkedNodes = publicTreeRef.value.getCheckedNodes()
  } else if (disclosure === '2') {
    checkedNodes = roleTreeRef.value.getCheckedNodes()
  } else if (disclosure === '1') {
    checkedNodes = userTreeRef.value.getCheckedNodes()
  }
  const selectedRoles = checkedNodes.filter((node) => node.id.includes('-public-'))
  const permission = selectedRoles.map((item) => {
    return {
      type: disclosure,
      buttonCode: item.buttonCode,
      buttonName: item.name,
      id: item.baseId,
    }
  })
  if(permission.length === 0) {
    permission.push({
      type: disclosure,
    })
  }
  const params = {
    datasetId: currentRow.value.datasetId,
    permission,
  }
  try {
    loading.value = true
    const response = await createKlDatasetPermission(params)
    dialogVisible.value = false
    emit('success')
    message.success('保存权限成功')
  } catch (error) {
    console.error('保存权限失败:', error)
    message.error('保存权限失败')
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.card-col {
  height: 100%;
}

.el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-card__body) {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.checkbox-group {
  height: 100%;
  min-height: 200px;
  overflow-y: auto;
}

.checkbox-item {
  margin: 10px 0;
  display: flex;
  align-items: center;
}

.role-name {
  margin-left: 8px;
  flex: 1;
  font-size: 14px;
  cursor: pointer;
}
</style>