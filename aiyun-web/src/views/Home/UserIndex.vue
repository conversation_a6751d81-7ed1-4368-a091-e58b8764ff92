<template>
	<div class="home-content">
		<div class="home-left" >
			<div class="home-logo">
				<!-- <img src="@/assets/ai/ai.png" alt="" />
				<p>张三李</p> -->
					<ElAvatar :src="avatar" alt="" class="w-[calc(var(--logo-height)-25px)] rounded-[50%]" />
					<p>
						{{ userName }}
					</p>
			</div>
			<div class="home-menu">
				<el-menu
					default-active="1"
					class="el-menu-vertical-demo"
					:collapse="true"
					active-text-color="#d4f0f6"
        	background-color="#1c58c6"
					text-color="#fff"
					>
<!--          工作台、智能书写、智能书写、知识管理、智能助手-->
					<el-menu-item index="1">
						<div class="left-menu-item">
							<Icon icon="fa:briefcase" :size="28" />
							<p>工作台</p>
						</div>
					</el-menu-item>
					<el-menu-item index="2" @click="handleMoreDocs">
						<div class="left-menu-item">
							<Icon icon="fa:lightbulb-o" :size="28" />
							<p>智能书写</p>
						</div>
					</el-menu-item>
					<el-menu-item index="3" @click="handleCreate('knowledge')">
						<div class="left-menu-item">
							<Icon icon="fa:university" :size="28" />
							<p>知识管理</p>
						</div>
					</el-menu-item>
					<el-menu-item index="4"  @click="handleMoreChats">
						<div class="left-menu-item">
							<Icon icon="fa:magic" :size="28" />
							<p>智能对话</p>
						</div>
					</el-menu-item>
				</el-menu>
			</div>
		</div>
		<div class="home-right">
			<div class="right--top-bg">
				<div class="home-right-top">
					<el-menu
						background-color="#fff"
						text-color="#3c4550"
						active-text-color="#3c4550"
						class="my-menu"
						mode="horizontal"
						:ellipsis="false"
					>
						<el-menu-item index="0" disabled class="menu-logo">
							<img
								src="@/assets/imgs/gw.png"
								class="h-32px w-32px mr-5px"
								alt="Element logo"
							/>知识精准搜索和报告智能书写平台
						</el-menu-item>
						<el-menu-item index="3">
							<template #title><Icon icon="ep:connection" />行业动态</template>
						</el-menu-item >
					</el-menu>
					<div class="right-user">
						<UserInfo :isShowUser="true" />
					</div>
				</div>
			</div>
			<div class="home-right-content">
				<div class="hr-search">
					<div class="search-header flex items-center">
						<!-- enter键 搜索 -->
						<el-input 
							v-model="searchKeyword" 
							size="large" 
							clearable
							placeholder="知识库检索" 
							ref="searchInputRef"
							class="flex-1 input-with-select"
							@focus="handleSearchFocus"
							@keyup.enter="handleSearch"
						>
							<template #append>
								<div class="search-append">
									<div class="flex items-center justify-center w-40px">
										<el-button type="primary" @click="handleSearch">
											<Icon icon="ep:search" :size="16" />
										</el-button>
									</div>
								</div>
							</template>
							<template #prepend>
								<el-select v-model="searchType" size="large" placeholder="知识搜索">
									<el-option label="知识搜索" value="" />
									<el-option label="主题" value="topic" />
									<el-option label="作者" value="creator" />
									<el-option label="关键词" value="keyword" />
								</el-select>
							</template>
						</el-input>
					</div>
					<div v-show="isSearchFocused" ref="searchViewRef" class="search-dropdown" @mousedown.stop @click.stop>
						<el-scrollbar max-height="720px" min-height="100px">
							<div v-show="!showSearchView" >
								<div class="mb-20px pr-10px">
									<div class="doc-types">
										<div class="type-list">
											<el-checkbox-group v-model="checkValue">
												<el-checkbox 
													class="checkbox" 
													v-for="(item, index) in checkList"
													:key="index"
													:label="item.name" 
													:value="item.name" />
											</el-checkbox-group>
										</div>
									</div>
									<div class="history-header">
										<span>搜索历史</span>
										<el-button link type="primary" @click="clearHistory">
											<el-icon><Delete /></el-icon>
										</el-button>
									</div>
									
									<div class="history-list">
										<div v-for="(item, index) in searchHistoryList" @click.stop="handleHistoryClick(item)" :key="index">
											<div class="history-item">
												<el-icon><Clock /></el-icon>
												<span class="history-text">{{ item.content }}</span>
												<el-icon class="delete-icon" @click.stop="removeHistory(item.id)"><Close /></el-icon>
											</div>
										</div>
										<div v-for="(item, index) in searchDocList" :key="index">
											<div class="history-doc" @click="handleHistoryDocClick(item)">
												<el-icon><Document color="#1790ff" /></el-icon>
												<div class="doc-desc">
													<p class="doc-text-title">{{ item.fileName }}</p>
													<p class="doc-text-time">{{ item.createTime }} &nbsp;&nbsp;你在 {{ item.serviceType }}&nbsp;&nbsp; {{ filterEventType(item.eventType) }}了这篇文档</p>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div 
								v-show="showSearchView" 
								class="search-view">
									<SearchView ref="searchResultRef" />
							</div>
						</el-scrollbar>
					</div>
				</div>
				<el-scrollbar height="calc(100vh - 110px)">
					<div class="home-center-content">
						<div class="home-center-title">
							<Icon icon="ep:folder-opened" class="mr-[5px]"/>
							<div class="title-text">智能工作台</div>
						</div>
						<el-row :gutter="16">
							<el-col :span="8">
								<div class="hr-item">
									<div class="hr-item-title">
										<span>智能书写</span>
								<el-button
										type="primary"
										link
										size="small"
										@click="handleMoreDocs"
										>更多 <Icon icon="ep:d-arrow-right" class="ml-[5px]" /></el-button>

									</div>
									<div class="hr-item-content">
										<el-scrollbar height="260px">
											<!-- <div class="rank-list" v-if="generationList.length">
												<div 
													v-for="(item, index) in generationList" 
													:key="index"
													@click="handleView(item)"
													class="rank-item"
												>
													<span :class="['rank-number', index < 3 ? 'top-three' : '']">{{ index + 1 }}</span>
													<span class="rank-title">{{ item.fileName }}</span>
												</div>
											</div>
											<div class="empty-tips" style="padding-bottom: 32px;" v-else>暂无数据</div> -->
											<div class="tool-list">
												<div class="tool-item" @click="handleNewFile">
                          <img src="@/assets/icon_app/kaishichuangzuo.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>开始创作</span>
												</div>
												<div class="tool-item" @click="openTempDocments">
													<img src="@/assets/icon_app/cankaomoban.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>参考模板</span>
												</div>
											</div>
										</el-scrollbar>
									</div>
				
								</div>
							</el-col>
							<el-col :span="8">
								<div class="hr-item">
									<div class="hr-item-title">
										<span>智能对话</span>
										<el-button 
											type="primary"
											link
											size="small"
											@click="handleMoreChats"
											>更多<Icon icon="ep:d-arrow-right" class="ml-[5px]" /></el-button>
									</div>
									<el-skeleton :loading="loading" animated>
										<el-scrollbar height="260px">
											<div v-if="!isConversationEmpty">
												<div 
													class="chat-content"  
													v-for="conversationKey in Object.keys(conversationMap)"
													:key="conversationKey">
													<div class="chat-title">{{ conversationKey }}</div>
													<div class="chat-name" >
														<span 
														v-for="conversation in conversationMap[conversationKey]" 
														class="mr-10px"
														@click="handleChooseChat(conversation)"
														:key="conversation.id">
														<el-tag>{{ conversation.name }}</el-tag>
													</span>
													</div>
												</div>
											</div>
											<div class="empty-tips" v-else>
												暂无对话
											</div>
										</el-scrollbar>
									</el-skeleton>
								</div>
							</el-col>
							<el-col :span="8">
								<div class="hr-item">
									<div class="hr-item-title">
										<span class="known-title">
											常用工具
										</span>
									</div>
									<div class="hr-item-content">
										<el-scrollbar height="260px">
											<div class="tool-list">
												<div class="tool-item" @click="handleCreate('knowledge')">
													<!-- <Icon icon="fa:database" class="mb-[5px]" :size="14"/> -->
													 <img src="@/assets/icon_app/zhishiku.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>维护知识库</span>
												</div>
												<div class="tool-item" @click="handleCreate('docx')">
													<!-- <Icon icon="fa:file-word-o" class="mb-[5px]" :size="14"/> -->
													 <img src="@/assets/icon_app/xinjianwendang.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>新建文档</span>
												</div>
												<div class="tool-item" @click="handleCreate('xlsx')">
													<!-- <Icon icon="fa:file-excel-o" class="mb-[5px]" :size="14"/> -->
													 <img src="@/assets/icon_app/xinjianbiaoge.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>新建表格</span>
												</div>
												<div class="tool-item" @click="handleCreate('pptx')"> 
													<!-- <Icon icon="fa:file-excel-o" class="mb-[5px]" :size="14"/> -->
													 <img src="@/assets/icon_app/xinjianhuandengpian.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>新建幻灯片</span>
												</div>
												<div class="tool-item" @click="handleCreate('upload')"> 
													<!-- <Icon icon="fa:file-excel-o" class="mb-[5px]" :size="14"/> -->
													 <img src="@/assets/icon_app/shangchuanwendang.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>上传文件</span>
												</div>
												<div class="tool-item" v-if="!isSuperAdmin" @click="toProfile"> 
													<!-- <Icon icon="ep:tools" class="mb-[5px]" :size="14"/> -->
													 <img src="@/assets/icon_app/gerenzhognxin.png" alt="" class="h-58px w-58px rounded-[8px]" style="margin-bottom: 2px;"/>
													<span>个人中心</span>
												</div>
											</div>
                    </el-scrollbar>
									</div>
								</div>
							</el-col>
						</el-row>
					</div>
					<div class="home-center-content">
						<div class="home-center-title">
							<Icon icon="ep:folder-opened" class="mr-[5px]"/>
							<div class="title-text">知识库</div>
						</div>
						<el-row :gutter="16">
              <el-col :span="8">
                <div class="hr-item">
                  <div class="hr-item-title">
										<span class="known-title">
											知识排行
											<Icon icon="fa:bar-chart" class="ml-[4px] mt-[2px]" />
										</span>
                    <el-dropdown @command="handleCommand">
					<b class="el-dropdown-link">
						<Icon :icon="rankSelect.icon" class="mr-[4px]"/>
						{{ rankSelect.label }}
						<Icon icon="ep:arrow-down" class="ml-[8px]"/>
					</b>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
                            v-for="(item, index) in rankTypeOptions"
                            :key="index"
                            :command="item.value">
                            <Icon :icon="item.icon ? item.icon : 'ep:aim'" class="mr-[5px]" />{{ item.label }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                  <div class="hr-item-content">
                    <el-scrollbar height="360px">
                      <div class="rank-list" v-if="rankList.length">
                        <div
                          v-for="(item, index) in rankList"
                          :key="index"
                          @click="handleView(item)"
                          class="rank-item"
                        >
                          <span :class="['rank-number', index < 3 ? 'top-three' : '']">{{ index + 1 }}</span>
                          <span class="rank-title">{{ item.fileName }}</span>
                        </div>
                      </div>
                      <div class="empty-tips" style="padding-bottom: 32px;" v-else>暂无数据</div>
                    </el-scrollbar>
                  </div>
                </div>
              </el-col>
							<el-col :span="8">
								<div class="hr-item">
									<div class="hr-item-title">
										<span>学习园地</span>
										<el-button 
										type="primary"
										link
										size="small"
										@click="handleLearn"
										>设置 <Icon icon="ep:setting" class="ml-[5px]" /></el-button>
									</div>
									<div class="hr-item-content">
										<el-scrollbar height="360px">
											<el-skeleton :loading="learnloading" animated>
												<div v-if="learnList && learnList.length" class="rank-list">
													<div 
														v-for="(item, index) in learnList" 
														:key="index"
														@click="handleView(item)"
														class="rank-item"
													>
														<span class="rank-title">{{ item.fileName }}</span>
													</div>
												</div>
												<div v-else class="empty-tips" style="padding-bottom: 32px;">暂无数据</div>
											</el-skeleton>
										</el-scrollbar>
									</div>
				
								</div>
							</el-col>
							<el-col 
								v-for="(item, index) in knowledgeList" 
								:style="index > 0 ? { marginTop: '10px' } : {}"
								:key="index" 
								:span="8">
								<div class="hr-item">
									<div class="hr-more" @click="handleCreate('knowledgeItem', item)">
										<Icon icon="ep:setting" :size="16" />
									</div>
									<KnowledgeItem 
										:knowledgeName="item.name" 
										:datasetId="item.datasetId"
										@tag-click="tagClick"
										/>
									<!-- <div class="hr-item-content">
										<el-scrollbar height="360px">
										</el-scrollbar>
									</div> -->
								</div>
							</el-col>
						</el-row>
					</div>
				</el-scrollbar>
			</div>
		</div>
		<FileWayDialog ref="FileWayDialogRef" @success="getFileWay" />
		<CreatKnowledge ref="creatKnowledgeRef" @close="handleCloseDialog" />
		<CreatFile ref="creatFileRef" @success="createFileSuccess"/>
		<ChatMessageDialog ref="ChatMessageDialogRef" />
		<ChatListDialog ref="ChatListDialogRef" />
		<DocListDialog ref="docListDialogRef" @success="getdocsList" />
		<DocView ref="docViewRef" />
		<MyDocumentList ref="myDocumentListRef" />
		<AgentAnswer />
		<TagDocments ref="tagDocmentsRef" @docment-click="handleView"/>
		<TempDocments ref="TempDocmentsRef" @docment-click="handleView"/>
		<HomeProfile ref="homeprofileRef" />
		<LearnForm ref="learnFormRef" @success="handleLearnSearch" />
		<CreatAIFile ref="creatAIFileRef" @success="createAIFileSuccess" />
		<UploadForm 
			ref="uploadFormRef" 
			:path="choosePath" 
			:create-folder="FolderAPI.createFolder" 
			:update-folder="FolderAPI.updateFolder" 
			/>
	</div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
	import avatarImg from '@/assets/imgs/logo_1.png'
  import { ChatConversationVO } from '@/api/ai/chat/conversation'
  import { getKlModelSession, } from '@/api/knowledge/model'
  import {More, Delete, Clock, Close, Document, Connection} from '@element-plus/icons-vue'
  import * as homeApi from '@/api/home/<USER>'
  import * as TemplateAPI from '@/api/knowledge/createTem'
  import * as FolderAPI from '@/api/knowledge/docmangement'
	import { uploadInit } from '@/api/knowledge/docmangement'
  import SearchView from './components/SearchView.vue'
  import FileWayDialog from './components/FileWayDialog.vue'
  import DocView from '@/views/knowledge/docManagement/components/DocView.vue'
  import { useTagsViewStore } from '@/store/modules/tagsView'
  import CreatKnowledge from './components/CreatKnowledge.vue'
  import CreatFile from './components/CreatFile.vue'
  import ChatMessageDialog from './components/ChatMessageDialog.vue'
  import ChatListDialog from './components/ChatListDialog.vue'
  import DocListDialog from './components/DocListDialog.vue'
  import { useUserStoreWithOut } from '@/store/modules/user'
  import MyDocumentList from './components/MyDocumentList.vue'
  import KnowledgeItem from './components/KnowledgeItemNew.vue'
  import AgentAnswer from '@/components/AgentAnswer/index.vue'
  import TagDocments from './components/TagDocments.vue'
	import TempDocments from './components/TempDocments.vue'
  import HomeProfile from './components/HomeProfile.vue'
	import LearnForm from './components/LearnForm.vue'
	// src\views\knowledge\tempManagement\components\CreatFile.vue
	import CreatAIFile from '@/views/knowledge/tempManagement/components/CreatFile.vue';
  import UploadForm from '@/views/knowledge/docManagement/components/UploadForm.vue'
  // import { personKnowledge, specialKnowledge, publicKnowledge } from './components/data'
 import { UserInfo } from '@/layout/components/UserInfo'
  import type { Shortcut } from './types'
  
  defineOptions({ name: 'Home' })

  const { t } = useI18n()
  const { replace } = useRouter()
  const message = useMessage() // 消息弹窗

	
  const tagsViewStore = useTagsViewStore()
  
  const userStore = useUserStoreWithOut()
	const apikeys = userStore.getAppConfig
	const avatar = computed(() => userStore.user.avatar || avatarImg)
	const userName = computed(() => userStore.user.nickname ?? 'Admin')
  const roles = userStore.getRoles
  const loginUser = userStore.getUser
  const isSuperAdmin = roles.includes('super_admin')

  const searchViewParams = computed(() => {
    return {
      searchType: searchType.value,
      searchMessage: searchMessage.value,
      checkValue: checkValue.value,
    }
  })
  
  const loading = ref(true)
  
  // 搜索相关
  const searchType = ref('')
  const searchKeyword = ref('')

  // tab
  const activeName = ref('docs')

  const knowledgeList = ref([])
  const searchResultRef = ref()
  
  const filterEventType = (item: any) => {
    // 1增加2删除3修改4查看
    switch (item) {
      case '1':
        return '新增'
      case '2':
        return '删除'
      case '3':
        return '修改'
      case '4':
        return '查看'
      default:
        return ''
    }
  }
  
  // 知识排行相关
  const currentRankTab = ref('week')
	const rankSelect = ref({ label: '收藏', value: 'collect', icon: 'fa:star-o' })

	const rankTypeOptions = [
		{ label: '收藏', value: 'collect', icon: 'fa:star-o' },
		{ label: '下载', value: 'download', icon: 'ep:download' },
		{ label: '点赞', value: 'like', icon: 'fa:heart' },
		{ label: '推荐', value: 'recommend', icon: 'ep:promotion' },
	]

	const handleCommand = (command: string) => { 
		getHomeDownloadCountList(command)
		rankSelect.value = rankTypeOptions.find(item => item.value === command)
		// switch (command) {
		// 	case 'download':
		// 		rankSelect.value = '下载'
		// 		break
		// 	case 'collect':
		// 		rankSelect.value = '收藏'
		// 		break
		// 	case 'like':
		// 		rankSelect.value = '点赞'
		// 		break
		// 	case 'recommend':
		// 		rankSelect.value = '推荐'
		// 		break
		// 	default:
		// 		break
		// }
  }

  
  const rankList = ref([])
  
  const FileWayDialogRef = ref(null)
  const handleCreate = (type: string, item: any = null) => {
		console.log(type);
		
    if (type === 'knowledge') {
      creatKnowledgeRef.value && creatKnowledgeRef.value.open()
    } else if(type === 'knowledgeItem') {
			console.log(item);
			
			creatKnowledgeRef.value && creatKnowledgeRef.value.open(true, item)
		} else {
      FileWayDialogRef.value && FileWayDialogRef.value?.open(type);
    }
  }

	const rankRequestApi = {
		download: homeApi.getHomeDownloadCount,
		collect: homeApi.getHomeFavoritesCount,
		like: homeApi.getHomeLikesCount,
		recommend: homeApi.getHomePushesCount
	}
  
  // 获取数据
  const getHomeDownloadCountList = async(command: string) => {
    const response = await rankRequestApi[command]()
    rankList.value = response
  }

  // 获取知识库首页
  const getHomeKlDatset = async () => {
    knowledgeList.value = await homeApi.getHomeKlDatset()    
  }
  
  const klCloudcount = ref(0)
  const klDoccount = ref(0)
  const klModelSessioncount = ref(0)
  const getHomeCount = async () => {
    const res = await homeApi.getHomeCount()
    klCloudcount.value = res.klCloudcount
    klDoccount.value = res.klDoccount
    klModelSessioncount.value = res.klModelSessioncount
  }

  // 获取搜索历史
  const getSearchLog = async () => {
    const res = await homeApi.getSearchLog({
      creator: loginUser.id,
      pageNum: 1,
      pageSize: 10
    })
    searchHistoryList.value = res.records
  }

  const getLabelList = async () => {
    const res = await homeApi.getLabelPage({
      pageNum: 1,
      pageSize: 10
    })
    checkList.value = res.records
  }

  const isSearchFocused = ref(false)
  const searchHistoryList = ref([])

  const getColSpan = (index) => {
    return index % 2 === 0 ? 14 : 10
  }

  const checkList = ref([])
  const checkValue = ref([])

  const handleSearchFocus = () => {
    isSearchFocused.value = true
    showSearchView.value = false
    getSearchLog()
    getLabelList()
    getSearchDocList()
  }
  const creatKnowledgeRef = ref(null)
  const creatFileRef = ref(null)
  const choosePath = ref({})
  const uploadFormRef = ref(null)
  const getFileWay = (way: any, type: any) => {
    choosePath.value = way
    if (type === 'docx' || type === 'xlsx' || type === 'pptx') {
      creatFileRef.value && creatFileRef.value.open(way, type)
    } else if (type === 'upload') {
			nextTick(() => {
				uploadFormRef.value && uploadFormRef.value.open('0')
			})
    } 
  }

  const createFileSuccess = async(response: any) => {
    docViewRef.value.open('create', choosePath.value, response );
  }

  const handleHistoryDocClick = (item: any) => {
		console.log(item);
		const params = {
			...item,
			fileUrl: item.url,
			fileName: item.document_keyword
		}
    docViewRef.value.open('view', {}, params, true, '搜索结果' );
  }

  // const handleSearchBlur = () => {
  //   // 使用setTimeout让点击历史记录和清除按钮的事件能够先触发
  //   setTimeout(() => {
  //     isSearchFocused.value = false
  //   }, 200)
  // }

  const clearHistory = async() => {
    // searchHistoryList.value = []
    try {
      await homeApi.deleteSearchLogAll({creator: loginUser.id})
      message.success('删除成功')
      getSearchLog()

    } catch (error) {
      console.error('删除失败:', error);
    }
  }

  const removeHistory = async(id: string) => {
    await homeApi.deleteSearchLog(id)
    getSearchLog()
  }

  const handleHistoryClick = (item: any) => {
    searchKeyword.value = item.content
    showSearchView.value = true
    searchMessage.value = searchKeyword.value
    searchResultRef.value.open(
      searchViewParams.value
    )
  }

  const showSearchView = ref(false)
  const searchMessage = ref('')
  const searchInputRef = ref(null)
  const handleSearch = () => {
    console.log('search');
    if(!searchKeyword.value) {
      message.warning('请输入搜索内容')
      return
    }
    showSearchView.value = true
    searchMessage.value = searchKeyword.value
    searchResultRef.value.open(
      searchViewParams.value
    )
  }
  const searchViewRef = ref(null)

  const handleClickOutside = (event: any) => {
    if (
      isSearchFocused.value && 
      searchViewRef.value && 
      !searchViewRef.value.contains(event.target) && 
      searchInputRef.value && 
      !searchInputRef.value.$el.contains(event.target)
    ) {
      isSearchFocused.value = false
    }
  }

  /** 按照 creteTime 创建时间，进行分组 */
  const getConversationGroupByCreateTime = async (list: ChatConversationVO[]) => {
    // 排序、指定、时间分组(今天、一天前、三天前、七天前、30天前)
    // noinspection NonAsciiCharacters
    const groupMap = {
      置顶: [],
      我的对话:[],
    }
    for (const conversation of list) {
      // 置顶
      if (conversation.top === '0') {
        groupMap['置顶'].push(conversation)
        continue
      } else {
        groupMap['我的对话'].push(conversation)
      }
    }
    return groupMap
  }


  const conversationList = ref([])
  const conversationMap = ref({})
  const isConversationEmpty = computed(() => {
    return conversationList.value.length === 0
  })

  /** 获取对话列表 */
  const getChatConversationList = async () => {
    try {
      const { records } = await getKlModelSession({
        pageNum: 1,
        pageSize: 999,
        // 是否临时对话
        isTemporary: false
      })

      // 1.1 获取 对话数据
      conversationList.value = records
      // 1.2 排序
      conversationList.value.sort((a, b) => {
        return b.createTime - a.createTime
      })
      // // 1.3 没有任何对话情况
      if (conversationList.value.length === 0) {
        // activeConversationId.value = null
        conversationMap.value = {}
        return
      }
      // 2. 对话根据时间分组(置顶、今天、一天前、三天前、七天前、30 天前)
        conversationMap.value = await getConversationGroupByCreateTime(conversationList.value)
        console.log('conversationMap.value', conversationMap.value);
        
      } finally {
        console.log('加载');
        
      }
    }
  const ChatMessageDialogRef = ref(null)
  const handleChooseChat = (chatItem) => {
    console.log('handleChooseChat', chatItem);
    ChatMessageDialogRef.value && ChatMessageDialogRef.value.open(chatItem)
  }

  const ChatListDialogRef = ref(null)
  const handleMoreChats = () => {
    ChatListDialogRef.value && ChatListDialogRef.value.open()
  }

  const docListDialogRef = ref(null)
  const handleMoreDocs = () => {
    docListDialogRef.value && docListDialogRef.value.open()
  }
	const generationList = ref([])
  const getdocsList = async () => {
    const response = await TemplateAPI.getCloudPage({
      pageNum: 1,
      pageSize: 10,
      isfile: 1,
	  creator: loginUser.id,
    });
    generationList.value = response.records
  }

	const getAllApi = async () => {
    await Promise.all([
      getHomeCount(),
			getdocsList(),
      getHomeDownloadCountList('collect'),
      getHomeKlDatset()
    ])
    loading.value = false
  }
  
  getAllApi()

  const tagDocmentsRef = ref()
  const tagClick = (tagParams) => {
    tagDocmentsRef.value.open(tagParams)
  }

	const TempDocmentsRef = ref()
	
	const openTempDocments = async () => { 
		TempDocmentsRef.value.open()
	}

  const homeprofileRef = ref()
  const toProfile = async () => {
    homeprofileRef.value && homeprofileRef.value.open()
  }

  const loginOut = async () => {
    try {
      await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
      await userStore.loginOut()
      tagsViewStore.delAllViews()
      replace('/login?redirect=/index')
    } catch {}
  }

  const myDocumentListRef = ref()
  const handleShowDocument = (type) => {
    myDocumentListRef.value && myDocumentListRef.value.open(type)
  }

  // activeName监听

  const docViewRef = ref()
  const handleView = (item) => {
    docViewRef.value && docViewRef.value.open('view', {}, item, true, '我的文档');
  }

  const searchDocList = ref([])
  const getSearchDocList = async () => {
    const response = await homeApi.getFileEventLog({
      pageNum: 1,
      pageSize: 5,
      creator: loginUser.id,
    })
    searchDocList.value = response.records
  }
	const learnFormRef = ref()
	const handleLearn = () => {
		learnFormRef.value && learnFormRef.value.open()
  }

	const learnList = ref([])
	const learnloading = ref(false)
	const handleLearnSearch = async() => {
		learnloading.value = true
		try {
			const response = await homeApi.getStudy()
			learnloading.value = false
			learnList.value = response
		} catch (error) {
			learnloading.value = false
			console.log('获取学习列表失败');
		}
	}

	const handleCloseDialog = () => {
		console.log('关闭对话框--------------');
		getHomeKlDatset()
	}

	const creatAIFileRef = ref()
	const handleNewFile = () => {
		const pathParams = {
			id: '',
			fileName: '根目录',
			filePath: '',
			level: "1"
		}
		creatAIFileRef.value && creatAIFileRef.value.open(pathParams, 'docx')
	}
	const createAIFileSuccess = async(results, callback) => {
		const params = {
			...results
		}
		const initReponse = await uploadInit({
			url: `${apikeys.file_server_url}/init.docx`,
			path: null,
			name: results.fileName,
		})
		params.fileUrl = initReponse.fileUrl
		// await TemplateAPI.createFolder(params)
		// const docparams = {
		// 	docTitle: results.docTitle,
		// 	fileUrl: params.fileUrl,
		// 	klTemplateId: results.klTemplateId,
		// 	filePrefix: results.filePrefix,
		// 	fileName: results.fileName,
		// 	fileKey: results.fileKey,
		// 	scope: results.scope,
		// 	id: results.id,
		// }
		const response = await TemplateAPI.createFolder(params)
		const docparams = {
			docTitle: results.title,
			fileUrl: params.fileUrl,
			klTemplateId: results.documentTemplate,
			filePrefix: results.documentType,
			fileName: results.fileName,
			fileKey: results.fileKey,
			scope: results.scope,
			id: response.data.id,
		}
		if(results.searchFile) {
			docparams.searchFile = results.searchFile
		}
		const docResponse = await TemplateAPI.createDoc(docparams)
		if(docResponse) {
			callback && callback()
			message.success('正在生成中 请稍后...')
		} else {
			message.error('生成失败')
		}
	}

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    getChatConversationList()
		handleLearnSearch()
    setTimeout(() => {
      getdocsList()
    }, 100);
  })

  onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
  })
  </script>

<style lang="scss" scoped>
// 定义变量
$color-primary: #1c58c6;
$left-width: 70px;
.no-border-popper {
	border: none !important;
}
.home-content {
	width: 100%;
	height: 100vh;
	display: flex;
	background-color: #e2eaf1;
	.home-left {
		width: $left-width;
		height: 100%;
		background-color: #1c58c6;
		:deep(.el-menu) {
			border-right: none;
		}
		:deep(.el-sub-menu__title) {
			padding: 0;
			height: 80px;
		}
		.el-menu-vertical-demo {
			width: $left-width;
			&::v-deep(.el-menu-item) {
				height: 80px;
				padding: 0;
			}
			&::v-deep(.is-active) {
				background-color: var(--el-menu-hover-bg-color);;
			}
		}
		.home-logo {
			padding: 10px;
			padding-top: 20px;
			text-align: center;
			img {
				width: 32px;
				height: 32px;
				border-radius: 50%;
			}
			p {
				font-size: 14px;
				color: #fff;
			}

		}
		.left-menu-item {
			width: 64px;
			text-align: center;
			p {
				font-size: 12px;
				line-height: 24px;
				height: 24px;
			}
		}
	}
	.home-right {
		height: 100vh;
		flex: auto;
		.right--top-bg {
			background-color: $color-primary;
		}
		.home-right-top {
			border-radius: 20px 0 0 0;
			overflow: hidden;
			background-color: $color-primary;
			display: flex;
			.menu-logo {
				opacity: 1;
				cursor: default;
			}
		}
		.right-user {
			background-color: #fff;
		}
		.my-menu {
			border-bottom: none;
			flex: 1;
			height: 48px;
			:deep(.el-submenu__title:hover),
			:deep(.el-menu-item:hover),
			:deep(.el-menu-item.is-active) {
				i {
					color: #000 !important;
				}

				color: #000 !important;
				background: #ecf5ff !important;
			}
		}
		.last-menu-item {
			margin-left: auto;
			:deep(.el-sub-menu__icon-arrow) {
				display: none !important;
			}
			:deep(.el-sub-menu__title) {
				margin-right: -40px !important;
			}
		}
		.home-right-content {
			padding: 10px;
			padding-right: 0;
			.hr-search {
				position: relative;
				margin-bottom: 10px;
			}
			.search-header  {
				:deep(.el-input-group__prepend)  {
					background-color: #fff;
					color: #fff;
				}
				:deep(.el-input-group__append) {
					background-color: #fff;
					padding: 0;
				}
				:deep(.el-divider--vertical) {
					margin: 0;
				}
				:deep(.el-select) {
					width: 110px;
				}
			}
			.search-dropdown {
				position: absolute;
				top: 41px;
				background: white;
				border: 1px solid var(--el-border-color-light);
				border-top: none;
				border-radius: 0 0 8px 8px;
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
				z-index: 2000;
				width: 100%;
				padding: 10px 4px 4px 16px;
				overflow-y: auto;
				.doc-types {
					margin-bottom: 10px;
					padding-bottom: 10px;
					border-bottom: 1px solid var(--el-border-color-lighter);
					border-top: none;
				}
				.history-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 12px;
					color: var(--el-text-color-regular);
					font-size: 14px;
				}
				.history-list {
					display: flex;
					flex-direction: column;
					gap: 4px;
					.history-item {
						display: flex;
						align-items: center;
						padding: 8px 12px;
						background: #fff;
						font-size: 14px;
						color: var(--el-text-color-regular);
						cursor: pointer;
						transition: all 0.3s;
						border-radius: 4px;
					}
					.history-item:hover {
						background: var(--el-fill-color-light);
					}

					.history-item .el-icon {
						margin-right: 8px;
						font-size: 16px;
						color: #909399;
					}

					.history-text {
						flex: 1;
					}

					.history-item .delete-icon {
						margin-left: 8px;
						font-size: 16px;
						color: #909399;
						opacity: 0;
						transition: opacity 0.3s;
					}
					.history-item:hover .delete-icon {
						opacity: 1;
					}
					.history-doc {
						display: flex;
						align-items: center;
						padding: 8px 12px;
						background: #fff;
						font-size: 14px;
						color: var(--el-text-color-regular);
						cursor: pointer;
						transition: all 0.3s;
						border-radius: 4px;
					}

					.history-doc .el-icon {
						margin-right: 8px;
						font-size: 24px;
						color: #909399;
					}

					.history-doc .doc-desc {
						flex: 1;
					}
					.doc-text-time {
						font-size: 12px;
					}
				}
			}
			.home-center-content {
				padding: 10px;
				margin-top: 10px;
				background-color: #fff;
				.home-center-title {
					font-size: 16px;
					display: flex;
					align-items: center;
					margin-top: 10px;
					margin-bottom: 20px;
				}
				.hr-item {
					border-radius: 10px;
					background-color: #fff;
					padding: 10px;
					position: relative;
					box-shadow: 0 4px 16px rgba(200, 210, 220, 0.5);
					.hr-item-title {
						display: flex;
						justify-content: space-between;
						font-size: 16px;
						align-items: center;
						.known-title {
							display: flex;
							align-items: center;
						}
						> span {
							padding-left: 10px;
							position: relative;
							&::before {
								content: "";
								position: absolute;
								left: 0;
								top: 50%;
								transform: translateY(-50%);
								width: 4px;
								height: 14px;
								background-color: #409EFF;
								border-radius: 2px;
							}
						}
						.el-dropdown-link {
							cursor: pointer;
							color: var(--el-color-primary);
							display: flex;
							align-items: center;
							font-weight: normal;
						}
					}
					.hr-more {
						cursor: pointer;
						display: flex;
						align-items: center;
						color: #409EFF;
						position: absolute;
						right: 10px;
						top: 10px;
					}
					.rank-list {
						margin-top: 10px;
						display: flex;
						flex-direction: column;
						gap: 12px;
						width: 100%;
						max-width: 100%;
						overflow: hidden;
					}

					.rank-item {
						display: flex;
						align-items: center;
						gap: 8px;
						font-size: 12px;
						cursor: pointer;
						width: 100%;
					}

					.rank-item:hover {
						color: var(--el-color-primary);
					}

					.rank-number {
						min-width: 20px;
						text-align: center;
					}

					.rank-number.top-three {
						color: var(--el-color-primary);
						font-weight: bold;
					}

					.rank-title {
						flex: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						max-width: calc(100% - 40px);
					}
					.chat-content {
						margin-top: 10px;
						.chat-title {
							height: 36px;
							line-height: 36px;
							padding-left: 5px;
							font-size: 14px;
							border-radius: 10px;
							background-color: #eff3f7;
						}
						.chat-name {
							min-height: 40px;
							padding-left: 10px;
							padding-top: 10px;
							span {
								cursor: pointer;
								font-size: 12px;
							}
						}
					}
				}
			}
		}
	}

}
.tool-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 16px;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  background: #f5f7fa;
  transition: all 0.3s ease;
  cursor: pointer;
	span {
		font-size: 12px;
	}
}

.tool-item:hover {
  background: #e1e8f0;
}

.icon {
  margin-bottom: 12px;
}

</style>
