import { service } from './service'

import { config } from './config'

const { default_headers } = config

const request = (option: any) => {
  const { url, method, params, data, headersType, responseType, ...config } = option
  
  return service({
    url: url,
    method,
    params,
    data,
    responseType: responseType,
    timeout: option.request_timeout || config.request_timeout,
    ...config,
    headers: {
      'ngrok-skip-browser-warning':'69420',
      'Content-Type': headersType || default_headers,
    }
  })
}
export default {
  get: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res.data as unknown as T
  },
  getOriginal: async (option: any) => {
    const res = await request({ method: 'GET', ...option })
    return res
  },
  post: async <T = any>(option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res.data as unknown as T
  },
  postOriginal: async (option: any) => {
    const res = await request({ method: 'POST', ...option })
    return res
  },
  delete: async <T = any>(option: any) => {
    const res = await request({ method: 'DELETE', ...option })
    return res.data as unknown as T
  },
  deleteOriginal: async (option: any) => {
    const res = await request({ method: 'DELETE', ...option })
    return res
  },
  put: async <T = any>(option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res.data as unknown as T
  },
  putOriginal: async(option: any) => {
    const res = await request({ method: 'PUT', ...option })
    return res
  },
  download: async <T = any>(option: any) => {
    const res = await request({ method: 'GET', responseType: 'blob', ...option })
    return res as unknown as Promise<T>
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data'
    const res = await request({ method: 'POST', ...option })
    return res as unknown as Promise<T>
  }
}
