<template>
    <el-dialog
      v-model="dialogVisible" 
      width="700"
      title="新建文件">
      <div class="content">
        <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="100px">
            <el-form-item label="文件名称" prop="localName">
              <el-input v-model="formData.localName" placeholder="请输入文件名称" >
                <template #append>
                  {{ fileType }}
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="标签" prop="label">
              <TagInput 
                v-model="formData.label" 
                :default-tags="defaultTags"
                placeholder="请输入标签"
              />
            </el-form-item>
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="formData.projectName" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="所属人" prop="knowledgeOwner">
              <el-input v-model="formData.knowledgeOwner" placeholder="请输入所属人" />
            </el-form-item>
            <el-form-item label="所属部门" prop="department">
              <el-input v-model="formData.department" placeholder="请输入所属部门" />
            </el-form-item>
            <el-form-item label="知识所属年份" prop="year">
              <el-date-picker
                v-model="formData.year"
                type="year"
                placeholder="请选择知识所属年份"
              />
            </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </el-dialog>  
  </template>
  <script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
  import * as FolderAPI from '@/api/knowledge/docmangement'
  import TagInput from '@/components/TagInput/index.vue'
  import { getCloudLabelPage } from '@/api/knowledge/docmangement'
  import { FolderOpened,  Document, } from '@element-plus/icons-vue'
  import { useUserStore } from '@/store/modules/user'
  
  import { v4 as uuidv4 } from 'uuid'
  
  defineOptions({ name: 'CreatFile' })
  
  const emit = defineEmits(['success'])
  
  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  
  const defaultTags = ref([])
   const userStore = useUserStore()
  const userInfo = userStore.getUser
  
  const dialogVisible = ref(false) // 弹窗的是否展示
  const formLoading = ref(false)
  const formRef = ref()
  const formData = ref({
    localName: '',
    label: [],
    knowledgeOwner: userInfo.nickname,
    projectName: '',
    department: '',
    year: '',
  })
  const formRules = reactive({
    localName: [
      { required: true, message: '请输入文件名称', trigger: 'blur' },
      { min: 1, max: 50, message: '请输入正确的内容', trigger: 'blur' }
    ],
    knowledgeOwner: [{ required: true, message: '知识所属人不能为空', trigger: 'blur' }],
  })
  // 当前路径
  const path = ref({})
  const fileType = ref('docx')
 

  // 获取标签
  const getLabel = async () => {
    const res = await getCloudLabelPage({
      pageNum: 1,
      pageSize: 5,
    })
    defaultTags.value = res.records.map((item) => item.name)
  }

  /** 打开弹窗 */
  const open = async (pathParams, type) => {
    // fileId.value = row.id
    resetForm()
    path.value = pathParams
    dialogVisible.value = true
    fileType.value = type
    getLabel()
  }
  defineExpose({ open }) // 提供 open 方法，用于打开弹窗
  
  const submitForm = async () => {
    const data = formData.value
    const params = {
      ...data,
      fileName: `${data.localName}.${fileType.value}`,
      datasetId: path.value.datasetId,
      filePath: path.value.id ? `${path.value.filePath}/${data.localName}.${fileType.value}` : null,
      parentId: path.value.id ? path.value.id : null,
      level: path.value.id ? parseInt(path.value.level) + 1 : '1',
      filePrefix: fileType.value
    }
    dialogVisible.value = false
    emit('success', params)
  }

  /** 重置表单 */
  const resetForm = () => {
    formData.value = {
      localName: '',
      label: [],
      knowledgeOwner: userInfo.nickname,
      projectName: '',
      department: '',
      year: '',
    }
    formRef.value?.resetFields()
  }
  
  /** 提交表单 */
  
  </script>
  
  <style lang="scss" scoped>
    .breadcrumb-wrapper {
      display: flex;
      height: 32px;
      align-items: center;
      padding: 0 20px 0 0;
      .back-button {
        padding: 0;
        font-size: 14px;
      }
    }
    .file-item {
      line-height: 32px;
      padding: 8px;
      border-bottom: 1px solid #ccc;
      cursor: pointer;
    }
  </style>
  