interface BaseNodeData {
export 	label: string;
	inputParams: { name: string; type: string; key?: string; required?: boolean }[];
	outputParams: { name: string; type: string }[];
}

export interface StartNodeData extends BaseNodeData {
	description: string;
}

export interface LLMNodeData extends BaseNodeData {
	model: string;
	apiKey?: string;
	temperature?: number;
	maxTokens?: number;
}

export interface KBNodeData extends BaseNodeData {
	kbId?: string;
	kbName?: string;
	similarity?: number;
	topK?: number;
}

export interface TemplateNodeData extends BaseNodeData {
	template: string;
}

export interface HttpNodeData extends BaseNodeData {
	url: string;
	method: string;
	headers: Record<string, string>;
}

export interface SearchNodeData extends BaseNodeData {
	engine: string;
	apiKey?: string;
	topK?: number;
}

export interface TestNodeData extends BaseNodeData {
	inputValue: string;
}