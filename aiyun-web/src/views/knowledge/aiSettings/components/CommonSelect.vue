<template>
    <el-select
      v-model="selectedValue"
      :placeholder="props.placeholder"
      :clearable="props.clearable"
      :disabled="props.disabled"
      :filterable="props.filterable"
      :multiple="props.multiple"
      @change="handleChange"
    >
      <el-option
        v-for="option in mergedOptions"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>
  </template>
  
  <script setup>
  import { computed, ref, watch } from 'vue'

  defineOptions ({ name: 'CommonSelect' })
  
  const props = defineProps({
    // 绑定值
    modelValue: {
      type: [String, Number, Array],
      default: ''
    },
    // 选项列表（可选，不传则使用默认选项）
    options: {
      type: Array,
      default: () => [],
      validator: (value) => {
        if (!value.length) return true
        return value.every(option => {
          return option.hasOwnProperty('label') && option.hasOwnProperty('value')
        })
      }
    },
    // 占位文本
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否可搜索
    filterable: {
      type: Bo<PERSON>an,
      default: false
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    }
  })
  
  const emit = defineEmits(['update:modelValue', 'change'])
  
  // 默认选项
  const defaultOptions = [
    { label: 'Qwen', value: 'Qwen' },
    { label: 'Ollama', value: 'Ollama' },
    { label: 'Deepseek', value: 'Deepseek' }
  ]
  
  // 合并选项，如果传入了options则使用传入的，否则使用默认的
  const mergedOptions = computed(() => {
    return props.options.length ? props.options : defaultOptions
  })
  
  // 内部选中的值
  const selectedValue = ref(props.modelValue)
  
  // 监听外部modelValue变化
  watch(() => props.modelValue, (newVal) => {
    selectedValue.value = newVal
  })
  
  // 值变化处理
  const handleChange = (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
  </script>