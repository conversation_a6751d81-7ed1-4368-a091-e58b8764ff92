<template>
    <!-- 我的推荐 -->
        <ContentWrap>
          <!-- 头部按钮和搜索框 -->
          <div class="header">
            <!-- 左侧按钮 -->
            <div>
            </div>
      
            <!-- 右侧搜索框和按钮 -->
            <div>
              <el-input v-model="queryParams.fileName" placeholder="请输入文件名" style="width: 200px; margin-right: 10px;" />
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </div>
          </div>
      
          <!-- 文件列表 -->
          <el-table v-loading="tableLoading" :data="fileList">
            <el-table-column prop="klCloudDO.fileName" show-overflow-tooltip label="名称" />
            <el-table-column prop="createTime" label="推荐时间" />
            <el-table-column prop="klCloudDO.createTime" label="创建时间" />
            <el-table-column prop="klCloudDO.creator" label="创建人" />
            <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="handlePreview(scope.row)"
              >
                预览
              </el-button>
            </template>
          </el-table-column>
          </el-table>
      
          <Pagination
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="queryList"
          />
        </ContentWrap>
        <DocView ref="docViewRef" @success="handleSearch" />
      </template>
      
      <script setup>
      import { ref, onMounted, computed } from 'vue';
    
      import * as FolderAPI from '@/api/knowledge/docmangement'
      import { useUserStore } from '@/store/modules/user'
      import DocView from './components/DocView.vue'
      
      const message = useMessage() // 消息弹窗
      const userStore = useUserStore()
      const apikeys = userStore.getAppConfig
    
      const tableLoading = ref(false);
      
      // 当前文件夹下的文件列表
      const fileList = ref([]);
      
      // 搜索查询
      const queryParams = ref({
        pageNum: 1,
        pageSize: 10,
        fileName: '',
      });
      const total = ref(0);
      
      // 加载文件夹内容
      const loadFolderContent = async () => {
        // fileList.value = fakeData[folderId] || [];
        tableLoading.value = true;
        const response = await FolderAPI.getCloudPushesPage({
          pageNum: queryParams.value.pageNum,
          pageSize: queryParams.value.pageSize,
          klCloudName: queryParams.value.fileName,
        });
        tableLoading.value = false;
        total.value = response.total;
        fileList.value = response.records;
      };
      
      const queryList = () => {
        loadFolderContent();
      }
      
      const handleSearch = () => {
        queryParams.value.pageNum = 1;
        loadFolderContent();
      }
      
      const handleReset = () => {
        queryParams.value.fileName = ''
        queryParams.value.pageNum = 1;
        loadFolderContent();
      }
    
      const docViewRef = ref(null);
      const handlePreview = (row) => {
        docViewRef.value.open('view', {}, row.klCloudDO);
      }
    
      // 初始化加载根目录
      onMounted(() => {
        handleSearch();
        // 开启定时器
        // setQueryInterval()
      });
      
      </script>
      
      <style scoped lang="scss">
      .header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      
      .breadcrumb-wrapper {
        display: flex;
        height: 48px;
        align-items: center;
        margin-bottom: 10px;
        padding: 0 20px;
        .back-button {
          padding: 0;
          font-size: 14px;
        }
      }
      </style>