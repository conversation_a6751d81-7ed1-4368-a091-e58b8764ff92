<script setup lang="ts">
import { <PERSON>le, Position, useVueFlow } from "@vue-flow/core";
import { NodeToolbar } from '@vue-flow/node-toolbar'
import { ref, markRaw } from 'vue';
import { ArrowUp, ArrowDown, Delete } from '@element-plus/icons-vue';

const { updateNodeData, removeNodes } = useVueFlow();

const isExpanded = ref(true);

defineOptions({ name: 'KnowledgeBaseNode' })
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const props = defineProps<{
  id: string;
  data: {
    label: string;
    kbId?: string;
    similarity?: number;
    topK?: number;
    inputParams?: Array<{ name: string; type: string }>;
    outputParams?: Array<{ name: string; type: string }>;
    toolbarVisible?: boolean;
    searchPrompt?: string;
  }
}>();

const emit = defineEmits(['updateNodeInternals']);

const Icons = {
  ArrowUp: markRaw(ArrowUp),
  ArrowDown: markRaw(ArrowDown),
  Delete: markRaw(Delete)
};

const updateData = (key: string, value: any) => {
  updateNodeData(props.id, {
    ...props.data,
    [key]: value
  });
};

const addInputParam = () => {
  const inputParams = [...(props.data.inputParams || [])];
  inputParams.push({ name: '', type: 'string' });
  updateData('inputParams', inputParams);
};

const addOutputParam = () => {
  const outputParams = [...(props.data.outputParams || [])];
  outputParams.push({ name: '', type: 'string' });
  updateData('outputParams', outputParams);
};

const updateParam = (index: number, key: string, value: string, isInput: boolean) => {
  const params = isInput ? [...(props.data.inputParams || [])] : [...(props.data.outputParams || [])];
  params[index] = { ...params[index], [key]: value };
  updateData(isInput ? 'inputParams' : 'outputParams', params);
};

const removeParam = (index: number, isInput: boolean) => {
  const params = isInput ? [...(props.data.inputParams || [])] : [...(props.data.outputParams || [])];
  params.splice(index, 1);
  updateData(isInput ? 'inputParams' : 'outputParams', params);
};
</script>

<template>
  <div class="node-wrapper">
    <NodeToolbar  
      :is-visible="data.toolbarVisible" 
      style="background-color: #fff;"
      :position="Position.Top"
      align="end"
      :offset="4"
    >
      <el-button
        @click.stop="removeNodes([props.id])"
      >
        <el-icon><component :is="Icons.Delete" /></el-icon>
      </el-button>
    </NodeToolbar>
    <div class="kb-node">
      <Handle
        type="target"
        :position="Position.Top"
        :id="`${props.id}-top`"
        class="horizontal-handle"
      />
      <Handle
        type="target"
        :position="Position.Left"
        :id="`${props.id}-left`"
        class="vertical-handle"
      />
      <div class="node-header">
        <span class="node-type">{{ data.label }}</span>
        <el-button
          type="primary"
          link
          @click="toggleExpand"
        >
          <el-icon>
            <component :is="Icons.ArrowUp" v-if="isExpanded" />
            <component :is="Icons.ArrowDown" v-else />
          </el-icon>
        </el-button>
      </div>
      <div v-show="isExpanded" class="node-content">
        <!-- 知识库配置 -->
        <div class="params-section">
          <div class="params-header">
            <span>知识库</span>
            <el-button type="primary" size="small" @click="addInputParam">+</el-button>
          </div>
          <div v-for="(param, index) in data.inputParams" :key="index" class="param-item">
            <el-select
              :model-value="param.type"
              @update:model-value="(val) => updateParam(index, 'type', val, true)"
              size="small"
            >
              <el-option label="String" value="String" />
              <el-option label="Number" value="Number" />
              <el-option label="Boolean" value="Boolean" />
              <el-option label="Object" value="Object" />
            </el-select>
            <el-button type="danger" size="small" @click="removeParam(index, true)">-</el-button>
          </div>
        </div>

        <!-- 文件配置 -->
        <div class="params-section">
          <div class="params-header">
            <span>选择文档</span>
            <el-button type="primary" size="small" @click="addOutputParam">+</el-button>
          </div>
          <div v-for="(param, index) in data.outputParams" :key="index" class="param-item">
            <el-select
              :model-value="param.type"
              @update:model-value="(val) => updateParam(index, 'type', val, false)"
              size="small"
            >
              <el-option label="String" value="String" />
              <el-option label="Number" value="Number" />
              <el-option label="Boolean" value="Boolean" />
              <el-option label="Object" value="Object" />
            </el-select>
            <el-button type="danger" size="small" @click="removeParam(index, false)">-</el-button>
          </div>
        </div>
        <div class="kb-section">
          <div class="form-item">
            <label>检索提示词</label>
            <el-input
              :model-value="data.searchPrompt"
              @update:model-value="(val) => updateData('searchPrompt', val)"
              placeholder="请输入检索提示词"
              type="textarea"
              show-word-limit
              maxlength="200"
              :rows="4"
            />
          </div>
        </div>
      </div>
      <Handle
        type="source"
        :position="Position.Bottom"
        :id="`${props.id}-bottom`"
        class="horizontal-handle"
      />
      <Handle
        type="source"
        :position="Position.Right"
        :id="`${props.id}-right`"
        class="vertical-handle"
      />
    </div>
  </div>
</template>

<style scoped>
.kb-node {
  width: 320px;
  background-color: white;
  border: 2px solid #e6a23c;
  border-radius: 8px;
  padding: 12px;
}

.node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.node-type {
  font-weight: bold;
  color: #e6a23c;
  text-align: left;
}

.kb-section {
  margin-bottom: 12px;
}

.kb-section > * {
  margin-bottom: 8px;
}

.kb-params {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.params-section {
  margin-bottom: 12px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.param-item {
  display: grid;
  grid-template-columns: 2fr 30px;
  gap: 8px;
  margin-bottom: 8px;
}

:deep(.el-input__wrapper) {
  box-shadow: none !important;
  border: 1px solid #e2e8f0;
}

:deep(.el-input__wrapper:hover) {
  border-color: #e6a23c;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-button--small) {
  padding: 4px 8px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-button--text) {
  padding: 0;
  height: auto;
  color: #e6a23c;
}

.horizontal-handle {
  background-color: #e6a23c;
  width: 30px;
  height: 16px;
  border-radius: 5px;
}

.vertical-handle {
  background-color: #e6a23c;
  width: 16px;
  height: 30px;
  border-radius: 5px;
}

.node-wrapper {
  position: relative;
}
</style> 