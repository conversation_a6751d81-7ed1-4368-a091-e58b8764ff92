<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="所属平台" prop="platform">
        <!-- <el-input v-model="formData.platform" placeholder="请输入所属平台" /> -->
        <common-select
          v-model="formData.platform"
          :options="platformOptions"
          placeholder="请选择所属平台"
        />
      </el-form-item>
      <el-form-item label="引擎名称" prop="engineName">
        <el-input v-model="formData.engineName" placeholder="请输入引擎名称" />
      </el-form-item>
      <el-form-item label="引擎地址" prop="apiUrl">
        <el-input v-model="formData.apiUrl" placeholder="请输入引擎地址" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio-button value="0">启用</el-radio-button>
          <el-radio-button value="1">禁用</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input 
          v-model="formData.remark" 
          :maxlength="80" 
          :rows="3" 
          show-word-limit 
          type="textarea"
          placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup>
import CommonSelect from '../components/CommonSelect.vue'
import * as SearchEngineApi from '@/api/knowledge/aiSettings/searchengine'

defineOptions({ name: 'SearchEngineSettingForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  platform: undefined,
  engineName: undefined,
  apiUrl: undefined,
  sort: undefined,
  status: undefined,
  remark: undefined
})

const platformOptions = reactive([
  { label: '百度', value: '百度' },
  { label: '谷歌', value: '谷歌' },
  { label: '必应', value: '必应' },
  { label: '搜狗', value: '搜狗' },
])

const formRules = reactive({
  platform: [{ required: true, message: '所属平台不能为空', trigger: 'blur' }],
  engineName: [{ required: true, message: '引擎名称不能为空', trigger: 'blur' }],
  apiUrl: [{ required: true, message: '引擎地址不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type, id) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增' : '修改'
  formType.value = type
  resetForm()
  // 修改时，获取详情数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SearchEngineApi.getklArrangeSearchEngine(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return

  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await SearchEngineApi.addklArrangeSearchEngine(data)
      message.success(t('common.createSuccess'))
    } else {
      await SearchEngineApi.updateklArrangeSearchEngine(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    platform: undefined,
    engineName: undefined,
    apiUrl: undefined,
    sort: undefined,
    status: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>