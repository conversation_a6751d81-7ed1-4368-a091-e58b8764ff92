<template>
  <el-card class="chart-section" shadow="never">
    <template #header>
      <span class="section-title">近7日系统访问</span>
    </template>
    <div ref="chart" style="width: 100%; height: 329px"></div>
  </el-card>
</template>

<script setup>
import * as echarts from 'echarts'
import * as homeAPI from '@/api/home/<USER>'

const chart = ref(null)
// 图表初始化
onMounted(async() => {
  const response = await homeAPI.getHomeLoginCount()
  const { sdata, ydata} = response

  const myChart = echarts.init(chart.value)
  myChart.setOption({
    xAxis: {
      type: 'category',
      data: sdata
    },
    yAxis: { type: 'value' },
    grid: {
      left: '3%',
      right: '5%',
      bottom: '3%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    series: [
      {
        type: 'bar',
        data: ydata,
        itemStyle: { color: '#409EFF' }
      }
    ]
  })
})
</script>

<style lang="less" scoped></style>
