export const chatList = [
  // 客服助手：67a0540cb12711ef99a80242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '67a0540cb12711ef99a80242c0a82005',
    type: 'ai',
    name: '客服助手'
  },
  // 财金数据：29b0872eb12811ef8db00242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '29b0872eb12811ef8db00242c0a82005',
    type: 'ai',
    name: '财金数据'
  },
  // 智能检索：58f9f5b6b12711ef87d90242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '58f9f5b6b12711ef87d90242c0a82005',
    type: 'ai',
    name: '智能检索'
  },
  // 翻译助手：78da1d56b12811ef889d0242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '78da1d56b12811ef889d0242c0a82005',
    type: 'ai',
    name: '智能翻译'
  },
  // 智能问诊：091d63c4b12811ef87f80242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '091d63c4b12811ef87f80242c0a82005',
    type: 'ai',
    name: '智能问诊'
  },
  // 智能助理：78da1d56b12811ef889d0242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '78da1d56b12811ef889d0242c0a82005',
    type: 'ai',
    name: '智能助理'
  },
  // 数据运维助手：b4727a26b12711ef89f30242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: 'b4727a26b12711ef89f30242c0a82005',
    type: 'ai',
    name: '数据运维助手'
  },
  // 数据开发助手：98ba577cb12711ef97ae0242c0a82005
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '98ba577cb12711ef97ae0242c0a82005',
    type: 'ai',
    name: '数据开发助手'
  },
  //文档专员 ：79d74f4cb3aa11ef9d3a0242c0a82002
  {
    api_key: 'ragflow-k3MTljZmUwYjNhOTExZWY5NTM2MDI0Mm',
    agent_id: '79d74f4cb3aa11ef9d3a0242c0a82002',
    type: 'chat',
    name: '文档专员'
  }
];
