# Yun<PERSON>hi Plugin 本地开发指南

## 问题说明

在本地开发时，`window.Asc.plugin.init` 函数不会执行，这是因为：

1. **缺少 OnlyOffice 环境**: 插件需要在 OnlyOffice 编辑器环境中运行
2. **SDK 依赖**: 插件依赖 OnlyOffice 的 SDK 来建立与编辑器的通信
3. **生命周期管理**: 只有 OnlyOffice 编辑器才会调用插件的初始化函数

## 解决方案

### 方案 1: 本地测试环境 (推荐用于快速开发)

1. **启动本地服务器**:
   ```bash
   # 在 yunzhi 目录下运行
   ./start-dev.sh
   ```

2. **访问测试页面**:
   - 打开浏览器访问: `http://localhost:8000/test-local.html`
   - 这个页面模拟了 OnlyOffice 环境，可以测试插件的基本功能

3. **查看插件界面**:
   - 访问: `http://localhost:8000/index.html`
   - 直接查看插件的 UI 界面

### 方案 2: OnlyOffice Desktop Editors (推荐用于完整测试)

1. **下载 OnlyOffice Desktop Editors**:
   - 访问: https://www.onlyoffice.com/download-desktop.aspx
   - 下载并安装桌面版

2. **打包插件**:
   ```bash
   # 在 yunzhi 目录下
   zip -r yunzhi.plugin .
   ```

3. **安装插件**:
   - 打开 OnlyOffice Desktop Editors
   - 创建或打开文档
   - 点击 "插件" → "插件管理器" → "添加插件"
   - 选择 `yunzhi.plugin` 文件

### 方案 3: OnlyOffice Document Server (用于服务器环境测试)

1. **使用 Docker 部署**:
   ```bash
   # 拉取镜像
   docker pull onlyoffice/documentserver
   
   # 运行容器
   docker run -itd -p 80:80 \
     -v $(pwd):/var/www/onlyoffice/documentserver/sdkjs-plugins/yunzhi \
     onlyoffice/documentserver
   ```

2. **访问编辑器**:
   - 打开浏览器访问: `http://localhost`
   - 创建文档并测试插件

## 开发调试技巧

### 1. 控制台调试
```javascript
// 在 main.js 中添加更多调试信息
window.Asc.plugin.init = function () {
    console.log('=== Plugin Init Start ===');
    console.log('User:', window.Asc.plugin.info.userName);
    console.log('Language:', window.Asc.plugin.info.lang);
    console.log('Editor Type:', window.Asc.plugin.info.editorType);
    console.log('=== Plugin Init End ===');
    
    // 原有的初始化代码...
};
```

### 2. 错误处理
```javascript
window.Asc.plugin.init = function () {
    try {
        // 初始化代码
        console.log('Plugin initialized successfully');
    } catch (error) {
        console.error('Plugin initialization failed:', error);
    }
};
```

### 3. 实时重载
- 修改代码后，在 OnlyOffice 中重新加载插件
- 或者刷新测试页面查看更改

## 常见问题

### Q: 为什么直接打开 index.html 不工作？
A: 插件需要 OnlyOffice 的 SDK 环境，直接打开 HTML 文件缺少必要的 API。

### Q: 如何调试插件与编辑器的交互？
A: 使用 OnlyOffice Desktop Editors，打开开发者工具查看控制台输出。

### Q: 插件在不同编辑器类型中表现不同怎么办？
A: 在 `window.Asc.plugin.init` 中检查 `window.Asc.plugin.info.editorType`：
- `"word"` - Word 文档
- `"cell"` - Excel 表格  
- `"slide"` - PowerPoint 演示文稿

### Q: 如何测试插件的 API 调用？
A: 使用测试页面的模拟环境，或在真实的 OnlyOffice 环境中使用控制台。

## 文件结构

```
yunzhi/
├── config.json          # 插件配置
├── index.html           # 插件主界面
├── scripts/
│   ├── main.js          # 插件主逻辑
│   └── sdkjs/           # SDK 文件
├── test-local.html      # 本地测试页面
├── start-dev.sh         # 开发启动脚本
└── README-DEV.md        # 开发文档
```

## 下一步

1. 使用 `./start-dev.sh` 启动本地开发环境
2. 在 `test-local.html` 中测试基本功能
3. 在 OnlyOffice Desktop Editors 中进行完整测试
4. 根据需要调整插件配置和功能
