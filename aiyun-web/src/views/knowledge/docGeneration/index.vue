<template>
  <el-row :gutter="20" v-loading="loading">
    <el-col :span="18">
      <div class="content">
        <DocumentEditor id="docEditor" :documentServerUrl="`${apikeys.onlyoffice_url}`" :config="config" />
      </div>
    </el-col>
    <el-col :span="6" class="form-col">
      <el-form>
        <el-scrollbar class="scrollbar">

          <!-- 文档标题 -->
          <h4>第一步：输入文档标题</h4>
          <el-form-item>
            <el-input v-model="formData.title" />
          </el-form-item>
          <h4>第二步：选择文档类型</h4>
          <el-form-item>
            <el-radio-group v-model="formData.documentType" size="large">
              <el-space wrap>
                <el-radio-button v-for="item in getIntDictOptions(DICT_TYPE.knowledge_doc_type)" :key="item.value"
                  :label="item.label" :value="item.value" />
              </el-space>
            </el-radio-group>
          </el-form-item>
          <div class="step-title">
            <h4>第三步：选择模板</h4>
            <el-button size="small" type="primary" @click="handleTemplate">查看模板</el-button>
          </div>
          <el-form-item>
            <el-select v-model="formData.documentTemplate" placeholder="请选择模板">
              <el-option v-for="item in tempOptions" :key="item.id" :label="item.templateFileName" :value="item.id" />
            </el-select>
          </el-form-item>
          <div>
            <div class="step-title">
              <h4>第四步：设置文档范围</h4>
              <el-button size="small" type="primary" @click="handleChoose">选择文件</el-button>
            </div>
            <!-- <el-upload
              :on-change="handleChange"
              :on-remove="handleRemove"
              :file-list="fileList"
              :limit="5"
              :on-exceed="handleExceed"
              :auto-upload="false"
              :show-file-list="false"
            >
              <el-button type="primary">选择文件</el-button>
              </el-upload> -->
          </div>
          <div v-if="chooseList.length > 0" style="margin: 10px 0;">
            <h4>已选择文件：</h4>
            <ul>
              <li v-for="file in chooseList" :key="file.id" class="file-item">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
                <el-button type="danger" :icon="Delete" circle size="small" @click="handleRemove(file)" />
              </li>
            </ul>
          </div>
        </el-scrollbar>
      </el-form>
      <div class="button-group">
        <el-button type="primary" @click="submitForm">生成文档</el-button>
      </div>
    </el-col>
  </el-row>

  <!-- 对话框 -->
  <el-dialog title="模板预览" v-model="dialogVisible" width="80%">
    <div class="template-editor">
      <DocumentEditor id="templateEditor" :documentServerUrl="`${apikeys.onlyoffice_url}`" :config="templateConfig" />
    </div>
  </el-dialog>
  <ChooseFile ref="chooseFileRef" @success="getChooseList" />
</template>

<script setup>

import { DocumentEditor, } from '@onlyoffice/document-editor-vue'
import { Delete, Document } from '@element-plus/icons-vue'; // 引入图标
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TemplateAPI from '@/api/knowledge/tempmangement'
import { useUserStore } from '@/store/modules/user'
import * as UserAPI from '@/api/system/user'
import ChooseFile from './ChooseFile.vue'

import { v4 as uuidv4 } from 'uuid'
const userStore = useUserStore()
const apikeys = userStore.getAppConfig
console.log(apikeys)
const message = useMessage() // 消息弹窗

const loading = ref(false)
const dialogVisible = ref(false)
const formData = reactive({
  documentType: '',
  title: '',
  documentTemplate: ''
})
const fileList = ref([]);
const tempOptions = ref([])
const chooseFileRef = ref()

const config = ref({
  document: {
    fileType: 'docx',
    key: uuidv4(),
    title: '技术规范书',
    url: `${apikeys.file_server_url}/init.docx`
  },
  documentType: 'word',
  editorConfig: {
    lang: "zh-CN",
    mode: 'edit'
  }
})

const templateConfig = ref({
  document: {
    fileType: 'docx',
    key: uuidv4(),
    title: '技术规范书',
    url: `${apikeys.file_server_url}/init.docx`
  },
  documentType: 'word',
  editorConfig: {
    lang: "zh-CN",
    mode: 'view' // 设置为只读模式
  }
});

const submitForm = async () => {
  loading.value = true
  const ids = chooseList.value.map(item => item.id)
  const names = chooseList.value.map(item => item.name)
  const params = {
    docTitle: formData.title,
    klTemplateId: formData.documentTemplate,
    filePrefix: formData.documentType,
    fileName: docKey.value + '.docx',
    fileKey: docKey.value,
    scope: ids,
    scopeName: names
  }
  try {
    const response = await TemplateAPI.createDoc(params)
    loading.value = false
    clearLocal()
    const { fileKey, fileUrl, fileName } = response.data
    const { id: userId, username: userName } = await getUserInfo()
    config.value = {
      document: {
        fileType: 'docx',
        key: fileKey,
        title: fileName,
        url: fileUrl,
        permissions: {
          comment: true,
          download: true,
          modifyContentControl: true,
          modifyFilter: true,
          print: false,
          edit: true,
          fillForms: true,
          review: true
        }
      },
      documentType: "word",
      editorConfig: {
        lang: "zh-CN",
        callbackUrl: `${apikeys.file_callback_url}`,
        customization: {
          commentAuthorOnly: false,
          comments: true,
          compactHeader: false,
          compactToolbar: true,
          feedback: false,
          plugins: true,
          autosave: true,
          forcesave: true
        },
        user: {
          id: userId,
          name: userName
        },
      }
    }
  } catch (error) {
    console.log('createDoc error', error);
  }
}

const handleChoose = () => {
  chooseFileRef.value.open()
}
// 获取模板列表
const getTemplateList = async () => {
  const response = await TemplateAPI.getCloudList()
  tempOptions.value = response
}
// 文件选择变化时的回调
const handleChange = (file) => {
  fileList.value.push(file); // 将新文件添加到文件列表
};

// 文件移除时的回调
const handleRemove = (file) => {
  const index = chooseList.value.findIndex((item) => item.id === file.id);
  if (index !== -1) {
    chooseList.value.splice(index, 1); // 从文件列表中移除
  }
  // 处理localstorage里面的数据
  const documentChooseList = JSON.parse(localStorage.getItem('documentChooseList'))
  const knowledgeChooseList = JSON.parse(localStorage.getItem('knowledgeChooseList'))
  const documentIndex = documentChooseList.findIndex((item) => item.id === file.id)
  const knowledgeIndex = knowledgeChooseList.findIndex((item) => item.id === file.id)
  if (documentIndex !== -1) {
    documentChooseList.splice(documentIndex, 1)
    localStorage.setItem('documentChooseList', JSON.stringify(documentChooseList))
  }
  if (knowledgeIndex !== -1) {
    knowledgeChooseList.splice(knowledgeIndex, 1)
    localStorage.setItem('knowledgeChooseList', JSON.stringify(knowledgeChooseList))
  }
};

// 文件超出限制时的回调
const handleExceed = () => {
  message.warning('最多只能上传 5 个文件');
};

// 在组件卸载或页面刷新时清理数据
onMounted(() => {
  window.onbeforeunload = function () {
    clearLocal()
  };
});

const clearLocal = () => {
  localStorage.removeItem('documentChooseList');
  localStorage.removeItem('knowledgeChooseList');
}

const handleTemplate = () => {
  if (formData.documentTemplate === '') {
    message.warning('请选择模板')
    return
  }
  tempOptions.value.forEach((item) => {
    if (item.id === formData.documentTemplate) {
      templateConfig.value.document.url = item.templateFileUrl
      templateConfig.value.document.title = item.templateFileName
    }
  })
  dialogVisible.value = true
}
// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  }
};

const getUserInfo = async () => {
  const response = await UserAPI.getUserProfileById(userStore.getUser.id)
  return response
}
const docKey = ref('')
const docTitle = ref('')

onMounted(async () => {
  getTemplateList()
  const { id: userId, username: userName } = await getUserInfo()
  docKey.value = uuidv4() + '-klDoc'
  docTitle.value = docKey.value + '.docx'
  config.value = {
    document: {
      fileType: 'docx',
      key: docKey.value,
      title: docTitle.value,
      url: `${apikeys.file_server_url}/init.docx`,
      permissions: {
        comment: true,
        download: true,
        modifyContentControl: true,
        modifyFilter: true,
        print: false,
        edit: true,
        fillForms: true,
        review: true
      }
    },
    documentType: "word",
    editorConfig: {
      lang: "zh-CN",
      callbackUrl: `${apikeys.file_callback_url}`,
      customization: {
        commentAuthorOnly: false,
        comments: true,
        compactHeader: false,
        compactToolbar: true,
        feedback: false,
        plugins: true,
        autosave: true,
        forcesave: true
      },
      user: {
        id: userId,
        name: userName
      },
    }
  }
  console.log(config.value);

})

const chooseList = ref([])
const getChooseList = (list) => {
  chooseList.value = list
}


</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: calc(100vh - 160px);
  border: 1px dashed blue;
}

.template-editor {
  height: 65vh;
  min-height: 500px;
}

.scrollbar {
  height: calc(100vh - 200px);
}

.form-col {
  background-color: #fff;
  padding: 10px;
  padding-top: 0;
  padding-bottom: 54px;
  position: relative;

  h4 {
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 10px;
  }
}

.button-group {
  position: absolute;
  margin-top: 20px;
  text-align: right;
  bottom: 10px;
  right: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.file-icon {
  font-size: 18px;
  color: #409eff;
  margin-right: 8px;
}

.file-name {
  flex: 1;
  margin-right: 10px;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  margin: 5px 0;
  font-size: 14px;
  vertical-align: middle;
}

.step-title {
  align-items: center;
  display: flex;
  justify-content: space-between;

  h4 {
    flex: 1;
    display: inline-block;
  }
}
</style>
