import request from '@/config/axios'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getAccessToken } from '@/utils/auth'
import { config } from '@/config/axios/config'
// 
export const saveKlModelSession = (data) => {
  return request.post({ url: '/v1/kownledge/KlModelSession/save', data })
}

export const getKlModelSession = (params) => {
  return request.get({ url: '/v1/kownledge/KlModelSession/page', params })
}
// 
export const updateKlModelSession = (data) => {
  return request.put({ url: '/v1/kownledge/KlModelSession', data })
}

export const deleteKlModelSession = (id) => {
  return request.delete({ url: `/v1/kownledge/KlModelSession/${id}` })
}

// 写入对话 /v1/kownledge/KlSessionMessage/save
export const saveKlSessionMessage = (data) => {
  return request.post({ url: '/v1/kownledge/KlSessionMessage/save', data })
}


export const sendMessage = async (
  data,
  ctrl,
  onMessage,
  onError,
  onClose,
) => {
  const token = getAccessToken()
  console.log('token', token);

  return fetchEventSource(`${config.base_url}/v1/kownledge/KlSessionMessage/chatStream`, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    },
    openWhenHidden: true,
    body: JSON.stringify(data),
    onmessage: onMessage,
    onerror: onError,
    onclose: onClose,
    signal: ctrl.signal
  })
}

export const getKlSessionMessage = (params) => {
  return request.get({ url: `/v1/kownledge/KlSessionMessage/page`, params })
}

export const getKlMessageConfig = (params) => {
  return request.get({ url: `/v1/kownledge/klMessageConfig/list`, params })
}


export const saveKlMessageConfig = (data) => {
  return request.post({ url: `/v1/kownledge/klMessageConfig`, data })
}


export const deleteKlSessionMessage = (id) => {
  return request.delete({ url: `/v1/kownledge/KlSessionMessage/${id}` })
}


export const deleteKlSessionMessageBySessionId = (klModelSessionId) => {
  return request.delete({ url: `/v1/kownledge/KlSessionMessage/clear/${klModelSessionId}` })
}
