<template>
  <el-dialog v-model="dialogVisible" width="700" title="新建文件">
    <div class="content">
      <el-scrollbar max-height="420px" >
        <el-form
          ref="formRef"
          v-loading="formLoading"
          :model="formData"
          :rules="formRules"
          label-position="top"
          label-width="100px"
        >
          <el-form-item label="文件名称" prop="localName">
            <el-input v-model="formData.localName" maxlength="40" placeholder="请输入文件名称">
              <template #append>
                {{ fileType }}
              </template>
            </el-input>
          </el-form-item>
          <!-- 文档标题 -->
          <!-- <p class="ismust">文档主题 </p> -->
          <el-form-item label="文档主题" prop="title">
            <el-input v-model="formData.title" maxlength="50" />
          </el-form-item>
          <!-- <div class="step-title">
            <p class="ismust">选择模板</p>
            <el-button size="small" type="primary" @click="handleTemplate">查看模板</el-button>
          </div> -->
          <el-form-item label="选择模板" prop="documentTemplate">
            <el-select v-model="formData.documentTemplate" placeholder="请选择模板">
              <el-option
                v-for="item in tempOptions"
                :key="item.id"
                :label="item.templateFileName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <div>
            <div class="step-title">
              <p class="ismust">设置文档范围</p>
              <el-button size="small" type="primary" @click="handleChoose">选择文件</el-button>
            </div>
          </div>
          <div v-if="chooseList.length > 0" style="margin: 10px 0">
            <p>已选择文件：</p>
            <ul>
              <li v-for="file in chooseList" :key="file.id" class="file-item">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
                <el-button
                  type="danger"
                  :icon="Delete"
                  circle
                  size="small"
                  @click="handleRemove(file)"
                />
              </li>
            </ul>
          </div>
          <div>
            <div class="step-title">
              <p>招标检索</p>
              <el-button size="small" type="primary" @click="handleChooseSearch">选择文件</el-button>
            </div>
          </div>
          <div v-if="chooseSearchList.length > 0" style="margin: 10px 0">
            <p>已选择文件：</p>
            <ul>
              <li v-for="file in chooseSearchList" :key="file.id" class="file-item">
                <el-icon class="file-icon">
                  <Document />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
                <el-button
                  type="danger"
                  :icon="Delete"
                  circle
                  size="small"
                  @click="handleSearchRemove(file)"
                />
              </li>
            </ul>
          </div>
        </el-form>
      </el-scrollbar>
    </div>
    <ChooseFile ref="chooseFileRef"  key="documentChooseList" localDocName="documentChooseList" localKnowName="knowledgeChooseList" @success="getChooseList" />
    <ChooseFile ref="chooseSearchFileRef" key="documentSearchChooseList" localDocName="documentSearchChooseList" localKnowName="knowledgeSearchChooseList" :limit="1" @success="getChooseSearchList" />
    <el-dialog title="模板预览" v-model="templateDialogVisible" width="80%">
      <div class="template-editor" style="height: 600px;">
        <DocumentEditor id="templateEditor" :documentServerUrl="`${apikeys.onlyoffice_url}`" :config="templateConfig" />
      </div>
    </el-dialog>
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </el-dialog>
</template>
  <script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as FolderAPI from '@/api/knowledge/docmangement'
import { DocumentEditor } from '@onlyoffice/document-editor-vue'
import TagInput from '@/components/TagInput/index.vue'
import { useUserStore } from '@/store/modules/user'
import ChooseFile from '../../docGeneration/ChooseFile.vue'
import { getCloudLabelPage } from '@/api/knowledge/docmangement'
import { FolderOpened, Document, Delete } from '@element-plus/icons-vue'
import * as TemplateAPI1 from '@/api/knowledge/tempmangement'

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'CreatFile' })

const emit = defineEmits(['success'])

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const defaultTags = ref([])
const userStore = useUserStore()
const apikeys = userStore.getAppConfig

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false)
const chooseFileRef = ref()
const chooseSearchFileRef = ref()
const chooseList = ref([])
const chooseSearchList = ref([])
const tempOptions = ref([])
const formRef = ref()
const formData = ref({
  localName: '',
   documentTemplate: '',

})
const formRules = reactive({
  localName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' },
    { min: 1, max: 50, message: '请输入正确的内容', trigger: 'blur' }
  ]
})
// 当前路径
const path = ref({})
const fileType = ref('docx')
/** 打开弹窗 */
const open = async (pathParams, type) => {
  // fileId.value = row.id
  resetForm()
  clearLocal()
  path.value = pathParams
  await getTemplateList()
  dialogVisible.value = true
  fileType.value = type
  // getLabel()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const handleChoose = () => {
  chooseFileRef.value.open()
}

const handleChooseSearch = () => {
  chooseSearchFileRef.value.open()
}

const getChooseList = (list) => {
  chooseList.value = list
}
const getChooseSearchList = (list) => { 
  if(list.length > 1) {
    message.warning('只能选择一个文件')
    return
  }
  chooseSearchList.value = list
}

const getTemplateList = async () => {
  try {
    const res = await TemplateAPI1.getCloudList()
    tempOptions.value = res.map(item => ({
      ...item,
      id: `${item.id}`,
    }))
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  }
}

const handleTemplate = () => {
  if (!formData.documentTemplate) {
    message.warning('请先选择模板')
    return
  }
  const template = tempOptions.value.find(t => t.id === formData.documentTemplate)
  console.log(template);
  if (template) {
    templateConfig.value.document.url = template.templateFileUrl
    templateConfig.value.document.title = template.templateFileName
    templateConfig.value.document.key = uuidv4()
    templateDialogVisible.value = true
  }
}

const submitLoading = ref(false)
const submitForm = async () => {
  const isValid = await formRef.value.validate()
  if (!isValid) return
  const data = formData.value
  const params = {
    ...data,
    fileName: `${data.localName}.${fileType.value}`,
    filePath: path.value.id ? `${path.value.filePath}/${data.localName}.${fileType.value}` : null,
    parentId: path.value.id ? path.value.id : null,
    level: path.value.id ? parseInt(path.value.level) + 1 : '1',
    filePrefix: fileType.value,
    scope: chooseList.value,
    fileKey: uuidv4() + '-klDoc'
  }
  if(chooseSearchList.value && chooseSearchList.value.length) {
    params.searchFile = chooseSearchList.value[0]
  }
  submitLoading.value = true
  emit('success', params, () => {
    submitLoading.value = false
    dialogVisible.value = false
  })
}

const handleRemove = (file) => {
  const index = chooseList.value.findIndex(item => item.id === file.id)
  if (index !== -1) {
    chooseList.value.splice(index, 1)
  }
  const documentChooseList = JSON.parse(localStorage.getItem('documentChooseList'))
  const knowledgeChooseList = JSON.parse(localStorage.getItem('knowledgeChooseList'))
  const documentIndex = documentChooseList.findIndex((item) => item.id === file.id)
  const knowledgeIndex = knowledgeChooseList.findIndex((item) => item.id === file.id)
  if (documentIndex !== -1) {
    documentChooseList.splice(documentIndex, 1)
    localStorage.setItem('documentChooseList', JSON.stringify(documentChooseList))
  }
  if (knowledgeIndex !== -1) {
    knowledgeChooseList.splice(knowledgeIndex, 1)
    localStorage.setItem('knowledgeChooseList', JSON.stringify(knowledgeChooseList))
  }
}

const handleSearchRemove = (file) => { 
  const index = chooseSearchList.value.findIndex(item => item.id === file.id)
  if (index !== -1) {
    chooseSearchList.value.splice(index, 1)
  }
  const documentChooseList = JSON.parse(localStorage.getItem('documentSearchChooseList'))
  const knowledgeChooseList = JSON.parse(localStorage.getItem('knowledgeSearchChooseList'))
  const documentIndex = documentChooseList.findIndex((item) => item.id === file.id)
  const knowledgeIndex = knowledgeChooseList.findIndex((item) => item.id === file.id)
  if (documentIndex !== -1) {
    documentChooseList.splice(documentIndex, 1)
    localStorage.setItem('documentSearchChooseList', JSON.stringify(documentChooseList))
  }
  if (knowledgeIndex !== -1) {
    knowledgeChooseList.splice(knowledgeIndex, 1)
    localStorage.setItem('knowledgeSearchChooseList', JSON.stringify(knowledgeChooseList))
  }
}

const clearLocal = () => {
  localStorage.removeItem('documentChooseList');
  localStorage.removeItem('knowledgeChooseList');
  localStorage.removeItem('documentChooseList');
  localStorage.removeItem('knowledgeChooseList');
  localStorage.removeItem('knowledgeSearchChooseList');
  localStorage.removeItem('documentSearchChooseList');
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    localName: '',
    label: []
  }
  formRef.value?.resetFields()
}

/** 提交表单 */
</script>
  
  <style lang="scss" scoped>
.breadcrumb-wrapper {
  display: flex;
  height: 32px;
  align-items: center;
  padding: 0 20px 0 0;
  .back-button {
    padding: 0;
    font-size: 14px;
  }
}
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #eee;
}
.file-icon {
  font-size: 18px;
  color: #409eff;
  margin-right: 8px;
}

.file-name {
  flex: 1;
  margin-right: 10px;
}

.step-title {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  p {
    flex: 1;
    display: inline-block;
    margin: 0;
  }
}

</style>
  