<template>
  <Dialog
    v-model="dialogVisible"
    :width="700"
    :before-close="beforeClose"
    :scroll="true"
    title="选择文件"
  >
    <div class="content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="知识库" name="first" />
        <el-tab-pane label="本地文件" name="second" />
      </el-tabs>
      <div class="knowlage-wrapper" v-if="activeName === 'first'">
        <div class="kl-content" v-if="isShowKL">
          <el-row :gutter="12">
            <el-col :span="8" v-for="(item, index) in knowledgeList" :key="index">
              <div class="knowledge-list" @click.stop="handleClickKnowledge(item)">
                <div class="list-header">
                  <span>
                    <Icon icon="fa:user-o" :size="24" />
                  </span>
                  <span></span>
                </div>
                <div class="title">{{ item.name }}</div>
                <div class="footer">
                  <p><Icon icon="ep:clock" class="mr-4px" />{{ item.createTime }}</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="content" v-else>
          <div class="breadcrumb-wrapper">
            <!-- 返回上一级按钮 -->
            <el-button v-if="path.length > 2" @click="handleGoBack" type="text" class="back-button">
              返回上一级
            </el-button>
            <el-divider direction="vertical" :style="computedStyle" />
            <!-- 路径导航 -->
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="(item, index) in path"
                :key="index"
                @click="handleBreadcrumbClick(index)"
              >
                <span style="cursor: pointer">{{ item.fileName }}</span>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>

        <!-- 文件列表 -->
        <div>
          <div
            class="file-item"
            @click="handleNameClick(item)"
            v-for="(item, index) in fileListFilter"
            :key="index"
          >
            <span class="file-name">
              <!-- {{ item.fileName }} -->
              <div class="flex align-center" v-if="item.filePrefix">
                <span>
                  <el-icon class="mr-[4px]">
                    <Document />
                  </el-icon>
                </span>
                <el-tooltip :content="item.fileName" placement="top">
                  <span class="ellipsis">
                    {{ item.fileName }}
                  </span>
                </el-tooltip>
              </div>
              <div class="flex align-center" v-else>
                <span>
                  <el-icon class="mr-[4px]">
                    <FolderOpened />
                  </el-icon>
                </span>
                <span class="ellipsis">
                  <el-tooltip :content="item.fileName" placement="top">
                    <span>
                      {{ item.fileName }}
                    </span>
                  </el-tooltip>
                </span>
              </div>
            </span>

            <!-- <el-tooltip content="选择文件"> -->
            <!-- <el-button 
                  v-if="item.filePrefix && !item.choose" 
                  class="choose-button" 
                  type="primary"
                  @click="handleChooseKnowlage(item)"
                  link>
                  <Icon icon="ep:document-checked" />
                </el-button> -->
            <el-checkbox
              class="choose-button"
              v-if="item.filePrefix"
              v-model="item.choose"
              @change="handleChooseFile(item)"
              size="large"
            />
            <!-- </el-tooltip> -->
            <!-- <el-tooltip content="取消选择">
                <el-button 
                  v-if="item.filePrefix && item.choose" 
                  class="choose-button" 
                  type="primary"
                  @click="handleKnowlageCancel(item)"
                  link>
                  <Icon icon="ep:document-delete" />
                </el-button>
              </el-tooltip> -->
          </div>
        </div>
      </div>
      <div v-if="activeName === 'second'">
        <div style="text-align: right">
          <el-space style="">
            <el-upload
              ref="uploadRef"
              :action="uploadUrl"
              :auto-upload="true"
              :before-upload="handleBeforeUpload"
              :on-success="submitFormSuccess"
              :on-error="handleUploadError"
              :headers="uploadHeaders"
              :show-file-list="false"
              accept="*"
              :multiple="false"
              :disabled="uploadLoading"
            >
              <el-button type="primary" :loading="uploadLoading">
                {{ uploadLoading ? '上传中...' : '选择文件' }}
              </el-button>
              <template #tip>
                <div class="el-upload__tip"> 文件大小不能超过 10MB </div>
              </template>
            </el-upload>
            <!-- <el-button type="primary" @click="getDocumentList" style="position: relative; top: -12px;">刷新</el-button> -->
          </el-space>
        </div>
        <el-table :data="documentListFilter">
          <el-table-column label="名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="解析状态" align="center" prop="run" width="250px">
            <template #default="scope">
              <div class="run-document">
                <el-popover placement="top" :width="400">
                  <template #reference>
                    <el-tag :type="RunningStatusMap[scope.row.run].type">{{
                      RunningStatusMap[scope.row.run].name
                    }}</el-tag>
                  </template>
                  <p>解析结果：{{ scope.row.progress_msg ? scope.row.progress_msg : '-' }}</p>
                </el-popover>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center">
            <template #default="{ row }">
              <el-tooltip content="解析">
                <el-button
                  class="choose-button"
                  type="primary"
                  v-if="row.run === 'UNSTART'"
                  @click="handleRunDocumentByIds(row)"
                  link
                >
                  <Icon icon="ep:refresh" />
                </el-button>
              </el-tooltip>
              <el-checkbox
                v-if="row.run === 'DONE'"
                v-model="row.choose"
                @change="chooseDoc(row)"
                size="large"
              />
              <!-- <el-tooltip content="选择文件">
                <el-button 
                  v-if="!row.choose && row.run === 'DONE'" 
                  class="choose-button" 
                  type="primary"
                  @click="handleChooseDocument(row)"
                  link>
                  <Icon icon="ep:document-checked" />
                </el-button>
              </el-tooltip> -->
              <!-- <el-tooltip content="取消选择">
                <el-button 
                  v-if="row.choose && row.run === 'DONE'" 
                  class="choose-button" 
                  type="primary"
                  @click="handleDocumentCancel(row)"
                  link>
                  <Icon icon="ep:document-delete" />
                </el-button>
              </el-tooltip> -->
            </template>
          </el-table-column>
        </el-table>
        <!-- <Pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
          @pagination="getDocumentList" /> -->
      </div>
    </div>
    <template #footer>
      <el-button type="primary" @click="submitForm">确认</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as FolderAPI from '@/api/knowledge/docmangement'
import { useUserStore } from '@/store/modules/user'
import axios from 'axios'
import { computed } from 'vue'
import { uploadFolderFile } from '@/api/knowledge/docmangement'

import { FolderOpened, Document } from '@element-plus/icons-vue'

const userStore = useUserStore()
const apikeys = userStore.getAppConfig

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'MoveDialog' })

const props = defineProps({
  temporaryKbId: {
    type: String,
    required: true
  },
  token: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['success'])

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// 使用计算属性动态获取当前使用的 kbId
const currentKbId = computed(() => {
  return activeName.value === 'first' ? apikeys.kl_cloud_data : props.temporaryKbId
})

// 使用计算属性动态获取当前使用的 token
const currentToken = computed(() => {
  return activeName.value === 'first' ? apikeys.ragflow_token : props.token
})

// 修改 uploadUrl 的计算属性
const uploadUrl = computed(() => {
  return `${apikeys.ragflow_url}/api/v1/datasets/${currentKbId.value}/documents`
})

// 修改 uploadHeaders 的计算属性
const uploadHeaders = computed(() => {
  return {
    Authorization: currentToken.value
  }
})

const RunningStatusMap = {
  UNSTART: {
    label: 'UNSTART',
    color: 'cyan',
    name: '未启动',
    type: 'info'
  },
  RUNNING: {
    label: 'RUNNING',
    color: 'blue',
    name: '解析中',
    type: 'primary'
  },
  CANCEL: { label: 'CANCEL', color: 'orange', name: '取消', type: 'warning' },
  DONE: { label: 'SUCCESS', color: 'geekblue', name: '成功', type: 'success' },
  FAIL: { label: 'FAIL', color: 'red', name: '失败', type: 'danger' }
}

const total = ref(0)

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const dialogType = ref('')
const knowledgeList = ref([])
const isShowKL = ref(true)
// 当前路径
const path = ref([{ id: '', fileName: '', level: '1' }])
// 当前文件夹下的文件列表
const fileList = ref([])
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})
const documentList = ref([])
const documentIdList = ref([])

const activeName = ref('first')
// const fakeData = {
//   root: [
//     { id: '1', name: '文件夹1', type: 'folder', size: 0, uploadDate: '2023-10-01', parseMethod: '自动', shareEnabled: false, parseStatus: '已解析' },
//     { id: '2', name: '文件1.txt', type: 'file', size: 1024, uploadDate: '2023-10-02', parseMethod: '手动', shareEnabled: true, parseStatus: '未解析' },
//   ],
//   '1': [
//     { id: '3', name: '子文件夹1', type: 'folder', size: 0, uploadDate: '2023-10-03', parseMethod: '自动', shareEnabled: false, parseStatus: '已解析' },
//     { id: '4', name: '文件2.txt', type: 'file', size: 2048, uploadDate: '2023-10-04', parseMethod: '手动', shareEnabled: true, parseStatus: '未解析' },
//   ],
//   '3': [
//     { id: '5', name: '文件3.txt', type: 'file', size: 512, uploadDate: '2023-10-05', parseMethod: '自动', shareEnabled: false, parseStatus: '已解析' },
//   ],
// };

// 加载文件夹内容
const loadFolderContent = async (row) => {
  // fileList.value = fakeData[folderId] || [];
  const { id, level, datasetId } = row
  const response = await FolderAPI.getCloudList({
    parentId: id,
    level: id ? parseInt(level) + 1 : level,
    datasetId,
    analysisStatus: '2'
  })
  fileList.value = response.map((item) => {
    return {
      ...item,
      choose: false
    }
  })
}
const handleClickKnowledge = (item) => {
  isShowKL.value = false
  path.value.push({
    id: '',
    datasetId: item.datasetId,
    fileName: item.name,
    level: '1'
  })
  setTimeout(() => {
    handleSearch()
  }, 100)
}

const handleSearchKlList = async () => {
  const response = await FolderAPI.getKlDatasetPermission()
  knowledgeList.value = response || []
}

const handleSearch = () => {
  loadFolderContent(path.value[path.value.length - 1])
}

const knowledgeChooseList = ref([])
const handleChooseKnowlage = (item) => {
  knowledgeChooseList.value.push(item)
}

const handleChooseFile = (item) => {
  if (item.choose) {
    handleChooseKnowlage(item)
  } else {
    handleKnowlageCancel(item)
  }
}

const chooseDoc = (item) => {
  if (item.choose) {
    handleChooseDocument(item)
  } else {
    handleDocumentCancel(item)
  }
}

const fileListFilter = computed(() => {
  // fileList 与knowledgeChooseList 与knowledgeChooseList中值与fileList中值相同的数据 将fileList中的那条数据choose改为true
  return fileList.value.map((item) => {
    const isChoose = knowledgeChooseList.value.find((chooseItem) => chooseItem.id === item.id)
    return {
      ...item,
      choose: !!isChoose
    }
  })
})

const handleKnowlageCancel = (item) => {
  knowledgeChooseList.value = knowledgeChooseList.value.filter(
    (chooseItem) => chooseItem.id !== item.id
  )
}

// 点击名称文本进入下一级
const handleNameClick = (row) => {
  if (!row.filePrefix) {
    path.value.push(row)
    loadFolderContent(row)
  }
}

const computedStyle = computed(() => {
  if (path.value.length > 1) {
    return 'margin: 0 8px;'
  }
  return 'margin: 0 8px 0 0;'
})

// 返回上一级
const handleGoBack = () => {
  if (path.value.length > 1) {
    path.value.pop() // 移除最后一项
    loadFolderContent(path.value[path.value.length - 1]) // 加载上一级文件夹内容
  }
}

// 点击路径导航返回上一级
const handleBreadcrumbClick = (index) => {
  total.value = 0
  if (index === 0) {
    isShowKL.value = true
    fileList.value = []
    path.value = [{ id: '', fileName: '知识库', level: '1' }]
  } else {
    path.value = path.value.slice(0, index + 1)
    loadFolderContent(path.value[index])
  }
}

/** 打开弹窗 */
const open = async (selectedFiles = []) => {
  // 重置状态
  path.value = [{ id: '', fileName: '根目录', level: '1' }]
  knowledgeChooseList.value = [] // 清空知识库选择列表
  documentChooseList.value = [] // 清空本地文件选择列表
  documentIdList.value = [] // 清空文档ID列表

  // 加载文件夹内容
  // loadFolderContent(path.value[path.value.length - 1])
  handleSearchKlList()
  dialogVisible.value = true

  // 如果有已选择的文件，则进行回显
  if (selectedFiles && selectedFiles.length > 0) {
    console.log('selectedFiles', selectedFiles)
    // 根据已选择的文件回显选中状态
    selectedFiles.forEach((file) => {
      if (file.arrangeBaseId === apikeys.kl_cloud_data) {
        // 知识库文件
        knowledgeChooseList.value.push({
          id: file.fileId,
          fileName: file.fileName,
          fileUrl: file.fileUrl,
          ...file
        })
      } else {
        // 本地文件
        documentChooseList.value.push({
          id: file.fileId,
          name: file.fileName,
          fileUrl: file.fileUrl,
          ...file
        })
      }
    })

    // 更新文档列表中的选中状态
    await getDocumentList()
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 添加文件大小限制常量
const MAX_FILE_SIZE = 10 * 1024 * 1024

// 添加上传 loading 状态
const uploadLoading = ref(false)

// 修改上传前的处理方法
const handleBeforeUpload = (file) => {
  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    message.error('文件大小不能超过 10MB')
    return false
  }
  return true
}
const timer = ref(null)
const parseTimers = ref(new Map())
// 清除所有定时器的方法
const clearAllTimers = () => {
  parseTimers.value.forEach((timer, id) => {
    clearInterval(timer)
    console.log(`已清除ID为 ${id} 的定时器`)
  })
  parseTimers.value.clear()
}

// 清除单个定时器的方法
const clearParseTimer = (id) => {
  if (parseTimers.value.has(id)) {
    clearInterval(parseTimers.value.get(id))
    parseTimers.value.delete(id)
  }
}
// 修改上传成功的处理方法
const submitFormSuccess = async (response, file, fileList) => {
  if (response.code === 0) {
    try {
      uploadLoading.value = true
      console.log('response', response)
      const { data } = response
      // 1. 上传到临时知识库成功后，再上传到云文档
      const formData = new FormData()
      const newFile = new File(
        [file.raw], // 文件内容（Blob/ArrayBuffer）
        uuidv4() + file.raw.name, // 新文件名
        {
          type: file.raw.type, // 保持原 MIME 类型
          lastModified: file.raw.lastModified // 保持原修改时间
        }
      )
      const oldFile = {
        ...file,
        name: uuidv4() + file.name,
        raw: newFile
      }
      formData.append('file', oldFile.raw)

      // 2. 调用 uploadFolderFile 上传到云文档
      const response1 = await uploadFolderFile(formData)

      // 3. 保存文档ID和更新文档列表
      documentIdList.value.push(
        ...data.map((item) => {
          return {
            id: item.id,
            fileUrl: response1.data.fileUrl
          }
        })
      )
      const params = {
        fileUrl: response1.data.fileUrl,
        type: '4',
        documentId: data[0].id
      }
      const analyResponse = await FolderAPI.getCloudAnalysisBack(params)
      clearParseTimer(analyResponse.documentId)
      const timer = setInterval(async () => {
        const resultResponse = await FolderAPI.getCloudAnalysisBackResults({
          ...analyResponse,
          type: '4'
        })
        if (resultResponse.analysisStatus !== '1') {
          getDocumentList()
          clearParseTimer(analyResponse.documentId)
        }
      }, 20000)
      parseTimers.value.set(analyResponse.documentId, timer)
      await getDocumentList() // 获取并过滤文档列表
      // 4. 刷新文档列表
      message.success('上传成功')
      loadFolderContent(path.value[path.value.length - 1])
    } catch (error) {
      console.error('上传文件失败:', error)
      message.error('上传文件失败')
    } finally {
      uploadLoading.value = false
    }
  }
}

// 修改上传失败的处理
const handleUploadError = (error) => {
  console.error('上传失败:', error)
  message.error('上传失败')
  uploadLoading.value = false
}

const getDocumentList = async () => {
  try {
    const response = await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${currentKbId.value}/documents`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: currentToken.value,
        Accept: '*/*'
      },
      params: { offset: queryParams.pageNum, limit: queryParams.pageSize }
    })
    const { data } = response

    // 根据 documentIdList 过滤数据并添加额外字段
    const filteredDocs = (data?.data?.docs ?? [])
      .map((doc) => {
        const matchedDoc = documentIdList.value.find((id) => id.id === doc.id)
        if (matchedDoc) {
          return {
            ...doc,
            fileUrl: matchedDoc.fileUrl
          }
        }
        return doc
      })
      .filter((doc) => documentIdList.value.some((id) => id.id === doc.id))

    documentList.value = filteredDocs
    total.value = filteredDocs.length
  } finally {
  }
}

const handleChooseDocument = (item) => {
  documentChooseList.value.push(item)
}

const handleDocumentCancel = (item) => {
  documentChooseList.value = documentChooseList.value.filter(
    (chooseItem) => chooseItem.id !== item.id
  )
}

const documentChooseList = ref([])
const documentListFilter = computed(() => {
  return documentList.value.map((item) => {
    const isChoose = documentChooseList.value.find((chooseItem) => chooseItem.id === item.id)
    return {
      ...item,
      choose: !!isChoose
    }
  })
})

const handleRunDocumentByIds = async (row) => {
  const params = {
    fileUrl: row.fileUrl,
    type: '4',
    documentId: row.id
  }
  const analyResponse = await FolderAPI.getCloudAnalysisBack(params)
  timer.value = setInterval(async () => {
    const resultResponse = await FolderAPI.getCloudAnalysisBackResults({
      ...analyResponse,
      type: '4'
    })
    if (resultResponse.analysisStatus !== '1') {
      getDocumentList()
      clearInterval(timer.value)
    }
  }, 20000)
  return
  try {
    await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${currentKbId.value}/chunks`,
      method: 'post',
      headers: {
        Authorization: currentToken.value
      },
      data: {
        document_ids: [id]
      }
    })
    getDocumentList()
  } finally {
  }
}

// 删除知识库内容
const handleDelete = async () => {
  try {
    const response = await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${currentKbId.value}/documents`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: currentToken.value,
        Accept: '*/*'
      },
      params: { offset: queryParams.pageNum, limit: queryParams.pageSize }
    })
    const { data } = response
    const ids = data?.data?.docs?.map((item) => item.id)
    await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${currentKbId.value}/documents`,
      method: 'delete',
      headers: {
        Authorization: currentToken.value
      },
      data: {
        ids: ids
      }
    })
    getDocumentList()
  } catch (error) {
    console.log(error)
  }
}

/** 提交表单 */
const submitForm = async () => {
  let formData = []
  // 本地
  documentChooseList.value.forEach((item) => {
    formData.push({
      fileName: item.name,
      documentId: item.id,
      apiKey: props.temporaryKbId,
      Authorization: props.token,
      ...item
    })
  })
  // 知识库
  knowledgeChooseList.value.forEach((item) => {
    formData.push({
      id: item.id,
      name: item.fileName,
      apiKey: apikeys.kl_cloud_data,
      Authorization: apikeys.ragflow_token,
      ...item
    })
  })
  dialogVisible.value = false
  // 点击确认时将数据直接保存到localStorage中
  if (formData.length > 5) {
    message.error('最多选择5个文件')
    return
  }
  console.log('formData===', formData)
  emit('success', {
    fileList: formData,
    activeName: activeName.value
  })
}
onUnmounted(() => {
  clearAllTimers()
})

const beforeClose = (done) => {
  clearAllTimers()
  done()
}
</script>

<style lang="scss" scoped>
.breadcrumb-wrapper {
  display: flex;
  height: 32px;
  align-items: center;
  padding: 0 20px 0 0;

  .back-button {
    padding: 0;
    font-size: 14px;
  }
}

.file-item {
  line-height: 32px;
  padding: 8px;
  border-bottom: 1px solid #ccc;
  cursor: pointer;
  display: flex;

  .file-name {
    flex: 1;
  }

  .choose-button {
    width: 60px;
  }
}
.choose-button {
  height: 32px;
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 500px;
}
.kl-content {
  padding-top: 10px;
  padding: 10px 10px 0;
  .knowledge-list {
    height: 251px;
    display: flex;
    margin-bottom: 10px;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 5%);
    cursor: pointer;
    .list-header {
      display: flex;
      justify-content: space-between;
    }
    .title {
      color: #000000e0;
      margin: 10px 0;
      font-size: 24px;
      line-height: 32px;
      font-weight: 600;
      word-break: break-all;
    }
    .footer {
      p {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 12px;
        line-height: 22px;
      }
    }
  }
}
</style>
