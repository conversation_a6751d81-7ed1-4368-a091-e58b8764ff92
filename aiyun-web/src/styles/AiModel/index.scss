.ai-model-container {
    width: 100%;
    height: 100vh;
    display: flex;
    background-color: #f5f7fa;
    position: relative;
  }
  
  .node-panel {
    width: 150px;
    height: 100%;
    background-color: white;
    border-right: 1px solid #e6e6e6;
    display: flex;
    flex-direction: column;
    z-index: 1;
  }
  
  .panel-header {
    padding: 12px;
    font-weight: bold;
    border-bottom: 1px solid #e6e6e6;
  }
  
  .node-list {
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow-y: auto;
  }
  
  .node-item {
    padding: 8px 12px;
    border: 1px dashed;
    border-radius: 4px;
    cursor: move;
    user-select: none;
    text-align: center;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .node-item .el-icon {
    font-size: 16px;
  }
  
  .node-item:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }
  
  .node-item.start {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.end {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.llm {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.kb {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.template {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.http {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.search {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.test {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.parallel {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .node-item.loop {
    border-color: #aeccec;
    color: #409eff;
  }
  
  .flow-container {
    flex: 1;
    height: 100%;
    position: relative;
    overflow: hidden;
  }
  
  .flow-canvas {
    width: 100%;
    height: 100%;
  }
  
  .save-panel {
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  :deep(.vue-flow__minimap) {
    transform: scale(0.8);
    transform-origin: bottom right;
  }
  
  :deep(.vue-flow__node) {
    width: auto;
    background: transparent;
    border: none;
  }
  
  :deep(.vue-flow__edge-path) {
    stroke: #b1b1b7;
    stroke-width: 2;
    cursor: pointer;
  }
  
  :deep(.vue-flow__edge-path:hover) {
    stroke: #409eff;
  }
  
  .delete-button {
    position: absolute;
    top: -10px;
    right: -10px;
    cursor: pointer;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 50%;
    padding: 5px;
    z-index: 10;
  }