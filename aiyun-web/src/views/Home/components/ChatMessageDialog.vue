<template>
    <el-dialog 
      v-model="dialogVisible" 
      :close-on-click-modal="false" 
      fullscreen
      :title="activeConversation?.title">
      <div class="chat-list-dialog">
        <ChatMessage  ref="chatMessageRef" />
      </div>
    </el-dialog>
  </template>
  <script setup>

	import ChatMessage from '@/views/knowledge/dataTraining/ChatMessage.vue'
  
  defineOptions({ name: 'ChatMessageDialog' })
  
  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  
  const dialogVisible = ref(false) // 弹窗的是否展示
  const dialogTitle = ref('') 
  

	const chatMessageRef = ref(null)
  /** 打开弹窗 */
  const open = async (chatItem) => {
		dialogVisible.value = true;
		setTimeout(() => {
			chatMessageRef.value && chatMessageRef.value.handleConversationClick(chatItem)
		}, 100)
  }

  defineExpose({ open }) // 提供 open 方法，用于打开弹窗
  
  </script>
  
  <style lang="scss" scoped>
  .chat-list-dialog {
    position: relative;
    height: calc(100vh - 60px);
    background-color: var(--el-color-white);
  }
  </style>
  