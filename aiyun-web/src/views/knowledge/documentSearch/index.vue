<template>
    <div v-if="isFirst" class="content-header">
      <div>
        <img src="@/assets/images/search_logo.png" class="search-logo" alt="" />
      </div>
       <div class="input-group">
         <el-input
           v-model="queryParams.search"
           class="search-input"
           clearable
           placeholder="搜索"
           @keyup.enter="handleQuery"
         >
           <template #append>
             <el-button
               type="primary"
               class="search-btn"
               :loading="searchLoading"
               @click="handleQuery"
               >
               <Icon icon="ep:search" v-if="!searchLoading" color="#fff"/>
             </el-button>
           </template>
         </el-input>
       </div>
    </div>
    <div v-else>
      <div class="content">
        <div class="content-search">
          <el-input
            v-model="queryParams.search"
            class="search-input"
            
            placeholder="搜索"
          >
            <template #append>
              <el-button
                type="primary"
                class="search-btn"
                :loading="searchLoading"
                @click="handleQuery"
                >
                <Icon icon="ep:search" v-if="!searchLoading" color="#fff"/>
              </el-button>
            </template>
          </el-input>
        </div>
        <el-card class="card" v-loading="chatLoading">
          <template #header>
            <p style="vertical-align: middle;">智能问答</p>
          </template>
          <el-scrollbar max-height="200px">
            <MarkdownView class="left-text" :content="chatMessage" />
          </el-scrollbar>
        </el-card>
        <div v-loading="searchLoading">
          <el-card v-for="item in list" :key="item.id" class="card">
            <el-popover
              placement="top"
              trigger="hover"
              :width="'70vw'"
              popper-class="popover-answer"
            >
              <template #reference>
                <p class="card-content" v-html="item.highlight + '...'"></p>
              </template>
              <div v-html="item.content" class="answer-pop-content"></div>
            </el-popover>
            <p class="card-footer">{{ item.document_keyword }}</p>
          </el-card>
        </div>
      </div>
    </div>
  
    <!-- 表单弹窗：添加/修改 -->
    <FileForm ref="formRef" @success="getList" />
  </template>
  <script setup>
  
  import axios from 'axios'
  import MarkdownView from '@/components/MarkdownView/index.vue'
  import { fetchEventSource } from '@microsoft/fetch-event-source'
  import { useUserStore } from '@/store/modules/user'
  
  defineOptions({ name: 'DataSetSearch' })

  const userStore = useUserStore()
  const apikeys = userStore.getAppConfig
  
  const message = useMessage() // 消息弹窗
  const { t } = useI18n() // 国际化
  
  const list = ref([]) // 列表的数据
  const chatMessage = ref('')
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
    search: undefined,
  })
  const isFirst = ref(true)
  const searchLoading = ref(false)
  const chatLoading = ref(false)
  
  /** 查询列表 */
  const getList = async () => {
    try {
      searchLoading.value = true
      isFirst.value = false
      const response = await axios({
        url: `${apikeys.ragflow_url}/api/v1/retrieval`,
        method: 'post',
        headers: { 
          'Content-Type': 'application/json' ,
          Authorization: `${apikeys.ragflow_token}`,
          Accept: '*/*'
        },
        data: { question: queryParams.search, dataset_ids: [`${apikeys.kl_cloud_data}`] }
      })
      const { data } = response
      list.value = data.data.chunks
      console.log(list);
      // list.value = data.data.docs
    } finally {
      searchLoading.value = false
      isFirst.value = false
    }
  }
  
  
  const getChatMessage = async() => {
    chatLoading.value = true
    // const agent_id = '79d74f4cb3aa11ef9d3a0242c0a82002'
    const Authorization = `${apikeys.ragflow_token}`
    const url_one = `${apikeys.ragflow_url}/api/v1/chats/${apikeys.ragflow_doc_retrieval_chatId}/completions`
    const response = await fetchEventSource(url_one, {
      method: 'post',
      headers: {
        Authorization: Authorization,
        Accept: '*/*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        "question":queryParams.search,
        'session_id': `${apikeys.ragflow_doc_retrieval_sessionId}`,
        "stream": true
      }),
      onmessage: async(msg) => {
        const { code, data, message } = JSON.parse(msg.data)
        if(data.id) {
          chatLoading.value = false
          chatMessage.value = data.answer
        }
      },
      onerror: (err) => {
        message.alert(`对话异常! ${err}`)
      },
    })
    // const response = await axios({
    //   url: url_one,
    //   method: 'post',
    //   headers: {
    //     Authorization: Authorization,
    //     Accept: '*/*',
    //     'Content-Type': 'application/json'
    //   },
    //   data: {
    //     "question": queryParams.search,
    //     "stream": true,
    //     'session_id': '21368dfef58011ef83d90242ac120006'
    //   }
    // })
    // chatLoading.value = false
    // console.log(response);
    
    // const answer = response.data.data.answer
    // chatMessage.value = answer
  }
  
  /** 搜索按钮操作 */
  const handleQuery = () => {
    if (!queryParams.search) {
      message.info('请输入搜索内容')
      return
    }
    getList()
    getChatMessage()
  }
  
  </script>
  <!-- style="max-width: 600px; height: 36px;" -->
  <style lang="scss" scoped>
    .content-header {
      position: sticky;
      top: 36%;
      // display: flex;
      // justify-content: center;
      .input-group {
        display: flex;
        justify-content: center;
        margin-top: 30px;
      }
      .search-logo {
        width: 320px;
        display: block;
        margin: 0 auto;
      }
    }
    .search-input {
      width: 600px;
      height: 40px;
      :deep(.el-input__wrapper) {
        border-start-start-radius: 30px !important;
        border-end-start-radius: 30px !important;
      }
      :deep(.el-input-group__append) {
        border-start-end-radius: 30px !important;
        border-end-end-radius: 30px !important;
        overflow: hidden;
        :hover {
          background-color: #1890ff;
        }
      }
      .search-btn {
        height: 100%;
        background-color: #1890ff;
        border-radius: 0;
        :deep(.is-loading) {
          color: aliceblue;
        }
      }
    }
    .content {
      width: 80%;
      max-width: 1100px;
      min-width: 400px;
      margin: 0 auto;
      padding-top: 50px;
      position: relative;
      .content-search {
        width: 100%;
        display: flex;
        justify-content: center;
        position: absolute;
        top: 0;
        .search-input {
          width: 100%;
        }
      }
      .card {
        margin-bottom: 16px;
      }
    }
    .card-content {
      font-size: 14px;
    }
    :deep(.el-card__body) {
      em {
        color: red;
        font-style: normal;
      }
    }
    .card-footer {
      font-size: 14px;
      margin-top: 5px;
    }
  </style>
  