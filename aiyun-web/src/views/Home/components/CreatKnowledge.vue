<template>
    <el-dialog
      v-model="dialogVisible" 
      fullscreen
			:before-close="handleBeforeClose"
      destroy-on-close
      title="维护知识库">
      <base-knowledge-setting
        :isPermission="false"
        @click-knowledge="handleClickKnowledge"
        :getData="getData"
        v-if="isBaseKnowledge"
      />
      <knowledge-home
        @path-back="handlePathBack"
        ref="KnowledgeHomeRef"
        :chooseKnowledge="chooseKnowledge"
        v-else
       />
    </el-dialog>  
  </template>
  <script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
  import * as FolderAPI from '@/api/knowledge/docPersonal'
  import { getKlDataset, getKlDatasetPermission } from '@/api/knowledge/docmangement'
  import BaseKnowledgeSetting from '@/views/knowledge/docManagement/components/BaseKnowledgeSetting.vue'
  import KnowledgeHome from '@/views/knowledge/docManagement/KnowledgeHome.vue'
  
  
  import { FolderOpened,  Document, } from '@element-plus/icons-vue'
  
  import { v4 as uuidv4 } from 'uuid'
  
  defineOptions({ name: 'CreatKnowledge' })
  
  const emit = defineEmits(['handleCloseDialog'])
  
  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  const isBaseKnowledge = ref(true)
  
  const dialogVisible = ref(false) // 弹窗的是否展示
  const formLoading = ref(false)
  const formRef = ref()
  const KnowledgeUpdateRef = ref()
  const formData = ref({
    name: '',
  })
  const formRules = reactive({
    name: [
      { required: true, message: '请输入知识库名称', trigger: 'blur' },
      { min: 1, max: 50, message: '请输入正确的内容', trigger: 'blur' }
    ]
  })
  // 当前路径
  const path = ref({})

  const handleCreate = () => {
		KnowledgeUpdateRef.value.open('create')
	}
  /** 打开弹窗 */
  const open = async (flag = false, item) => {
    dialogVisible.value = true
    if(flag) {
      chooseKnowledge.value = item
    }
    isBaseKnowledge.value = flag ? false : true
  }
  defineExpose({ open }) // 提供 open 方法，用于打开弹窗

  const chooseKnowledge = ref(null)
  const KnowledgeHomeRef = ref()
  const handleClickKnowledge = (item) => {
    chooseKnowledge.value = item
		isBaseKnowledge.value = false
	}

  const handleCommand = (command) => {
		switch (command[0]) {
			case 'edit':
				KnowledgeUpdateRef.value.open('edit', command[1])
				break;
			case 'delete':
				handleDelete(command[1])
				break;
			default:
				break;
		}
	}

  const handlePathBack = () => {
		isBaseKnowledge.value = true
	}

	const handleBeforeClose = (done) => {
		emit('handleCloseDialog')
		done()
	}

  const handleDelete = async(item) => {
		try {
			loading.value = true
			await message.delConfirm()
			await deleteKlDataset(item.id)
			loading.value = false
			message.success('删除成功')
			// 刷新列表
			await handleSearch()
		} catch {}
	}

  const getData = async () => {
    const res = await getKlDatasetPermission({});
    return res;
  };
  
  /** 提交表单 */
  
  </script>
  
  <style lang="scss" scoped>
    .breadcrumb-wrapper {
      display: flex;
      height: 32px;
      align-items: center;
      padding: 0 20px 0 0;
      .back-button {
        padding: 0;
        font-size: 14px;
      }
    }
    .file-item {
      line-height: 32px;
      padding: 8px;
      border-bottom: 1px solid #ccc;
      cursor: pointer;
    }
    .kl-content {
			padding-top: 10px;
      padding: 10px 10px 0;
			.knowledge-list {
				height: 251px;
				display: flex;
				margin-bottom: 10px;
				flex-direction: column;
				justify-content: space-between;
				padding: 20px;
				border: 1px solid #ebeef5;
				border-radius: 12px;
				background-color: #fff;
				box-shadow: 0 1px 3px 0 rgb(0 0 0 / 5%);
				cursor: pointer;
				.list-header {
					display: flex;
					justify-content: space-between;
				}
				.title {
					color: #000000e0;
					margin: 10px 0;
					font-size: 24px;
					line-height: 32px;
					font-weight: 600;
					word-break: break-all
				}
				.footer {
					p {
						display: flex;
						align-items: center;
						font-weight: 500;
						font-size: 12px;
						line-height: 22px;
					}
				}
			}
		}
  </style>
  