<template>
  <Dialog 
    v-model="dialogVisible" 
    :scroll="true"
    :title="dialogTitle">
    <el-form
      ref="formRef"
      class="form-container"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >

      <el-form-item label="知识库名称" prop="name">
        <el-input v-model="formData.name" maxlength="40" placeholder="请输入知识库名称" />
      </el-form-item>
      <el-form-item label="显示排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" clearable controls-position="right" />
      </el-form-item>
      <el-form-item  label="是否首页显示" prop="isHome">
        <el-radio-group v-model="formData.isHome">
          <el-radio key="true" :value="true" border>显示</el-radio>
          <el-radio key="false" :value="false" border>隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="isSuperAdmin">
        <el-form-item  label="是否分享" prop="isShard">
          <el-radio-group v-model="formData.isShard">
            <el-radio key="true" :value="true" border>是</el-radio>
            <el-radio key="false" :value="false" border>否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item  label="是否点赞" prop="isLikes">
          <el-radio-group v-model="formData.isLikes">
            <el-radio key="true" :value="true" border>是</el-radio>
            <el-radio key="false" :value="false" border>否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item  label="是否推荐" prop="isPushes">
          <el-radio-group v-model="formData.isPushes">
            <el-radio key="true" :value="true" border>是</el-radio>
            <el-radio key="false" :value="false" border>否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item  label="是否收藏" prop="isFavorites">
          <el-radio-group v-model="formData.isFavorites">
            <el-radio key="true" :value="true" border>是</el-radio>
            <el-radio key="false" :value="false" border>否</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <!-- <el-form-item  label="是否下载" prop="isDownload">
        <el-radio-group v-model="formData.isDownload">
          <el-radio key="true" :value="true" border>是</el-radio>
          <el-radio key="false" :value="false" border>否</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="标签" prop="label">
        <TagInput 
          v-model="formData.label" 
          :default-tags="defaultTags"
          placeholder="请输入标签"
        />
      </el-form-item>
      <el-form-item label="描述" prop="synopsis">
        <el-input 
          v-model="formData.synopsis" 
          type="textarea" 
          :rows="3"
          :maxlength="200"
          :show-word-limit="true"
          placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :loading="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import TagInput from '@/components/TagInput/index.vue'
import { CommonStatusEnum } from '@/utils/constants'
import { useUserStore } from '@/store/modules/user'
import { defaultProps, handleTree } from '@/utils/tree'
import * as homeApi from '@/api/home/<USER>'
import { createKlDataset, updateKlDataset, getKlDatasetById } from '@/api/knowledge/docmangement'
import { update } from 'lodash'

defineOptions({ name: 'KnowledgeUpdate' })


const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userStore = useUserStore()

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const userInfo = userStore.getUser

const defaultTags = ref([])
const fileInput = ref(null)
const uploading = ref(false)
const formData = ref({
  name: '',
  sort: 0,
  isHome: true,
  isShard: true,
  isLikes: true,
  isFavorites: true,
  isPushes: true,
  isDownload: true,
  synopsis: '',
})
const submitDisabled = ref(true)
const formRules = reactive({
  name: [{ required: true, message: '知识库名称不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  isHome: [{ required: true, message: '是否首页显示不能为空', trigger: 'blur' }],
  isShard: [{ required: true, message: '是否分享不能为空', trigger: 'blur' }],
  isLikes: [{ required: true, message: '是否点赞不能为空', trigger: 'blur' }],
  isPushes: [{ required: true, message: '是否推荐不能为空', trigger: 'blur' }],
  isFavorites: [{ required: true, message: '是否收藏不能为空', trigger: 'blur' }],
  isDownload: [{ required: true, message: '是否下载不能为空', trigger: 'blur' }],
  label: [{ required: true, message: '标签不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const deptList = ref([]) // 树形结构
const dialogEdit = ref(false)
const isSuperAdmin = ref(false)

const getLabelList = async () => {
  const res = await homeApi.getLabelPage({
    pageNum: 1,
    pageSize: 5
  })
  defaultTags.value = res.records.map((item) => item.name)
}

/** 打开弹窗 */
const open = async (type, row = {}, isPermission) => {
  console.log('row', row);
  
  resetForm()
  dialogVisible.value = true
  formLoading.value = false
  dialogTitle.value = row.id ? '编辑' : '新增'
  getLabelList()
  isSuperAdmin.value = isPermission
  dialogEdit.value = row.id ? true : false
  if (row.id) {
    formData.value = {
      ...row,
      label: row.label ? row.label : [],
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  formLoading.value = true
  try {
    const resquest = dialogEdit.value ? updateKlDataset : createKlDataset
    const params = {
      ...formData.value,
      "isSuperAdmin": isSuperAdmin.value,
    }
    delete params.creator;
    const res = await resquest(params)
    console.log(res);
    formLoading.value = false
    message.success(dialogEdit.value ? '修改成功' : '新增成功')
    emit('success')
    dialogVisible.value = false
  } catch (error) {
    formLoading.value = false
    console.log(dialogEdit.value ? '修改失败' : '新增失败')
    console.log(error)
  }
}


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    name: '',
    sort: 0,
    isHome: true,
    isShard: true,
    isLikes: true,
		isPushes: true,
    isFavorites: true,
    isDownload: true,
    synopsis: '',
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
  .form-container {
    padding: 0 20px;
  }
  .file-upload-wrapper {
  width: 100%;
}

.file-input {
  :deep(.el-input-group__append) {
    padding: 0 20px;
    border: none;
  }
  
  :deep(.el-input__inner) {
    cursor: default;
    background-color: #fff;
  }
}

.el-upload__tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}
</style>
