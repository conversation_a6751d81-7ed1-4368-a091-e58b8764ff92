html, body {
    width: 100%;
    height: 100%;
    margin: 0px !important;
}

.div_content {
    position: relative;
    margin: 15px 0px 15px 15px;
    padding-right: 15px;
    height: calc(100% - 40px);
    overflow: hidden;
}

.ps__rail-y {
    margin-top: 0px !important;
}

.div_main {
    display: flex;
    flex-flow: column;
}

.mrtop {
    margin-top: 8px;
}

.prompt {
    height: 270px !important;
}

.prompt::selection {
    background: #D8DADC; /* WebKit/Blink Browsers */
    color: #444444;
}

input::selection {
    background: #D8DADC; /* WebKit/Blink Browsers */
    color: #444444;
}

.div_form {
    display: flex;
    flex-flow: column;
}

.btn_submit {
    flex: 0.8;
    margin-right: 5px;
    height: 30px !important;
}

.btn_clear {
    flex: 0.2;
    margin-left: 5px;
    height: 30px !important;
}

.div_req_btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 18px;
    /* margin-bottom: 15px; */
}

.div_parametr {
    display: flex;
    flex-flow: column;
    margin: 10px 0px;
}

.div_param_val {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
}

.lbl_param_val {
    margin-left: 10px;
    min-width: 30px;
}

.inp_value {
    max-width: 35px;
}


#footerCont {
    margin: 5px 0px;
}

.div_settings {
    flex: 1;
}

.div_tokens {
    display: flex;
    justify-content: end;
    margin-top: 7px;
}

.div_row {
    margin: 5px;
}

.lb_err {
    color: red;
    padding: 2px 10px;
}

.div_show_settings {
    display: flex;
    width: fit-content;
    cursor: pointer;
}

.div_arrow {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
}

.div_title {
    position: absolute;
    z-index: 1;
    height: fit-content;
    padding: 8px 5px;
    right: 15px;
    left: 0px;
    overflow-wrap: anywhere;
}

.arrow {
    display: block;
    width: 4px !important;
    height: 4px !important;
    margin: -1px 1px !important;
    border: solid 1px rgba(0, 0, 0, 0.8);
    border-bottom: none !important;
    border-right: none !important;
    background-image: none;
    box-sizing: border-box;
    transition: transform 0.2s ease;
}

.arrow_down {
    transform: rotate(-135deg) translate(1px,1px);
}

.arrow_up {
    transform: rotate(45deg);
}

.div_modal {
    display: flex;
    flex-flow: column;
    position: absolute;
    top: 230px;
    /* top: 292px; */
    right: 15px;
    left: 0px;
    height: fit-content;
    z-index: 1;
}

.loader {
    margin: 0 !important;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100% !important;
    background-color: rgba(128, 128, 128, 0.5);
}

.hidden {
    display: none !important;
}

.error_border {
    border: 1px solid red !important;
}

input[type="range"] {
    -webkit-appearance: none;
    margin-right: 15px;
    height: 4px;
    background-color: #ccc;
    border-radius: 5px;
    background-image: linear-gradient(#444, #444);
    background-repeat: no-repeat;
}


input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    background: #444;
    cursor: pointer;
    transition: background .3s ease-in-out;
}

input[type="range"]::-moz-range-thumb {
    -webkit-appearance: none;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    background: #444;
    border: none;
    cursor: pointer;
    transition: background .3s ease-in-out;
}

input[type="range"]::-ms-thumb {
    -webkit-appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #444;
    cursor: pointer;
    transition: background .3s ease-in-out;
}

input[type=range]::-webkit-slider-runnable-track  {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
}

input[type=range]::-moz-range-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
}

input[type="range"]::-ms-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
}



.div_error {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    top: 0;
    left: 0;
}
.response {
    margin: 10px 0 0 0;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 90%;
    min-height: 100px;
    height: auto;
    overflow: auto;
}
