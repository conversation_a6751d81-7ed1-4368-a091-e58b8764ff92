<template>
    <el-dialog 
      v-model="dialogVisible" 
      fullscreen
      :destroy-on-close="true"
      :close-on-click-modal="false"
      title="智能对话">
			<div class="chat-list-dialog">
        <AiChat v-if="dialogVisible" ref="AiChatRef" />
      </div>
    </el-dialog>
  </template>
  <script setup>

	import AiChat from '@/views/knowledge/dataTraining/index.vue'
  
  defineOptions({ name: 'ChatListDialog' })
  
  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  
  const dialogVisible = ref(false) // 弹窗的是否展示

	const AiChatRef = ref(null)
  /** 打开弹窗 */
  const open = async (chatItem) => {
		dialogVisible.value = true;
		// setTimeout(() => {
		// 	AiChatRef.value && AiChatRef.value.handleConversationClick(chatItem)
		// }, 100)
  }

  defineExpose({ open }) // 提供 open 方法，用于打开弹窗
  
  </script>
  
  <style lang="scss" scoped>
  .chat-list-dialog {
    position: relative;
    height: calc(100vh - 60px);
    background-color: var(--el-color-white);
  }
  </style>
  