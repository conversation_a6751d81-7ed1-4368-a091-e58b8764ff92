<template>
  <el-dialog 
    v-model="dialogVisible" 
    :scroll="true" 
    :fullscreen="true" 
    :destroy-on-close="true"
    :before-close="handleBeforeClose" 
    :title="dialogTitle">
    <div class="content">
      <DocumentEditor 
        id="docEditor" 
        ref="documentEditorRef" 
        :events_onDocumentReady="onDocumentReady"
        :documentServerUrl="`${apikeys.onlyoffice_url}`" 
        :config="config" />
    </div>

    <el-dialog 
      v-model="innerVisible" 
      width="500" 
      title="提示" 
      :show-close="false" 
      :close-on-click-modal="false"
      :close-on-press-escape="false" 
      append-to-body>
      <span>文档保存中，将在 {{ countdown }} 秒后自动关闭...</span>
    </el-dialog>
    <!-- <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </template> -->
  </el-dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { DocumentEditor, } from '@onlyoffice/document-editor-vue'
import { useUserStore } from '@/store/modules/user'
import * as FolderAPI from '@/api/knowledge/docmangement'
import * as UserAPI from '@/api/system/user'
import { createFileEventLog } from '@/api/home/<USER>'

import { v4 as uuidv4 } from 'uuid'
import { before } from 'lodash'

defineOptions({ name: 'DocView' })
const userStore = useUserStore()
const apikeys = userStore.getAppConfig
const docObj = {
  create: '新建文档',
  update: '编辑文档',
  view: '查看文档'
}
const loading = ref(false)
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const emit = defineEmits(['success'])

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const dialogType = ref('')

const url = import.meta.env.VITE_DOCUMENT_URL

const getUserInfo = async () => {
  const response = await UserAPI.getUserProfileById(userStore.getUser.id)
  return response
}

const config = ref({
  document: {
    fileType: 'docx',
    key: uuidv4(),
    title: '技术规范书',
    url: `${apikeys.file_server_url}/init.docx`,
    permissions: {
      comment: true,
      download: true,
      modifyContentControl: true,
      modifyFilter: true,
      print: false,
      edit: true,
      fillForms: true,
      review: true
    }
  },
  documentType: "word",
  editorConfig: {
    lang: "zh-CN",
    // callbackUrl: import.meta.env.VITE_DOCUMENT_URL + "/admin-api/v1/kownledge/klCloud/onlyOfficeSave",
    callbackUrl: `${apikeys.file_callback_url}`,
    customization: {
      commentAuthorOnly: false,
      comments: true,
      compactHeader: false,
      compactToolbar: true,
      feedback: false,
      plugins: true
    },
    user: {
      id: '',
      name: ''
    },
  }
})

// const saveFunction = async(path) => {

//   const params = {}
//   const response = await FolderAPI.createFolder(params)
// }

const docKey = ref('')
const docTile = ref('')
const docUrl = ref('')
const fileLogTimer = ref(null)
/** 打开弹窗 */
const open = async (type, path, row, commonType, serviceType) => {
  // 从文件名获取文件类型
  let eventType = ''
  const fileType = row.fileName.split('.').pop()?.toLowerCase() || 'docx'
  const fileName = row.fileName ? row.fileName : `${docKey.value}.${fileType}`
  const filePath = row.filePath ? row.filePath : path?.id ? path.filePath + '/' + fileName : null
  docKey.value = uuidv4() + '-klCloud'
  if (type === 'create') {
    docTile.value = fileName
    eventType = '1'
    docUrl.value = `${apikeys.file_server_url}/init.${fileType}`
    const initReponse = await FolderAPI.uploadInit({
      url: docUrl.value,
      path: path?.id ? path.filePath : null,
      name: fileName,
    })
    docUrl.value = initReponse.fileUrl
    const params = {
      fileKey: docKey.value,
      parentId: path.id ? path.id : null,
      filePath,
      level: path.id ? parseInt(path.level) + 1 : '1',
      fileName,
      fileUrl: docUrl.value,
      datasetId: path.id ? path.datasetId : null,
      knowledgeOwner: row.knowledgeOwner ? row.knowledgeOwner : null,
      projectName: row.projectName ? row.projectName : null,
      department: row.department ? row.department : null,
      year: row.year ? row.year : null,
      // saveStatus: '0',
      // isCommon: commonType ? '0' : '1',
      filePrefix: fileType
    }
    const response = await FolderAPI.createFolder(params)
  } else if (type === 'update') {
    eventType = '3'
    docTile.value = row?.fileName
    docUrl.value = row?.fileUrl ? row?.fileUrl : `${apikeys.file_server_url}/init.${fileType}`
    const response = await FolderAPI.updateFolder({
      id: row.id,
      fileKey: docKey.value,
      // saveStatus: '0',
    })
  } else {
    eventType = '4'
    docTile.value = row?.fileName
    docUrl.value = row?.fileUrl ? row?.fileUrl : `${apikeys.file_server_url}/init.${fileType}`
  }
  const { id: userId, username: userName } = await getUserInfo()
  const fileLogParams = {
    fileName,
    fileUrl: docUrl.value,
    filePrefix: fileType,
    eventType,
    klServiceId: row.id || '',
    serviceType: serviceType ? serviceType : '知识库',
  }
  fileLogTimer.value = setTimeout(async() => {
    clearTimeout(fileLogTimer.value)
    const createFileEventLogResponse = await createFileEventLog(fileLogParams)
  }, 1000 * 10)
  
  config.value = {
    document: {
      fileType: fileType, // 使用从文件名获取的类型
      key: docKey.value,
      title: docTile.value,
      url: docUrl.value,
      permissions: {
        comment: true,
        download: true,
        modifyContentControl: true,
        modifyFilter: type === 'view' ? false : true,
        print: false,
        edit: type === 'view' ? false : true,
        fillForms: type === 'view' ? false : true,
        review: type === 'view' ? false : true
      }
    },
    // documentType: "word",
    editorConfig: {
      lang: "zh-CN",
      // callbackUrl: import.meta.env.VITE_DOCUMENT_URL + "/admin-api/v1/kownledge/common/onlyOfficeSave",
      callbackUrl: `${apikeys.file_callback_url}`,
      customization: {
        // hideHeader: true,   
        toolbar: {
          hide: ["save", "print", "download"] // 隐藏指定按钮（按需调整）
        },
        commentAuthorOnly: false,
        comments: type === 'view' ? false : true,
        compactHeader: false,
        compactToolbar: type === 'view' ? false : true,
        feedback: false,
        plugins: type === 'view' ? false : true,
        autosave: type === 'view' ? false : true,
        forcesave: type === 'view' ? false : true,
      },
      user: {
        id: userId,
        name: userName
      },
    }
  }
  console.log(apikeys.file_callback_url);
  debugger
  dialogVisible.value = true
  dialogTitle.value = type ? docObj[type] : '查看文档'
  dialogType.value = type
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const documentEditorRef = ref()

const submitForm = async () => {
  if (documentEditorRef.value) {

    const editor = documentEditorRef.value.getInstance();
    if (editor) {
      try {
        await editor.save();
        dialogVisible.value = false
        emit('success')
        message.success('文档保存成功');
      } catch (error) {
        console.error('保存文档失败', error);
        message.error('文档保存失败');
      }
    }
  }
}

const countdown = ref(0)
let timer = null
const innerVisible = ref(false)

// 处理弹窗关闭前的回调
const handleBeforeClose = (done) => {
  // if (dialogType.value === 'view') {
  //   done()
  // } else {
  //   // startCountdown()
  //   done()
  // }
  if(fileLogTimer.value) {
    clearTimeout(fileLogTimer.value)
  }
  done()
  // 不调用 done() 保持弹窗打开
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 10
  innerVisible.value = true
  timer = setInterval(() => {
    countdown.value -= 1
    if (countdown.value <= 0) {
      innerVisible.value = false
      closeDialog()
    }
  }, 1000)
}

// 取消倒计时
const cancelCountdown = () => {
  clearInterval(timer)
  countdown.value = 0
}

// 关闭弹窗并触发成功事件
const closeDialog = () => {
  clearInterval(timer)
  dialogVisible.value = false
  emit('success')
}

// 组件卸载前清理定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})

const onDocumentReady = () => {
  console.log('Document ready');
}


</script>

<style lang="scss" scoped>
.content {
  padding: 10px;
  height: calc(100vh - 72px);
}
</style>
