/* Scoped styles for chat footer reconstructed from Vue footer */
.chat-footer.footer-container {
    display: flex;
    flex-direction: column;
}
.chat-footer .selected-files {
    padding: 10px 7px 0 7px;
}
.chat-footer .file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}
.chat-footer .file-title {
    font-size: 14px;
    color: #606266;
}
.chat-footer .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}
.chat-footer .file-item {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #f5f7fa;
    border: 1px solid #e3e3e3;
    border-radius: 6px;
    padding: 2px 6px;
}
.chat-footer .file-tag {
    font-size: 12px;
    color: #606266;
}
.chat-footer .file-tag-close {
    border: none;
    background: transparent;
    cursor: pointer;
    color: #909399;
    font-size: 14px;
    line-height: 1;
}
.chat-footer .file-tag-close:hover {
    color: #606266;
}

.chat-footer .prompt-from {
    display: flex;
    flex-direction: column;
    border: 1px solid #e3e3e3;
    border-radius: 10px;
    margin: 10px 7px 20px 7px;
    padding: 9px 10px;
}
.chat-footer .prompt-input {
    height: 80px;
    border: none;
    box-sizing: border-box;
    resize: none;
    padding: 0 2px;
    overflow: auto;
}
.chat-footer .prompt-input:focus {
    outline: none;
}
.chat-footer .prompt-btns {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 5px;
}
.chat-footer .right-section {
    display: flex;
    gap: 6px;
    align-items: center;
}

.chat-footer .btn-group .agent-btn {
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    background: #fff;
    cursor: pointer;
}
.chat-footer .btn-group .agent-btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
.chat-footer .btn-group .agent-btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
.chat-footer .btn-group .agent-btn.active {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
}

/* 发送按钮样式 */
.chat-footer .send-button {
    padding: 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-footer .send-button:hover {
    background: #f5f7fa;
}

.chat-footer .send-button.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* 停止按钮和重新生成按钮样式 */
.chat-footer .stop-button,
.chat-footer .regenerate-button {
    padding: 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #606266;
}

.chat-footer .stop-button:hover,
.chat-footer .regenerate-button:hover {
    background: #f5f7fa;
}
.chat-footer .clear-files-btn {
    border: none;
    background: transparent;
    color: #f56c6c;
    cursor: pointer;
}
.chat-footer .clear-files-btn:hover {
    text-decoration: underline;
}

/* 文件选择按钮状态 */
.chat-footer .file-btn {
    padding: 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    font-size: 20px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.chat-footer .file-btn:hover {
    background: #f5f7fa;
}
.chat-footer .file-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: transparent;
}

/* 按钮组禁用状态 */
.chat-footer .agent-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 文件选择对话框样式 */
.chat-footer .file-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-footer .file-dialog {
    background: #fff;
    border-radius: 8px;
    width: 700px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
}

.chat-footer .file-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e3e3e3;
}

.chat-footer .file-dialog-header h3 {
    margin: 0;
    font-size: 16px;
    color: #303133;
}

.chat-footer .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #909399;
}

.chat-footer .file-dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.chat-footer .file-tabs {
    display: flex;
    border-bottom: 1px solid #e3e3e3;
    margin-bottom: 20px;
}

.chat-footer .file-tabs button {
    border-radius: 0;
}

.chat-footer .tab-btn {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    color: #606266;
}

.chat-footer .tab-btn.active {
    color: #409eff;
    border-bottom-color: #409eff;
}

/* 知识库内容区域 */
.chat-footer .kl-content {
    padding: 10px 10px 0;
}

.chat-footer .knowledge-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.chat-footer .knowledge-item {
    height: 251px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s;
}

.chat-footer .knowledge-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.chat-footer .knowledge-header {
    display: flex;
    justify-content: space-between;
}

.chat-footer .knowledge-icon {
    font-size: 24px;
}

.chat-footer .knowledge-title {
    color: rgba(0, 0, 0, 0.88);
    margin: 10px 0;
    font-size: 24px;
    line-height: 32px;
    font-weight: 600;
    word-break: break-all;
}

.chat-footer .knowledge-footer p {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 12px;
    line-height: 22px;
    margin: 0;
    color: #909399;
}

/* 文件浏览区域 */
.chat-footer .file-browser-content {
    margin-top: 20px;
}

.chat-footer .breadcrumb-wrapper {
    display: flex;
    align-items: center;
    padding: 0 20px 0 0;
    padding-bottom: 8px;
    color: #909399;
    font-size: 14px;
    min-height: 32px;
}

.chat-footer .back-button {
    padding: 0;
    border: none;
    background: none;
    color: #409eff;
    cursor: pointer;
    font-size: 14px;
    margin-right: 8px;
    width: 80px;
    text-align: left;
    white-space: nowrap;
    flex-shrink: 0;
    line-height: 1.3;
    vertical-align: baseline;
}

.chat-footer .back-button:hover {
    text-decoration: underline;
}

.chat-footer .breadcrumb-divider {
    margin: 0 8px;
    color: #909399;
    line-height: 1.3;
    vertical-align: baseline;
}

.chat-footer .breadcrumb {
    color: #909399;
    font-size: 14px;
    display: flex;
    align-items: flex-start;
    flex: 1;
    flex-wrap: wrap;
    min-width: 0;
    line-height: 1.3;
}

.chat-footer .breadcrumb-item {
    color: #909399;
    cursor: pointer;
    transition: color 0.2s;
    white-space: nowrap;
    flex-shrink: 0;
    line-height: 1.3;
    display: inline-block;
    vertical-align: baseline;
}

.chat-footer .breadcrumb-item:hover {
    color: #409eff;
}

.chat-footer .breadcrumb-item:not(:last-child)::after {
    content: ' / ';
    margin: 0 4px;
    color: #909399;
    cursor: default;
}

.chat-footer .breadcrumb-separator {
    margin: 0 4px;
    color: #909399;
}

/* 知识库文件表格 - 不带表头 */
.chat-footer .kb-file-table-container {
    max-height: 300px;
    overflow-y: auto;
}

.chat-footer .kb-file-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.chat-footer .kb-file-table tbody tr {
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
}

.chat-footer .kb-file-table tbody tr:hover {
    background: #f5f7fa;
}

.chat-footer .kb-file-table td {
    padding: 12px 16px;
    vertical-align: middle;
    color: #606266;
    line-height: 1.5;
}

.chat-footer .kb-file-table .file-name-cell {
    width: 100%;
}

.chat-footer .kb-file-table .file-name-content {
    display: flex;
    align-items: center;
}

.chat-footer .kb-file-table .file-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #909399;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.chat-footer .kb-file-table .file-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 500px;
    color: #606266;
    font-size: 14px;
}

.chat-footer .kb-file-table .action-cell {
    width: 60px;
    text-align: center;
}

.chat-footer .kb-file-table .action-cell input[type='checkbox'] {
    width: 16px;
    height: 16px;
}

.chat-footer .upload-section {
    text-align: right;
    margin-bottom: 20px;
}

.chat-footer .upload-btn {
    padding: 8px 16px;
    background: #409eff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.chat-footer .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
}

.chat-footer .local-file-table-container {
    max-height: 300px;
    overflow-y: auto;
}

.chat-footer .local-file-table-container {
    position: relative;
    min-height: 200px;
}

.chat-footer .local-file-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
}

.chat-footer .local-file-table th {
    background-color: #fafafa;
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid #ebeef5;
    font-weight: 500;
    color: #909399;
    font-size: 14px;
}

.chat-footer .local-file-table td {
    padding: 12px 8px;
    text-align: center;
    border-bottom: 1px solid #ebeef5;
    color: #606266;
    font-size: 14px;
    vertical-align: middle;
}

.chat-footer .local-file-table tbody tr:last-child td {
    border-bottom: none;
}

.chat-footer .local-file-table tbody tr:hover {
    background-color: #f5f7fa;
}

.chat-footer .action-btn {
    padding: 4px 8px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #fff;
    cursor: pointer;
    font-size: 12px;
    margin-right: 4px;
}

.chat-footer .action-btn:hover {
    background: #f5f7fa;
}

.chat-footer .action-btn.parse {
    color: #409eff;
    border-color: #409eff;
}

.chat-footer .empty-state {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    border-radius: 4px;
    padding: 20px;
}

.chat-footer .file-dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 20px;
    border-top: 1px solid #e3e3e3;
    position: sticky;
    bottom: 0;
    background: #fff;
    z-index: 10;
}

.chat-footer .confirm-btn {
    padding: 8px 16px;
    background: #409eff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 8px;
}

.chat-footer .cancel-btn {
    padding: 8px 16px;
    background: #fff;
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
}

/* 文件按钮样式 */

/* 状态标签样式（模仿Element Plus的el-tag） */
.chat-footer .status-tag {
    display: inline-block;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid;
    white-space: nowrap;
    text-align: center;
}

.chat-footer .status-tag-info {
    color: #909399;
    background-color: #f4f4f5;
    border-color: #d3d4d6;
}

.chat-footer .status-tag-primary {
    color: #409eff;
    background-color: #ecf5ff;
    border-color: #b3d8ff;
}

.chat-footer .status-tag-success {
    color: #67c23a;
    background-color: #f0f9ff;
    border-color: #c2e7b0;
}

.chat-footer .status-tag-danger {
    color: #f56c6c;
    background-color: #fef0f0;
    border-color: #fbc4c4;
}

.chat-footer .status-tag-warning {
    color: #e6a23c;
    background-color: #fdf6ec;
    border-color: #f5dab1;
}

/* 解析按钮样式 */
.chat-footer .parse-btn {
    background: none;
    border: none;
    color: #409eff;
    cursor: pointer;
    font-size: 16px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.chat-footer .parse-btn:hover {
    background-color: #ecf5ff;
}

/* 文件复选框样式 */
.chat-footer .file-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* 重试按钮样式 */
.chat-footer .retry-btn {
    background: none;
    border: none;
    color: #f56c6c;
    cursor: pointer;
    font-size: 16px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.chat-footer .retry-btn:hover {
    background-color: #fef0f0;
}
