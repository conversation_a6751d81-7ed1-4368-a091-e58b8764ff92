<template>
  <el-row :gutter="20" class="flex-wrap">
    <el-col :span="12" v-for="item in docs" :key="item.id">
      <el-card class="doc-section" >
        <template #header>
          <span class="section-title">{{ item.title }}</span>
        </template>
        <el-table :data="item.list">
          <el-table-column prop="fileName" show-overflow-tooltip label="文档名称" />
          <el-table-column prop="filePath" show-overflow-tooltip label="文档路径" />
          <el-table-column prop="count" label="数量" width="80" />
        </el-table>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref } from 'vue'
import * as homeAPI from '@/api/home/<USER>'
const homeLikesList = ref([])
const homePushesList = ref([])
const homeFavoritesList = ref([])
const homeDownloadList = ref([])

const docs = computed(() => {
  return [
    {
      title: '点赞文档',
      list: homeLikesList.value
    },
    {
      title: '收藏文档',
      list: homeFavoritesList.value
    },
    {
      title: '推荐文档',
      list: homePushesList.value
    },
    {
      title: '下载文档',
      list: homeDownloadList.value
    }
  ]
})



// 点赞
const getHomeLikesCountList = () => {
  homeAPI.getHomeLikesCount().then((response) => {
    homeLikesList.value = response
  })
}
// 推荐
const getHomePushesCountList = () => {
  homeAPI.getHomePushesCount().then((response) => {
    homePushesList.value = response
  })
}
// 收藏
const getHomeFavoritesCountList = () => {
  homeAPI.getHomeFavoritesCount().then((response) => {
    homeFavoritesList.value = response
  })
}
// 下载
const getHomeDownloadCountList = () => {
  homeAPI.getHomeDownloadCount().then((response) => {
    homeDownloadList.value = response
  })
}

onMounted(() => {
  getHomeLikesCountList()
  getHomePushesCountList()
  getHomeFavoritesCountList()
  getHomeDownloadCountList()
})
</script>

<style lang="less" scoped></style>
