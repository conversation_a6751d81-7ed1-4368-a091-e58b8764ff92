import request from '@/config/axios'

// 
export const getArrangeAgentPage = (params) => {
  return request.get({ url: '/v1/kownledge/klArrangeAgent/page', params })
}
// 
export const addArrangeAgent = (data) => {
  return request.post({ url: '/v1/kownledge/klArrangeAgent', data })
}

// 
export const updateArrangeAgent = (data) => {
  return request.put({ url: '/v1/kownledge/klArrangeAgent', data })
}

// 
export const deleteArrangeAgent = (id) => {
  return request.delete({ url: `/v1/kownledge/klArrangeAgent/${id}` })
}

// 
export const getArrangeAgent = (id) => {
  return request.get({ url: `/v1/kownledge/klArrangeAgent/${id}` })
}
