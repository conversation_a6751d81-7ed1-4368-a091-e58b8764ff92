<template>
  <div class="doc-content">
    <!-- 头部按钮和搜索框 -->
    <div class="header" v-if="!isAdmin">
      <!-- 左侧按钮 -->
      <div>
        <input 
          type="file" 
          ref="fileInput" 
          @change="handleFileSelect" 
          style="display: none"
          accept=".doc,.docx,.pdf" />
        <!-- <el-button type="primary" @click="handleUploadFile" :loading="uploading">上传文件</el-button> -->
        <el-button type="primary" @click="handleNewFile()">新建文件</el-button>
        <el-button type="primary" @click="handleNewFolder('folder')">新建文件夹</el-button>
      </div>

      <!-- 右侧搜索框和按钮 -->
      <div>
        <el-input v-model="queryParams.fileName" placeholder="请输入文件名" style="width: 200px; margin-right: 10px;" />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </div>

    <!-- 路径导航 -->
    <div class="breadcrumb-wrapper">
      <!-- 返回上一级按钮 -->
      <el-button v-if="path.length > 1" @click="handleGoBack" link type="primary" class="back-button">
        返回上一级
      </el-button>
      <el-divider direction="vertical" />
      <!-- 路径导航 -->
      <el-breadcrumb separator="/">
        <el-breadcrumb-item v-for="(item, index) in path" :key="index" @click="handleBreadcrumbClick(index)">
          <span style="cursor: pointer;">{{ item.fileName }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 文件列表 -->
    <el-table :data="fileList" v-loading="tableLoading">
      <el-table-column label="名称" width="700">
        <template #default="{ row }">
          <div class="name-column">
            <!-- 名称文本 -->
            <!-- 重命名 文本框 确认 取消 -->
            <div class="name-wrapper">
              <div v-if="editId == row.id">
                <el-input v-model="editName" size="small" maxlength="40" style="width: 200px;" />
                <el-button 
                  type="primary" 
                  style="margin-left: 5px;" 
                  size="small"
                  @click="handleEditNameConfirm(row)">确认</el-button>
                <el-button link type="primary" size="small" @click="handleEditNameCancel(row)">取消</el-button>
              </div>
              <span v-else class="name-text" @click.stop="handleNameClick(row)">
                <div class="flex align-center">
                  <span>
                    <el-icon class="mr-[4px]">
                      <Document v-if="row.filePrefix" />
                      <FolderOpened v-else />
                    </el-icon>
                  </span>
                  <span>
                    <el-tooltip :content="row.fileName" placement="top">
                      <span class="ellipsis">
                        {{ row.fileName }}
                      </span>
                    </el-tooltip>
                  </span>
                </div>
              </span>
            </div>

            <!-- 操作按钮组 -->
            <div class="button-group" v-if="row.id != '-1'">
              <!-- 文件专属操作 -->
              <template v-if="row.filePrefix">
                <el-tooltip content="推送" placement="top" v-if="row.isCloud !== '1'">
                  <el-button link type="primary" @click.stop="handlePush(row)">
                    <!-- <el-icon><Delete /></el-icon> -->
                    <Icon icon="fa:paper-plane" />
                  </el-button>
                </el-tooltip>
                <el-tooltip v-if="row.shardStatus === '1'" content="分享" placement="top">
                  <el-button link type="primary" @click.stop="handleShare(row)">
                    <!-- <el-icon><Share /></el-icon> -->
                    <Icon icon="fa:share-square-o" />
                  </el-button>
                </el-tooltip>

                <el-tooltip v-if="row.downloadPermission === '1'" content="下载" placement="top">
                  <el-button link type="primary" @click.stop="handleDownload(row)">
                    <!-- <el-icon><Download /></el-icon> -->
                    <Icon icon="fa:cloud-download" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="重命名" v-if="!isAdmin" placement="top">
                  <el-button link type="primary" @click.stop="handleRename(row)">
                    <!-- <el-icon><Edit /></el-icon> -->
                    <Icon icon="fa:edit" />
                  </el-button>
                </el-tooltip>
                <!-- 查看文档 编辑文档 -->
                <el-tooltip content="查看文档" placement="top">
                  <el-button link type="primary" @click.stop="handleView(row)">
                    <!-- <el-icon><View /></el-icon> -->
                    <Icon icon="fa:eye" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="编辑文档" v-if="!isAdmin" placement="top">
                  <el-button link type="primary" @click.stop="handleEdit(row)">
                    <!-- <el-icon><Edit /></el-icon> -->
                    <!-- <i class="far fa-calendar-edit" /> -->
                    <Icon icon="fa-solid:file-signature" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="移动" v-if="!isAdmin" placement="top">
                  <el-button link type="primary" :disabled="row.status == '0'" @click.stop="handleMove(row)">
                    <!-- <el-icon><FolderOpened /></el-icon> -->
                    <Icon icon="fa:folder-open" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="删除" placement="top">
                  <el-button link type="primary" @click.stop="handleDelete(row)">
                    <!-- <el-icon><Delete /></el-icon> -->
                    <Icon icon="fa:trash-o" />
                  </el-button>
                </el-tooltip>
                <!-- 更多操作下拉菜单 -->
                <!-- <el-dropdown trigger="click" @command="handleCommand(row, $event)">
                  <span class="more-actions">
                    更多
                    <el-icon :size="12" style="margin-left: 5px;">
                      <ArrowDown />
                    </el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="move">
                        <el-icon><FolderOpened /></el-icon>移动
                      </el-dropdown-item>
                      <el-dropdown-item command="delete">
                        <el-icon><Delete /></el-icon>删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
</el-dropdown> -->
              </template>
              <template v-else>
                <el-tooltip content="重命名" v-if="!isAdmin" placement="top">
                  <el-button link type="primary" @click.stop="handleRename(row)">
                    <Icon icon="fa:edit" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="移动" v-if="!isAdmin" placement="top">
                  <el-button link type="primary" @click.stop="handleMove(row)">
                    <!-- <el-icon><FolderOpened /></el-icon> -->
                    <Icon icon="fa:folder-open" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="删除" placement="top">
                  <el-button link type="primary" @click.stop="handleDelete(row)">
                    <!-- <el-icon><Delete /></el-icon> -->
                    <Icon icon="fa:trash-o" />
                  </el-button>
                </el-tooltip>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传日期">
        <template #default="{ row }">
          <span>{{ row.id === '-1' ? '-' : row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <div v-if="row.filePrefix">
            <el-tag v-if="!row.message" :type="statusClass[row.status]">{{ analysisObj[row.status]
            }}</el-tag>
            <el-popover v-else placement="top" :width="400" >
              <template #reference>
                <el-tag :type="statusClass[row.status]">{{ analysisObj[row.status]}}</el-tag>
              </template>
              <p>生成结果：{{ row.message ? row.message : '-'}}</p>
            </el-popover>
          </div>
          <div v-else>
            <span>-</span>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <Pagination 
      :total="total" 
      v-model:page="queryParams.pageNum" 
      v-model:limit="queryParams.pageSize"
      @pagination="queryList" />
  </div>
  <NewTemUploadForm ref="uploadFormRef" :path="path" @success="handleSearch" />
  <DocView ref="docViewRef" @success="handleSearch" />
  <ParseSettingsForm ref="parseSettingsFormRef" @success="handleSearch" />
  <NewTemMoveDialog ref="moveDialogRef" @success="handleSearch" type="newTem" />
  <ShareDialog ref="shareDialogRef" @success="handleSearch" />
  <ShareList ref="shareListRef" />
  <ImgView ref="imgViewRef" />
  <EditDialog ref="editDialogRef" @success="handleSearch" />
  <CreatFile ref="creatFileRef" @success="createFileSuccess" />
  <FileWayDialog ref="FileWayDialogRef" />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
// 新增图标引入
import {
  Star,
  StarFilled,
  Share,
  Download,
  Edit,
  FolderOpened,
  Delete,
  Promotion,
  MagicStick,
  Setting,
  View,
  EditPen,
  MoreFilled,
  Document,
  ArrowDown
} from '@element-plus/icons-vue'
import * as TemplateAPI from '@/api/knowledge/createTem'
import * as FolderAPI from '@/api/knowledge/docmangement'
import { CommonStatusEnum } from '@/utils/constants'
import { downloadFileFunction } from '@/utils/downloadFile'
import NewTemUploadForm from './newTemUploadForm.vue';
import ParseSettingsForm from './ParseSettingsForm.vue';
import DocView from './DocView.vue'
import NewTemMoveDialog from './newTemMoveDialog.vue';
import ShareDialog from './ShareDialog.vue';
import ShareList from './ShareList.vue';
import ImgView from './ImgView.vue';
import EditDialog from './components/EditDialog.vue';
import CreatFile from './components/CreatFile.vue';
import FileWayDialog from './components/FileWayDialog.vue';
import { uploadInit } from '@/api/knowledge/docmangement'
import { v4 as uuidv4 } from 'uuid'
 import { useUserStore } from '@/store/modules/user'

const message = useMessage() // 消息弹窗
const userStore = useUserStore()
const userInfo  = userStore.user
const apikeys = userStore.getAppConfig
// 当前路径
const path = ref([{ id: '', fileName: '根目录', level: '1', filePath: '' }]);

// 0生成中，1生成成功，2生成失败，3待生成
const analysisObj = ref({
  '0': '生成中',
  '1': '生成完成',
  '2': '生成失败',
  '3': '待生成'
});

const statusClass = ref({
  '0': 'primary',
  '1': 'success',
  '2': 'warning',
  '3': 'info',
})

// 当前文件夹下的文件列表
const fileList = ref([]);
const props = defineProps({
  isAdmin: {
    type: Boolean,
    require: false,
    default: true
  },
})

const { isAdmin } = toRefs(props)
// 编辑id
const editId = ref('-1');
const editName = ref('');
const tableLoading = ref(false);

// 搜索查询
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileName: '',
  keyword: ''
});
const total = ref(0);
const parseSettingsFormRef = ref(null);
const docViewRef = ref(null);
const moveDialogRef = ref(null);
const shareDialogRef = ref(null);
const editDialogRef = ref(null);

// 加载文件夹内容
const loadFolderContent = async (row) => {
  // fileList.value = fakeData[folderId] || [];
  const { id, level } = row
  const response = await TemplateAPI.getCloudPage({
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    fileName: queryParams.value.fileName,
    parentId: id,
    level: id ? parseInt(level) + 1 : level,
    creator: isAdmin.value ? '' : userInfo.id,
  });
  tableLoading.value = false;
  total.value = response.total;
  fileList.value = response.records;
};

const queryList = () => {
  loadFolderContent(path.value[path.value.length - 1]);
}

const handleSearch = () => {
  queryParams.value.pageNum = 1;
  loadFolderContent(path.value[path.value.length - 1]);
}

const handleReset = () => {
  queryParams.value.fileName = ''
  queryParams.value.pageNum = 1;
  loadFolderContent(path.value[path.value.length - 1]);
}

// 新建文件夹
const handleNewFolder = (type) => {
  console.log('新建文件夹');
  clearInterval(timer.value)
  fileList.value.unshift({ id: '-1', fileName: '', type: type, });
};


// 点击名称文本进入下一级
const handleNameClick = (row) => {
  if (!row.filePrefix) {
    path.value.push(row);
    loadFolderContent(row);
  }
};

// 点击路径导航返回上一级
const handleBreadcrumbClick = (index) => {
  clearInterval(timer.value)
  path.value = path.value.slice(0, index + 1);
  loadFolderContent(path.value[index]);
  setQueryInterval()
};

// 重命名
const handleRename = (row) => {
  const fileName = row.fileName
  const lastDotIndex = fileName.lastIndexOf('.');
  editName.value = lastDotIndex === -1 ? row.fileName : fileName.substring(0, lastDotIndex);
  if(!row.filePrefix) {
    row.type = 'folder'
    editName.value = fileName
  }
  editId.value = row.id;
  clearInterval(timer.value)
};

// 删除
const handleDelete = async (row) => {
  console.log('删除:', row.name);
  await message.delConfirm()
  const response = await TemplateAPI.deleteFolder(row.id)
  if (response.code === 200) {
    message.success('删除成功')
    handleSearch()
  }
};

// 喜欢
const handleLike = async (row) => {
  console.log('喜欢:', row.name);
  const response = await TemplateAPI.saveFolderLikes({ klTemplateId: row.id })
  if (response.code === 200) {
    message.success('喜欢成功')
    handleSearch()
  }
};

// 取消喜欢
const handleCancelLike = async (row) => {
  console.log('取消喜欢:', row.name);
  const response = await TemplateAPI.deleteFolderLikes(row.id)
  if (response) {
    message.success('取消喜欢成功')
    handleSearch()
  }
};

// 收藏
const handleCollect = async (row) => {
  console.log('收藏:', row.name);
  const response = await TemplateAPI.saveFolderFavorites({ klTemplateId: row.id })
  if (response.code === 200) {
    message.success('收藏成功')
    handleSearch()
  }
};

// 取消收藏
const handleCancelCollect = async (row) => {
  console.log('取消收藏:', row.name);
  const response = await TemplateAPI.deleteFolderFavorites(row.id)
  if (response) {
    message.success('取消收藏成功')
    handleSearch()
  }
};

const FileWayDialogRef = ref(null)
const handlePush = async (row) => {
  FileWayDialogRef.value && FileWayDialogRef.value?.open(row);
  // console.log('推送:', row.name);
  // const res = await TemplateAPI.pushKlCloud(row)
  // console.log('res:', res);
  // if (res) {
  //   message.success('推送成功')
  //   handleSearch()
  // }
}

// 推荐
const handleRecommend = async (row) => {
  console.log('推荐:', row.name);
  const response = await TemplateAPI.saveFolderPushes({ klTemplateId: row.id })
  if (response.code === 200) {
    message.success('推荐成功')
    handleSearch()
  }
};

// 取消推荐
const handleCancelRecommend = async (row) => {
  console.log('取消推荐:', row.name);
  const response = await TemplateAPI.deleteFolderPushes(row.id)
  if (response) {
    message.success('取消推荐成功')
    handleSearch()
  }
};
const shareListRef = ref()
// 共享文件
const handleShareFile = () => {
  shareListRef.value.open('my')
}

const handleMyShare = () => {
  shareListRef.value.open('other')
}

const handleEditNameConfirm = async (row) => {
  if (!/^[\u4e00-\u9fa5a-zA-Z0-9-]+$/.test(editName.value)) {
    message.warning('名称只能包含中文、英文、数字、-')
    return
  }
  const otherParams = row.type === 'file' ? {
    filePrefix: 'file'
  } : {};
  const fileName = row.fileName
  const lastDotIndex = fileName.lastIndexOf('.');
  let editType = lastDotIndex === -1 ? '' : '.' + fileName.substring(lastDotIndex + 1)
  if (row.type === 'folder') {
    editType = '';
  }
  const params = {
    id: row.id === '-1' ? null : editId.value,
    fileName: editName.value + editType,
    parentId: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].id : null,
    filePath: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].filePath + '/' + editName.value : null,
    level: path.value[path.value.length - 1].id ? parseInt(path.value[path.value.length - 1].level) + 1 : '1',
    ...otherParams,
  }
  if(row.filePath && !path.value[path.value.length - 1].id) {
    params.filePath = row.filePath.split('/')[0] + '/' + editName.value + editType
  }
  if (editName.value === '') {
    message.error('文件夹名称不能为空');
    return;
  }
  try {
    tableLoading.value = true
    const response = row.id === '-1' ? await TemplateAPI.createFolder(params) : await TemplateAPI.updateFolder(params);
    if (response.code === 200) {
      editName.value = '';
      editId.value = '-1';
      message.success(row.id === '-1' ? '新建文件夹成功' : '重命名成功');
      handleSearch()
      setQueryInterval()
    } else {
      message.error(response.msg);
    }

  } catch (error) {
    tableLoading.value = false
    console.log('handleEditNameConfirm error:', error);

  }
}

const handleEditNameCancel = (row) => {
  if (row.id === '-1') {
    // 删除filelist 第一项
    fileList.value.shift();
  } else {
    editName.value = '';
    editId.value = '-1';
  }
  setQueryInterval()
};

// 返回上一级
const handleGoBack = () => {
  if (path.value.length > 1) {
    path.value.pop(); // 移除最后一项
    const currentFolderId = path.value[path.value.length - 1].id; // 获取上一级的文件夹 ID
    loadFolderContent(path.value[path.value.length - 1]); // 加载上一级文件夹内容
  }
};

// 处理操作按钮点击
const handleCommand = (row, command) => {
  console.log('操作:', command, row);
  switch (command) {
    case 'move':
      console.log('移动:', row.name);
      handleMove(row)
      break;
    case 'rename':
      console.log('重命名:', row.name);
      break;
    case 'delete':
      console.log('删除:', row.name);
      handleDelete(row)
      break;
    case 'favorite':
      console.log('收藏:', row.name);
      break;
    case 'share':
      console.log('分享:', row.name);
      break;
    case 'download':
      handleDownload(row)
      console.log('下载:', row.name);
      break;
    case 'view':
      console.log('查看:', row.name);
      break;
    case 'edit':
      console.log('编辑:', row.name);
      break;
    case 'recommend':
      console.log('推荐:', row.name);
      handleRecommend(row);
      break;
    case 'unRecommend':
      console.log('取消推荐:', row.name);
      handleCancelRecommend(row);
      break;
    case 'parse':
      console.log('解析:', row.name);
      break;
    case 'parseSettings':
      parseSettingsFormRef.value.open(row);
      break;
    default:
      break;
  }
};

// 处理分享状态变化
const handleShareChange = async (row) => {
  console.log('分享状态变化:', row.name, row.shardStatus);
  try {
    // 修改状态的二次确认
    const text = row.shardStatus === '1' ? '分享' : '取消分享'
    await message.confirm('确认要' + text + '吗?')
    // 发起修改状态
    const response = row.shardStatus === '1' ? await TemplateAPI.createCloudShare(row.id) : await TemplateAPI.deleteCloudShare(row.id)
    // 刷新列表
    handleSearch()
  } catch {
    // 取消后，进行恢复按钮
    row.shardStatus =
      row.shardStatus === '0' ? '1' : '0'
  }
  // 这里可以添加分享状态变化的逻辑
};

const handleShare = (row) => {
  console.log('分享:', row.name);
  // 这里可以添加分享逻辑
  shareDialogRef.value.open(row);
}

// 文件上传 TemplateAPI.uploadFolderFile
const uploadFile = async (file, oldFileName) => {
  return new Promise((resolve, reject) => {
    FolderAPI.uploadFolderFile({
      file,
      fileName: oldFileName,
      path: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].filePath + '/' + oldFileName : null,
      newPath: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].filePath + '/' + file.name : null,
    }).then((res) => {
      if (res.code === 200) {
        resolve(res)
      } else {
        reject(res)
      }
    })
  })
}

const uploading = ref(false)
// 处理文件选择
const handleFileSelect = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  // 判断文件类型
  const allowedMimeTypes = [
    'application/msword', // .doc
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/pdf' // .pdf
  ];
  
  if (!allowedMimeTypes.includes(file.type)) {
    message.error('仅支持 PDF/Word 格式!')
    event.target.value = ''; // 清空文件选择
    return
  }
  // 文件大小限制
  if (file.size > 100 * 1024 * 1024) {
    message.error('文件大小不能超过 100MB!')
    event.target.value = ''; // 清空文件选择
    return
  }
  if (file.size < 1) {
    message.error('文件内容不能为空!')
    event.target.value = ''; // 清空文件选择
    return
  }

  // 执行上传
  try {
    uploading.value = true
    const oldFileName = file.name
    const newFile = new File(
      [file], // 文件内容（Blob/ArrayBuffer）
      uuidv4() + file.name, // 新文件名
      {
        type: file.type, // 保持原 MIME 类型
        lastModified: file.lastModified, // 保持原修改时间
      }
    );
    const fileResponse = await uploadFile(newFile, oldFileName)
    const { filePath, fileName, fileUrl, versions, filePrefix } = fileResponse.data
    const params = {
      fileName,
      filePath,
      fileUrl,
      filePrefix,
      parentId: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].id : null,
      level: path.value[path.value.length - 1].id ? parseInt(path.value[path.value.length - 1].level) + 1 : '1',
    }
    const response = await TemplateAPI.createFolder(params)
    handleSearch()
    ElMessage.success('文件上传成功')
  } catch (error) {
    ElMessage.error('文件上传失败')
    console.error('上传失败:', error)
  } finally {
    uploading.value = false
    fileInput.value.value = '' // 清空input
  }
}

const uploadFormRef = ref();

const fileInput = ref(null)
const handleUploadFile = () => {
  console.log('上传文件');
  // uploadFormRef.value.open();
  fileInput.value.click()
};

// 下载文件
const handleDownload = async (row) => {
  console.log(row);
  const { fileName } = row

  await downloadFileFunction(fileName, fileName)
  // window.open(row.fileName);
};

const imgViewRef = ref()
const handleView = (row) => {
  console.log('查看:', row.name);
  if (!row.fileName) {
    message.warning('文件生成中， 请稍后')
    return
  }
  const imgType = ['.jpg', '.jpeg', '.png',]
  const fileType = row.fileName.substring(row.fileName.lastIndexOf('.'))
  if (imgType.includes(fileType)) {
    imgViewRef.value.open(row)
  } else {
    docViewRef.value.open('view', path.value[path.value.length - 1], row);
  }
};

const handleEdit = (row) => {
  console.log('编辑:', row.name);
  if (!row.fileName) {
    message.warning('文件生成中， 请稍后')
    return
  }
  editDialogRef.value.open(row);
}

const creatFileRef = ref()
const handleNewFile = () => {
  creatFileRef.value && creatFileRef.value.open(path.value[path.value.length - 1], 'docx')
  // docViewRef.value.open('create', path.value[path.value.length - 1]);
}

const createFileSuccess = async(results, callback) => {
  // docViewRef.value.open('create', choosePath.value, results );
  const params = {
    ...results
  }
  console.log('params', params);
  const initReponse = await uploadInit({
    url: `${apikeys.file_server_url}/init.docx`,
    path: path.value[path.value.length - 1]?.id ? path.value[path.value.length - 1].filePath : null,
    name: results.fileName,
  })
  params.fileUrl = initReponse.fileUrl
  const response = await TemplateAPI.createFolder(params)
  const docparams = {
    docTitle: results.title,
    fileUrl: params.fileUrl,
    klTemplateId: results.documentTemplate,
    filePrefix: results.documentType,
    fileName: results.fileName,
    fileKey: results.fileKey,
    scope: results.scope,
    id: response.data.id,
  }
  if(results.searchFile) {
    docparams.searchFile = results.searchFile
  }
  const docResponse = await TemplateAPI.createDoc(docparams)
  if(docResponse) {
    callback && callback()
    message.success('正在生成中 请稍后...')
  } else {
    message.error('生成失败')
  }
  handleSearch()
  // handleEdit(response.data)
}
  

const handleMove = (row) => {
  console.log('移动:', row.name);
  moveDialogRef.value.open(row);
}
const timer = ref()

// 初始化加载根目录
onMounted(() => {
  handleSearch();
  // 开启定时器
  setQueryInterval()
});

const setQueryInterval = () => {
  timer.value = setInterval(() => {
    queryList();
  }, 5000);
}

onUnmounted(() => {
  clearInterval(timer.value)
});

// 销毁时清楚定时器
onBeforeUnmount(() => {
  clearInterval(timer.value)
});
</script>

<style scoped lang="scss">
.doc-content {
  background-color: #fff;
  padding: 20px;  
  overflow: hidden;
}
.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.breadcrumb-wrapper {
  display: flex;
  height: 48px;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 20px 0 0;

  .back-button {
    padding: 0;
    font-size: 14px;
  }
}

.name-column {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .name-wrapper {
    width: 400px;
  }

  .name-text {
    cursor: pointer;

    &:hover {
      color: #409eff;
    }
  }

  .more-actions {
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-left: 12px;

    &:hover {
      color: #409eff;
    }
  }
}

.button-group {
  flex: 1;
  display: flex;
  align-items: center;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 320px;
  display: inline-block;
}
</style>