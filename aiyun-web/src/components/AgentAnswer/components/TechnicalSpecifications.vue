<template>
	<el-container class="detail-container">
		<el-main class="main-container">
			<el-form
				ref="formRef"
				v-loading="formLoading"
				:model="formData"
				:rules="formRules"
				label-width="150px"
			>
			<el-form-item label="技术规范书主题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入技术规范书主题" />
      </el-form-item>
			<el-form-item label="可行性研究" prop="fileName">
				<div class="file-upload-wrapper">
					<el-input 
						v-model="formData.fileName" 
						placeholder="请选择要上传的文件"
						readonly
						class="file-input" 
            style="width: 410px"
					>
						<template #append>
							<el-button 
								type="primary" 
								@click="triggerFileInput"
								:loading="uploading || isParse"
							>
								{{ isParse ? '解析中' : uploading ? '上传中...' : '选择文件' }}
							</el-button>
						</template>
					</el-input>
					<input
						type="file"
						ref="fileInput"
						@change="handleFileSelect"
						style="display: none"
						accept=".doc,.docx,.pdf,.png,.jpg,.jpeg,.xlsx, .ppt, .pptx, .txt"
					/>
				</div>
			</el-form-item>
				</el-form>
		</el-main>
		<!-- 底部 -->
		<el-footer class="footer-container">
      <el-button type="primary" v-if="isShowView"  @click="handleClick">查 看</el-button>
      <el-button type="primary" :disabled="submitDisabled" :loading="formLoading" @click="submit">确 定</el-button>
      <el-button @click="handleCancel">取 消</el-button>
		</el-footer>
		</el-container>
		<ReportView ref="reportViewRef" />
  </template>
  <script setup lang="ts">
  import * as FolderAPI from '@/api/knowledge/docmangement'
  import ReportView from './ReportView.vue';
  import { getTechnicalReports } from '@/api/home/<USER>'
  import { useUserStore } from '@/store/modules/user'

	defineOptions({ name: 'TechnicalSpecifications' })
  
  const emit = defineEmits(['cancel', 'submit'])
  const message = useMessage() // 消息弹窗
  const userStore = useUserStore()
  
  const formRef = ref()
  const formData = ref({
      fileName: '',
      fileUrl: '',
  })
  const formRules = ref({
		title: [
			{ required: true, message: '请输入技术规范书主题', trigger: 'blur' },
		],
	})
  const formLoading = ref(false)
  const fileInput = ref(null)
  const uploading = ref(false)
  const isParse = ref(false)
  const submitDisabled = ref(true)
  const isShowView = ref(false)
  
  // 触发文件选择
  const triggerFileInput = () => {
    fileInput.value && fileInput.value.click()
  }
  
  const uploadFile = async(file) => {
    return new Promise((resolve, reject) => {
      FolderAPI.uploadFolderFile({
        file,
      }).then((res) => {
        if (res.code === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      })
    })
  }
  
  const analysData = ref({})
  
  const timer = ref()
  
  // 处理文件选择
  const handleFileSelect = async (event) => {
    const file = event.target.files[0]
    if (!file) return
    submitDisabled.value = true
    responseKey.value = ''
    // 执行上传
    try {
      uploading.value = true
      isShowView.value = false
      clearInterval(timer.value)
      const fileResponse = await uploadFile(file)
      const { fileName, fileUrl,} = fileResponse.data
      const params = {
        fileUrl,
        type: '3',
      }
      const analyResponse = await FolderAPI.getCloudAnalysisBack(params)
      isParse.value = true
      timer.value = setInterval(async () => {
        const resultResponse= await FolderAPI.getCloudAnalysisBackResults({
          ...analyResponse
        })
        if (resultResponse.analysisStatus !== '1') {
          submitDisabled.value = false
                  analysData.value = resultResponse
          isParse.value = false
          clearInterval(timer.value)
        }
      }, 20000)
      formData.value.fileName = fileName // 上传成功后设置文件名
      formData.value.fileUrl = fileUrl
      ElMessage.success('文件上传成功')
    } catch (error) {
      ElMessage.error('文件上传失败')
      console.error('上传失败:', error)
    } finally {
      uploading.value = false
      fileInput.value.value = '' // 清空input
    }
  }
  const reportViewRef = ref()
  const responseKey = ref()
  const submit = async() => {
    formLoading.value = true
    clearInterval(timer.value)
    if(responseKey.value) {
      message.warning('已生成报告')
      return
    }
    const response = await getTechnicalReports({
      keyan: analysData.value?.content,
      title: formData.value.title,
      loginUserid: userStore.getUser.id,
    })
    responseKey.value = response
    timer.value = setInterval(async () => {
      const result = await getTechnicalReports({
        keyan: analysData.value?.content,
        title: formData.value.title,
        loginUserid: userStore.getUser.id,
        key: responseKey.value
      })
      const { status } = JSON.parse(result)
      // 成功
      if (status === 1) {
        clearInterval(timer.value)
        isShowView.value = true
        formLoading.value = false
      } else if(status === 2) {
        message.error('生成失败')
        responseKey.value = ''
        formLoading.value = false
        clearInterval(timer.value)
      }
    }, 10000)
  }
  
  const handleClick = () => {
    reportViewRef.value.open(responseKey.value, 'technical')
  }
  // 取消
  const handleCancel = () => {
      emit('cancel')
  }
  
  </script>
  
  <style lang="scss" scoped>
  .qa-container {
    margin: 0 auto;
    height: 100%;
    min-height: 200px;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
  .detail-container {
    height: 100%;
  }
  // main 容器
  .main-container {
    margin: 0;
    padding: 0;
    position: relative;
    height: 100%;
    width: 100%;
  
    .message-container {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      overflow-y: hidden;
      padding: 0;
      margin: 0;
    }
  }
  
  // 底部
  .footer-container {
      height: 36px;
      text-align: right;
  }
  // 按钮
  .tl-fixed {
      width: 50px;
      height: 130px;
      background-color: #5e60f3;
      position: fixed;
      z-index: 99;
      right: 30px;
      bottom: 10vh;
      cursor: pointer;
      border-radius: 25px;
      padding: 10px 0;
      .fixed-text {
        writing-mode: vertical-lr;
        font-size: 18px;
        font-weight: 700;
        color: #fff;
        line-height: 50px;
        display: inline-block;
        width: 100%;
        height: 100%;
        text-align: center;
        letter-spacing: 8px;
      }
  }
  
  </style>
  
  <style lang="scss">
    .pop-list {
      width: 100%;
      .pop-item {
        text-align: center;
        > * {
          vertical-align: middle;
        }
      }
    }
    .el-popover {
      min-width: 50px;
    }
  </style>
