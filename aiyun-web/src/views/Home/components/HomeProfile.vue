<template>
    <el-dialog 
      v-model="dialogVisible" 
      fullscreen
      :close-on-click-modal="false"
      title="个人中心">
			<div class="chat-list-dialog">
        <Profile v-if="dialogVisible" />
      </div>
    </el-dialog>
  </template>
  <script setup>

	import Profile from '@/views/Profile/Index.vue'
  
  defineOptions({ name: 'HomeProfile' })

  const emit = defineEmits(['success'])

  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  
  const dialogVisible = ref(false) // 弹窗的是否展示

	const NewTemRef = ref(null)
  /** 打开弹窗 */
  const open = async () => {
		console.log(2222);
		dialogVisible.value = true;
  }

  defineExpose({ open }) // 提供 open 方法，用于打开弹窗
  
  </script>
  
  <style lang="scss" scoped>
  .chat-list-dialog {
    height: 600px; 
    position: relative;
    background-color: var(--el-color-white);
  }
  </style>
