<template>
	<el-dialog 
		v-model="dialogVisible" 
		:scroll="true" 
		:fullscreen="true" 
		:destroy-on-close="true"
		:title="dialogTitle">
		<div class="chat-detail" v-loading="activeMessageListLoading">
			<MessageList 
				ref="messageRef"
				v-if="messageList.length > 0"
				:conversation="activeConversation" 
				:list="messageList" 
				:isDetail="true"
				@on-delete-success="handleMessageDelete" />
			<div v-else class="empty">
				<p>暂无对话</p>
			</div>
		</div>
	</el-dialog>

</template>

<script setup>
import MessageList from './MessageList.vue'
import { 
  sendMessage, 
  getKlSessionMessage, 
  getKlMessageConfig, 
  deleteKlSessionMessageBySessionId,
  saveKlMessageConfig,
  deleteKlSessionMessage
 } from '@/api/knowledge/model'
defineOptions({ name: 'ChatDetail' })

const message = useMessage() // 消息弹窗

const activeMessageList = ref([])
const messageList = computed(() => {
  if (activeMessageList.value.length > 0) {
    return activeMessageList.value
  }
  return []
})


const dialogVisible = ref(false)
const dialogTitle = ref('')
const activeConversationId = ref(null)
const activeConversation = ref(null)
const open = async(row) => {
	activeConversation.value = row
	activeConversationId.value = row.id
  dialogVisible.value = true
  dialogTitle.value = row.name
	await nextTick()
	getMessageList()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 处理删除 message 消息 */
const handleMessageDelete = async(id) => {
  await deleteKlSessionMessage(id)
  message.success('删除成功！')
  // 刷新 message 列表
  getMessageList()
}

const activeMessageListLoadingTimer = ref(null)
const activeMessageListLoading = ref(false)
/** 获取消息 message 列表 */
const getMessageList = async () => {
  try {
    if (activeConversationId.value === null) {
      return
    }
    const data = await getKlSessionMessage({
      klModelSessionId: activeConversationId.value,
      pageNum: 1,
      pageSize: 1000
    })
		activeMessageListLoading.value  = true
    // 获取消息列表并转换格式
    activeMessageList.value = data.map(item => ({
      id: item.id,
      conversationId: activeConversationId.value,
      type: item.type,
      userId: item.userId || '',
      roleId: item.roleId || '',
      model: item.model || 0,
      modelId: item.modelId || 0,
      content: item.type === 'user' ? item.inquiryContent : item.answerContent,
      tokens: item.tokens || 0,
      createTime: item.createTime,
      roleAvatar: item.roleAvatar || '',
      userAvatar: item.userAvatar || '',
      inquiryContent: item.inquiryContent || '',
      answerContent: item.answerContent || '',
      file: item.file || []
    }))
  } finally {
    // time 定时器，如果加载速度很快，就不进入加载中
    if (activeMessageListLoadingTimer.value) {
      clearTimeout(activeMessageListLoadingTimer.value)
    }
    // 加载结束
    activeMessageListLoading.value = false
  }
}

</script>

<style lang="scss" scoped>
.chat-detail {
	position: relative;
	height: calc(100vh - 80px);
}
.empty {
	text-align: center;
	margin-top: 100px;
	font-size: 16px;
}
</style>