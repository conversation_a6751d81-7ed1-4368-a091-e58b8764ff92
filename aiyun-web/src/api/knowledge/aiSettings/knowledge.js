import request from '@/config/axios'

// 
export const getklklArrangeBasePage = (params) => {
  return request.get({ url: '/v1/kownledge/klArrangeBase/page', params })
}

export const getklklArrangeChatPage = (params) => {
  return request.get({ url: '/v1/kownledge/klArrangeChat/page', params })
}
// 
export const addklArrangeBase = (data) => {
  return request.post({ url: '/v1/kownledge/klArrangeBase', data })
}

// 
export const updateklArrangeBase = (data) => {
  return request.put({ url: '/v1/kownledge/klArrangeBase', data })
}

// 
export const deleteklArrangeBase = (id) => {
  return request.delete({ url: `/v1/kownledge/klArrangeBase/${id}` })
}

// 
export const getklArrangeBase = (id) => {
  return request.get({ url: `/v1/kownledge/klArrangeBase/${id}` })
}
