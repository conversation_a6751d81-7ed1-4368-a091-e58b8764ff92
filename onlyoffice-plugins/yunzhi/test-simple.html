<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Yun<PERSON>hi Plugin Simple Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }
            .status {
                padding: 10px;
                margin: 10px 0;
                border-radius: 4px;
            }
            .success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .warning {
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
            button {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin: 5px;
            }
            button:hover {
                background-color: #0056b3;
            }
            .log {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
                max-height: 300px;
                overflow-y: auto;
                font-family: monospace;
                font-size: 12px;
                white-space: pre-wrap;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>YunZhi Plugin 简单测试</h1>

            <div id="status" class="status warning">正在初始化测试环境...</div>

            <div>
                <h3>测试操作：</h3>
                <button onclick="initPlugin()">初始化插件</button>
                <button onclick="testPluginMethods()">测试插件方法</button>
                <button onclick="clearLog()">清空日志</button>
            </div>

            <div>
                <h3>控制台日志：</h3>
                <div id="log" class="log"></div>
            </div>
        </div>

        <!-- 首先加载 OnlyOffice SDK 模拟 -->
        <script>
            // 创建模拟的 OnlyOffice 环境
            window.Asc = window.Asc || {};
            window.Asc.plugin = window.Asc.plugin || {};

            // 模拟插件信息
            window.Asc.plugin.info = {
                guid: 'asc.{4B5EFD7F-1234-1EF4-FEAB-BFBF2E1D86AC}',
                userName: '测试用户',
                lang: 'zh-CN',
                editorType: 'word',
            };

            // 模拟插件方法
            window.Asc.plugin.executeMethod = function (method, params, callback) {
                log(`执行方法: ${method}`, params);
                if (callback) {
                    setTimeout(() => callback('模拟返回结果'), 100);
                }
            };

            window.Asc.plugin.executeCommand = function (command, data) {
                log(`执行命令: ${command}`, data);
            };

            window.Asc.plugin.callCommand = function (func, isAsync, isReturnValue, callback) {
                log('调用命令函数');
                try {
                    const result = func();
                    if (callback) callback(result);
                } catch (e) {
                    log('命令执行错误: ' + e.message);
                }
            };

            window.Asc.plugin.tr = function (text) {
                return text; // 简单的翻译函数
            };

            // 日志函数
            function log(message, data) {
                const logDiv = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${message}`;

                if (data) {
                    console.log(logEntry, data);
                    logDiv.textContent += `${logEntry} ${JSON.stringify(data)}\n`;
                } else {
                    console.log(logEntry);
                    logDiv.textContent += `${logEntry}\n`;
                }

                logDiv.scrollTop = logDiv.scrollHeight;
            }

            function updateStatus(message, type) {
                const statusDiv = document.getElementById('status');
                statusDiv.textContent = message;
                statusDiv.className = `status ${type}`;
            }

            function initPlugin() {
                log('=== 开始初始化插件 ===');

                // 确保 info 对象存在
                if (!window.Asc.plugin.info) {
                    log('警告: window.Asc.plugin.info 不存在，重新创建...');
                    window.Asc.plugin.info = {
                        guid: 'asc.{4B5EFD7F-1234-1EF4-FEAB-BFBF2E1D86AC}',
                        userName: '测试用户',
                        lang: 'zh-CN',
                        editorType: 'word',
                    };
                    log('已重新创建 window.Asc.plugin.info 对象');
                } else {
                    log('window.Asc.plugin.info 对象存在');
                    log('用户名: ' + window.Asc.plugin.info.userName);
                    log('语言: ' + window.Asc.plugin.info.lang);
                }

                if (typeof window.Asc.plugin.init === 'function') {
                    try {
                        log('找到插件初始化函数，开始调用...');
                        window.Asc.plugin.init();
                        updateStatus('插件初始化成功！', 'success');
                        log('=== 插件初始化完成 ===');
                    } catch (e) {
                        updateStatus('插件初始化失败: ' + e.message, 'error');
                        log('初始化错误: ' + e.message);
                        console.error('Plugin init error:', e);
                    }
                } else {
                    updateStatus('未找到插件初始化函数', 'error');
                    log('window.Asc.plugin.init 不是一个函数');
                    log('当前 window.Asc.plugin.init 类型: ' + typeof window.Asc.plugin.init);
                }
            }

            function testPluginMethods() {
                log('=== 测试插件方法 ===');

                // 测试各种插件方法
                if (window.Asc.plugin.executeMethod) {
                    window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
                        log('获取选中文本: ' + text);
                    });
                }
            }

            function clearLog() {
                document.getElementById('log').textContent = '';
            }

            // 页面加载完成后的初始化
            window.addEventListener('load', function () {
                log('测试页面加载完成');
                updateStatus('正在加载插件脚本...', 'warning');
            });
        </script>

        <!-- 加载插件的 SDK 文件 -->
        <script type="text/javascript" src="scripts/sdkjs/plugins.js"></script>
        <script type="text/javascript" src="scripts/sdkjs/plugins-ui.js"></script>

        <!-- 加载插件主文件 -->
        <script type="text/javascript" src="scripts/main.js"></script>
        <script type="text/javascript" src="scripts/yunzhi_ai.js"></script>
        <script type="text/javascript" src="scripts/prompt.js"></script>
        <script type="text/javascript" src="scripts/chat-footer.js"></script>

        <script>
            // 脚本加载完成后自动初始化
            window.addEventListener('load', function () {
                setTimeout(() => {
                    log('所有脚本加载完成，准备初始化插件');

                    // 详细检查插件对象
                    log('=== 插件对象检查 ===');
                    log('window.Asc 存在: ' + (window.Asc ? '是' : '否'));

                    if (window.Asc) {
                        log('window.Asc.plugin 存在: ' + (window.Asc.plugin ? '是' : '否'));

                        if (window.Asc.plugin) {
                            log('window.Asc.plugin 的所有属性:');
                            for (let key in window.Asc.plugin) {
                                log(`  ${key}: ${typeof window.Asc.plugin[key]}`);
                            }

                            if (window.Asc.plugin.init) {
                                log('✓ window.Asc.plugin.init 函数存在');
                                log('函数类型: ' + typeof window.Asc.plugin.init);
                                updateStatus("插件加载成功，点击'初始化插件'按钮开始测试", 'success');
                            } else {
                                log('✗ window.Asc.plugin.init 函数不存在');
                                updateStatus('插件加载失败：未找到初始化函数', 'error');
                            }
                        } else {
                            log('✗ window.Asc.plugin 对象不存在');
                            updateStatus('插件加载失败：plugin 对象不存在', 'error');
                        }
                    } else {
                        log('✗ window.Asc 对象不存在');
                        updateStatus('插件加载失败：Asc 对象不存在', 'error');
                    }

                    log('=== 检查完成 ===');
                }, 1000);
            });
        </script>
    </body>
</html>
