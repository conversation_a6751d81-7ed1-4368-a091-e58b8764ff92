<template>
  <Dialog 
    v-model="dialogVisible" 
    :scroll="true"
    title="移动">
    <div class="content">
      <div class="breadcrumb-wrapper">
        <!-- 返回上一级按钮 -->
        <el-button
          v-if="path.length > 1"
          @click="handleGoBack"
          type="text"
          class="back-button"
        >
          返回上一级
        </el-button>
        <el-divider direction="vertical" />
        <!-- 路径导航 -->
        <el-breadcrumb separator="/">
          <el-breadcrumb-item
            v-for="(item, index) in path"
            :key="index"
            @click="handleBreadcrumbClick(index)"
          >
            <span style="cursor: pointer;">{{ item.fileName }}</span>
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 文件列表 -->
      <div>
        <div class="file-item flex align-center" @click="handleNameClick(item)" v-for="(item, index) in fileList" :key="index">
            <span>
              <el-icon class="mr-[4px]">
                <Document v-if="item.filePrefix" />
                <FolderOpened v-else />
              </el-icon>
            </span>
            <span>{{ item.fileName }}</span>
        </div>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="handleSearch"
        />
      </div>
    </div>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">移动到此</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as FolderAPI from '@/api/knowledge/docPersonal'

import { FolderOpened,  Document, } from '@element-plus/icons-vue'

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'MovePersonDialog' })

const emit = defineEmits(['success'])

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗



const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const dialogType = ref('')
// 当前路径
const path = ref([{ id: '', fileName: '根目录', level: '1' }]);
// 当前文件夹下的文件列表
const fileList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileName: '',
  parentId: '',
  level: '',
})
const total = ref(0)
// const fakeData = {
//   root: [
//     { id: '1', name: '文件夹1', type: 'folder', size: 0, uploadDate: '2023-10-01', parseMethod: '自动', shareEnabled: false, parseStatus: '已解析' },
//     { id: '2', name: '文件1.txt', type: 'file', size: 1024, uploadDate: '2023-10-02', parseMethod: '手动', shareEnabled: true, parseStatus: '未解析' },
//   ],
//   '1': [
//     { id: '3', name: '子文件夹1', type: 'folder', size: 0, uploadDate: '2023-10-03', parseMethod: '自动', shareEnabled: false, parseStatus: '已解析' },
//     { id: '4', name: '文件2.txt', type: 'file', size: 2048, uploadDate: '2023-10-04', parseMethod: '手动', shareEnabled: true, parseStatus: '未解析' },
//   ],
//   '3': [
//     { id: '5', name: '文件3.txt', type: 'file', size: 512, uploadDate: '2023-10-05', parseMethod: '自动', shareEnabled: false, parseStatus: '已解析' },
//   ],
// };

// 加载文件夹内容
const loadFolderContent = async (row) => {
  // fileList.value = fakeData[folderId] || [];
  const { id, level } = row
  const response = await FolderAPI.getCloudPage({
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    fileName: queryParams.value.fileName,
    parentId: id,
    level: id ? parseInt(level) + 1 : level,
  });
  total.value = response.total;
  fileList.value = response.records;
};

const handleSearch = () => {
  loadFolderContent(path.value[path.value.length - 1]);
}

// 点击名称文本进入下一级
const handleNameClick = (row) => {
  if (!row.filePrefix) {
    path.value.push(row);
    loadFolderContent(row);
  }
};

// 返回上一级
const handleGoBack = () => {
  if (path.value.length > 1) {
    path.value.pop(); // 移除最后一项
    loadFolderContent(path.value[path.value.length - 1]); // 加载上一级文件夹内容
  }
};

// 点击路径导航返回上一级
const handleBreadcrumbClick = (index) => {
  path.value = path.value.slice(0, index + 1);
  loadFolderContent(path.value[index]);
};

const fileId = ref('')
/** 打开弹窗 */
const open = async (row) => {
  fileId.value = row.id
  path.value = [{ id: '', fileName: '根目录', level: '1' }]
  loadFolderContent(path.value[path.value.length - 1]);
  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const submitForm = async () => {
  console.log('提交');
  const params = {
    id: fileId.value,
    parentId: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].id : null,
  }
  const response = await FolderAPI.movementFolder(params)
  if(response) {
    dialogVisible.value = false
    message.success('移动成功')
    emit('success')
  }
}

/** 提交表单 */

</script>

<style lang="scss" scoped>
  .breadcrumb-wrapper {
    display: flex;
    height: 32px;
    align-items: center;
    padding: 0 20px 0 0;
    .back-button {
      padding: 0;
      font-size: 14px;
    }
  }
  .file-item {
    line-height: 32px;
    padding: 8px;
    border-bottom: 1px solid #ccc;
    cursor: pointer;
  }
</style>
