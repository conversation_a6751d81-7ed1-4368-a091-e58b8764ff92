<template>
  <Dialog 
    v-model="dialogVisible" 
    :scroll="true"
    width="70vw"
    title="搜索">
    <div class="content" style="padding: 0 20px;">
      <div v-loading="loading" v-if="fileList.length > 0">
        <el-card v-for="item in fileList" :key="item.id" class="card">
          <el-popover
            placement="top"
            trigger="hover"
            :width="'70vw'"
            popper-class="popover-answer"
          >
            <template #reference>
              <p class="card-content" v-html="item.highlight + '...'"></p>
            </template>
            <div v-html="item.content" class="answer-pop-content"></div>
          </el-popover>
          <p class="card-footer">{{ item.document_keyword }}</p>
        </el-card>
      </div>
      <div v-else>
        未搜索到数据
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import axios from 'axios'
// import * as FolderAPI from '@/api/knowledge/docmangement'

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'KnowledgeDialog' })

const emit = defineEmits(['success'])

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
// 当前文件夹下的文件列表
const fileList = ref([]);

const loading = ref(false)

/** 打开弹窗 */
const open = async (kerword, ragApi) => {
  fileList.value = []
  dialogVisible.value = true
  loading.value = true
  const response = await axios({
    url: `${ragApi.ragflow_url}/api/v1/retrieval`,
    method: 'post',
    headers: { 
      'Content-Type': 'application/json' ,
      Authorization: `${ragApi.ragflow_token}`,
      Accept: '*/*'
    },
    data: { question: kerword, dataset_ids: [`${ragApi.kl_cloud_data}`] }
  })
  const { data } = response
  fileList.value = data.data.chunks
  loading.value = false
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

</script>

<style lang="scss" scoped>
  .breadcrumb-wrapper {
    display: flex;
    height: 48px;
    align-items: center;
    margin-bottom: 10px;
    padding: 0 20px;
    .back-button {
      padding: 0;
      font-size: 14px;
    }
  }
  .card {
    margin-bottom: 16px;
  }
  .card-content {
    font-size: 14px;
  }
</style>
