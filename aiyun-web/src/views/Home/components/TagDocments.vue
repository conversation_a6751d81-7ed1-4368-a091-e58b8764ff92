<template>
	<Dialog 
		v-model="dialogVisible" 
		:scroll="true"
		destroy-on-close
		:title="dialogTitle">
		<div v-loading="loading">
			<el-table
				v-loading="loading"
				:data="fileList"
				row-key="id"
			>
      	<el-table-column prop="fileName" label="文件名称">
					<template #default="scope">
						<el-tooltip :content="scope.row.fileName" placement="top">
							<span @click="handleClick(scope.row)" class="file-name-item">{{ scope.row.fileName }}</span>
						</el-tooltip>
					</template>
				</el-table-column>
      	<el-table-column prop="createTime" label="创建时间" />
      	<el-table-column prop="creator" label="创建人" width="120px" />
			</el-table>
			<Pagination
				:total="total"
				v-model:page="queryParams.pageNum"
				v-model:limit="queryParams.pageSize"
				@pagination="getDocuments"
			/>
		</div>
	</Dialog>
</template>

<script setup>
	import * as homeApi from '@/api/home/<USER>'

	const dialogVisible = ref(false)
	const loading = ref(false)
	const fileList = ref([])
	const dialogTitle = ref('文档列表')

	defineOptions({ name: 'TagDocments' })

	const emit = defineEmits(['docmentClick'])
	const queryParams = reactive({
		pageNum: 1,
		pageSize: 10,
	})
	const total = ref(0)
	const open = (tagParams) => {
		dialogVisible.value = true
		dialogTitle.value = tagParams.label
		queryParams.value = {
			...tagParams, 
			label: [tagParams.label],
			pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
		}
		getDocuments()
	}

	defineExpose({ open })
	
	const handleClick = (item) => {
		emit('docmentClick', item)
	}

	const getDocuments = async(params) => {
		queryParams.value.pageNum = params?.page || queryParams.value.pageNum
		queryParams.value.pageSize = params?.limit || queryParams.value.pageSize
		try {
			loading.value = true
			const response = await homeApi.getListBylabel(queryParams.value)
			fileList.value = response.records
			total.value = response.total
			loading.value = false
		} catch {
			console.log('获取文档列表失败');
		}
	}

	// onMounted(() => {
	// 	getDocuments()
	// })
</script>

<style lang="scss" scoped>
	.file-item {
		line-height: 32px;
		padding: 8px;
		align-items: center;
		border-bottom: 1px solid #ccc;
		cursor: pointer;
		&:hover {
			background-color: val(--color-primary);
		}
	}
	.file-name-item {
		cursor: pointer;
		color: #1890ff;
		display: inline-block;
		white-space: nowrap;      
		overflow: hidden;        
		text-overflow: ellipsis; 
		width: 220px;
	}
	
</style>