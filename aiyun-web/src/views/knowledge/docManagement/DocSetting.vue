<template>
  <ContentWrap>
    <!-- 头部按钮和搜索框 -->
    <div class="header">
      <!-- 左侧按钮 -->
      <div>
        <el-button type="primary" @click="handleUploadFile">上传文件</el-button>
        <el-button type="primary" @click="handleNewFolder('folder')">新建文件夹</el-button>
        <!-- <el-button type="primary" @click="handleAssignPermission(null, 'header')">
          <Icon icon="fa:user-plus" class="mr-5px" />
          分配权限
        </el-button> -->
      </div>

      <!-- 右侧搜索框和按钮 -->
      <div>
        <el-input v-model="queryParams.fileName" placeholder="请输入名称搜索" style="width: 200px; margin-right: 10px;" />
        <el-button type="primary" @click="handleSearchKnowledge">搜索</el-button>
      </div>
    </div>

    <!-- 路径导航 -->
    <div class="breadcrumb-wrapper">
      <!-- 返回上一级按钮 -->
      <el-button v-if="path.length > 2" @click="handleGoBack" type="primary" link claass="back-button">
        返回上一级
      </el-button>
      <el-divider direction="vertical" :style="computedStyle" />
      <!-- 路径导航 -->
      <el-breadcrumb separator="/">
        <el-breadcrumb-item v-for="(item, index) in path" :key="index" @click="handleBreadcrumbClick(index)">
          <span style="cursor: pointer;">{{ item.fileName }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 文件列表 -->
    <el-table v-loading="tableLoading" :data="fileList">
      <el-table-column label="名称" min-width="500">
        <template #default="{ row }">
          <div class="name-column">
            <!-- 名称文本 -->
            <!-- 重命名 文本框 确认 取消 -->
            <div class="name-wrapper">
              <div v-if="editId == row.id">
                <el-input v-model="editName" size="small" maxlength="40" style="width: 200px;" />
                <el-button 
                  type="primary" 
                  style="margin-left: 5px;" 
                  size="small"
                  @click="handleEditNameConfirm(row)">确认</el-button>
                <el-button type="primary" link size="small" @click="handleEditNameCancel(row)">取消</el-button>
              </div>
              <span v-else :class="row.filePrefix ? '' : 'name-text'" @click.stop="handleNameClick(row)">
                <div class="flex align-center" v-if="row.filePrefix">
                  <span>
                    <el-icon class="mr-[4px]">
                      <Document />
                    </el-icon>
                  </span>
                  <el-tooltip :content="row.fileName" placement="top">
                    <span class="ellipsis">
                      {{ row.fileName }}
                    </span>
                  </el-tooltip>
                </div>
                <div class="flex align-center" v-else>
                  <span>
                    <el-icon class="mr-[4px]">
                      <FolderOpened />
                    </el-icon>
                  </span>
                  <span>
                    <el-tooltip :content="row.fileName" placement="top">
                      <span class="ellipsis">
                        {{ row.fileName }}
                      </span>
                    </el-tooltip>
                  </span>
                </div>
              </span>
            </div>

            <!-- 操作按钮组 -->
            <div class="button-group" v-if="row.id != '1' && row.id != '-1'">
              <!-- 文件专属操作 -->
              <template v-if="row.filePrefix">
                
                <!-- 查看文档 编辑文档 -->
                <el-tooltip content="查看文档" placement="top">
                  <el-button type="primary" link @click.stop="handleView(row)">
                    <!-- <el-icon><View /></el-icon> -->
                    <Icon icon="fa:eye" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="编辑文档" v-if="isAdmin" placement="top">
                  <el-button type="primary" link @click.stop="handleEdit(row)">
                    <Icon icon="fa-solid:file-signature" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="下载"  placement="top">
                  <el-button type="primary" link @click.stop="handleDownload(row)">
                    <Icon icon="fa:cloud-download" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="重命名" v-if="isAdmin" placement="top">
                  <el-button type="primary" link @click.stop="handleRename(row)">
                    <Icon icon="fa:edit" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="移动" v-if="isAdmin" placement="top">
                  <el-button type="primary" link @click.stop="handleMove(row)">
                    <Icon icon="fa:folder-open" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除" v-if="isAdmin || isSystemSuperAdmin" placement="top">
                  <el-button type="primary" link @click.stop="handleDelete(row)">
                    <Icon icon="ep:delete" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="解析" v-if="isAdmin || isSystemSuperAdmin" placement="top">
                  <el-button type="primary" link :loading="row.analysisStatus == '1'" @click.stop="handleParse(row)">
                    <Icon v-if="row.analysisStatus != '1'" icon="ep:magic-stick" />
                  </el-button>
                </el-tooltip>
                <!-- <el-tooltip content="解析设置" placement="top">
                  <el-button type="primary" link @click.stop="handleParseSetting(row)">
                    <Icon icon="ep:setting" />
                  </el-button>
                </el-tooltip> -->
                <!-- <el-tooltip content="查看报告" placement="top">
                  <el-button type="primary" link @click.stop="handleReportView(row)">
                    <Icon icon="ep:reading" />
                  </el-button>
                </el-tooltip> -->
              </template>
              <template v-else>
                <el-tooltip content="重命名" v-if="isAdmin" placement="top">
                  <el-button type="primary" link @click.stop="handleRename(row)">
                    <Icon icon="fa:edit" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="移动" v-if="isAdmin" placement="top">
                  <el-button type="primary" link @click.stop="handleMove(row)">
                    <!-- <el-icon><FolderOpened /></el-icon> -->
                    <Icon icon="fa:folder-open" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="删除" placement="top">
                  <el-button type="primary" link @click.stop="handleDelete(row)">
                    <Icon icon="ep:delete" />
                  </el-button>
                </el-tooltip>
                <el-tooltip content="分配权限" v-if="isAdmin" placement="top">
                  <el-button type="primary" link @click.stop="handleAssignPermission(row, 'folder')">
                    <Icon icon="fa:user-plus" />
                  </el-button>
                </el-tooltip>
              </template>

            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传日期" width="200">
        <template #default="{ row }">
          <span style="display: inline-block;line-height: 32px;">{{ row.id === '1' || row.id === '-1' ? '-' :
            row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="analysisStatus" label="解析状态" min-width="100">
        <template #default="{ row }">
          <!-- 0 未解析1解析中2解析完成 -->
          <span v-if="row.id === '1' || row.id === '-1' || !row.filePrefix">-</span>
          <el-popover placement="top" :width="400" >
            <template #reference>
              <el-tag v-if="row.filePrefix" :type="statusClass[row.analysisStatus]">{{ analysisObj[row.analysisStatus]
              }}</el-tag>
            </template>
            <p>解析结果：{{ row.analysisMessage ? row.analysisMessage : '-'}}</p>
          </el-popover>
          <!-- <el-button 
            v-if="row.filePrefix && (row.analysisStatus === '2' || row.analysisStatus === '3')" 
            type="primary" 
            link 
            size="small"
            style="margin-left: 10px;" 
            @click="handleDocumentRefresh(row)"><el-icon>
              <Refresh />
            </el-icon></el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <Pagination 
      :total="total" 
      v-model:page="queryParams.pageNum" 
      v-model:limit="queryParams.pageSize"
      @pagination="queryList" />
  </ContentWrap>
  <UploadForm 
    ref="uploadFormRef" 
    :path="path[path.length - 1]" 
    :create-folder="FolderAPI.createFolder" 
    :update-folder="FolderAPI.updateFolder" 
    @success="handleSearch" />
  <UploadMultipleForm 
    ref="uploadMultipleFormRef" 
    :path="path[path.length - 1]"
    :create-folder="FolderAPI.uploadFolderFileMore"
    :update-folder="FolderAPI.updateFolder"
    @success="handleSearch" />
  <DocView ref="docViewRef" @success="handleSearch" />
  <ParseSettingsForm ref="parseSettingsFormRef" @success="handleSearch" />
  <MoveDialog ref="moveDialogRef" :isCommon="false" @success="handleSearch" />
  <ShareDialog ref="shareDialogRef" @success="handleSearch" />
  <ShareList ref="shareListRef" />
  <ReportView ref="reportViewRef" />
  <KnowledgeDialog ref="knowledgeDialogRef" />
  <ImgView ref="imgViewRef" />
  <AssignPublicPermissionDialog  ref="assignPermissionDialogRef" />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
// 新增图标引入
import {  FolderOpened,Document, Refresh, } from '@element-plus/icons-vue'
import * as FolderAPI from '@/api/knowledge/docmangement'
import { CommonStatusEnum } from '@/utils/constants'
import { useUserStore } from '@/store/modules/user'
import { v4 as uuidv4 } from 'uuid'
import { downloadFileFunction } from '@/utils/downloadFile'
import { knowledgeFileDownload } from "@/api/knowledge/docmangement";
import axios from 'axios'
import { saveCloudButtonRole } from '@/api/knowledge/docmangement'
import UploadMultipleForm from './components/UploadMultipleForm.vue';
import { 
  ParseSettingsForm, 
  UploadForm, 
  DocView, 
  MoveDialog, 
  ShareDialog, 
  ShareList, 
  ReportView, 
  KnowledgeDialog, 
  ImgView,
  AssignPublicPermissionDialog, 
} from './components';


const route = useRoute() 
const router = useRouter() 
const message = useMessage() // 消息弹窗
const userStore = useUserStore()
const apikeys = userStore.getAppConfig
const roles = userStore.getRoles
const isSystemSuperAdmin = roles.includes('super_admin')

// 当前路径
const path = ref([{ id: '', fileName: '知识库', level: '1' }]);
// 0 未解析1解析中2解析完成
const analysisObj = ref({
  '0': '未解析',
  '1': '解析中',
  '2': '解析完成',
  '3': '解析失败'
});

const statusClass = ref({
  '0': 'info',
  '1': 'primary',
  '2': 'success',
  '3': 'danger',
})

// 当前文件夹下的文件列表
const fileList = ref([]);

// 编辑id
const editId = ref('-1');
const editName = ref('');
const tableLoading = ref(false);

// 搜索查询
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileName: '',
  keyword: ''
});
const total = ref(0);
const parseSettingsFormRef = ref(null);
const docViewRef = ref(null);
const moveDialogRef = ref(null);
const shareDialogRef = ref(null);

// 加载文件夹内容
const loadFolderContent = async (row) => {
  const { id, level, datasetId } = row
  // tableLoading.value = true;
  const response = await FolderAPI.getCloudPage({
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    fileName: queryParams.value.fileName,
    datasetId,
    parentId: id,
    level: id ? parseInt(level) + 1 : level,
  });
  tableLoading.value = false;
  total.value = response.total;
  fileList.value = response.records;
  if(timer.value) {
    clearInterval(timer.value);
  }
  const isAnalysising = response.records.some(item => item.analysisStatus === '1');
  if(isAnalysising) {
    timer.value = setInterval(() => {
      loadFolderContent(path.value[path.value.length - 1]);
    }, 10000);
  }
};

const queryList = () => {
  loadFolderContent(path.value[path.value.length - 1]);
}

const handleSearch = () => {
  queryParams.value.pageNum = 1;
  loadFolderContent(path.value[path.value.length - 1]);
}

const handleReset = () => {
  queryParams.value.fileName = ''
  queryParams.value.pageNum = 1;
  loadFolderContent(path.value[path.value.length - 1]);
}

// 新建文件夹
const handleNewFolder = (type) => {
  console.log('新建文件夹');
  clearInterval(timer.value)
  fileList.value.unshift({ id: '-1', fileName: '', type, });
};

// 点击名称文本进入下一级
const handleNameClick = (row) => {
  if (!row.filePrefix) {
    path.value.push({
      ...row,
      knowledgeId: knowledgeId.value,
    });
    loadFolderContent(row);
  }
};

// 点击路径导航返回上一级
const handleBreadcrumbClick = (index) => {
  clearInterval(timer.value)
	if(index === 0) {
		router.push({
			path: '/knowledge/ragflow/mymanagement'
		})
	} else {
		path.value = path.value.slice(0, index + 1);
		loadFolderContent(path.value[index]);
    setQueryInterval()
	}
};

// 重命名
const handleRename = (row) => {
  if (row.filePrefix) {
    uploadFormRef.value.open('0', row);
  } else {
    const fileName = row.fileName
    editName.value = fileName
    // const lastDotIndex = fileName.lastIndexOf('.');
    // editName.value = lastDotIndex === -1 ? fileName : fileName.substring(0, lastDotIndex);
    row.type = 'folder'
    editId.value = row.id;
    clearInterval(timer.value)
  }
};

// 删除
const handleDelete = async (row) => {
  console.log('删除:', row.name);
  await message.delConfirm()
  const response = await FolderAPI.deleteFolder(row.id)
  if (row.documentId) {
    await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_data}/documents`,
      method: 'delete',
      headers: {
        Authorization: `${apikeys.ragflow_token}`,
      },
      data: {
        ids: [row.documentId],
      }
    })
  }
  if (response.code === 200) {
    message.success('删除成功')
    handleSearch()
  }
};

// 喜欢
const handleLike = async (row) => {
  console.log('喜欢:', row.name);
  const response = await FolderAPI.saveFolderLikes({ klCloudId: row.id })
  if (response.code === 200) {
    message.success('喜欢成功')
    handleSearch()
  }
};

// 取消喜欢
const handleCancelLike = async (row) => {
  console.log('取消喜欢:', row.name);
  const response = await FolderAPI.deleteFolderLikes(row.id)
  if (response) {
    message.success('取消喜欢成功')
    handleSearch()
  }
};

// 收藏
const handleCollect = async (row) => {
  console.log('收藏:', row.name);
  const response = await FolderAPI.saveFolderFavorites({ klCloudId: row.id })
  if (response.code === 200) {
    message.success('收藏成功')
    handleSearch()
  }
};

// 取消收藏
const handleCancelCollect = async (row) => {
  console.log('取消收藏:', row.name);
  const response = await FolderAPI.deleteFolderFavorites(row.id)
  if (response) {
    message.success('取消收藏成功')
    handleSearch()
  }
};

// 推荐
const handleRecommend = async (row) => {
  console.log('推荐:', row.name);
  const response = await FolderAPI.saveFolderPushes({ klCloudId: row.id })
  if (response.code === 200) {
    message.success('推荐成功')
    handleSearch()
  }
};

// 取消推荐
const handleCancelRecommend = async (row) => {
  console.log('取消推荐:', row.name);
  const response = await FolderAPI.deleteFolderPushes(row.id)
  if (response) {
    message.success('取消推荐成功')
    handleSearch()
  }
};
const shareListRef = ref()
// 共享文件
const handleShareFile = () => {
  shareListRef.value.open('my')
}

const handleMyShare = () => {
  shareListRef.value.open('other')
}

const handleEditNameConfirm = async (row) => {
  // /^[\u4e00-\u9fa5a-zA-Z0-9-]+$/
  if (!/^[\u4e00-\u9fa5a-zA-Z0-9-]+$/.test(editName.value)) {
    message.warning('名称只能包含中文、英文、数字、-')
    return
  }
  const fileName = row.fileName
  const lastDotIndex = fileName.lastIndexOf('.');
  let editType = lastDotIndex === -1 ? '' : '.' + fileName.substring(lastDotIndex + 1)
  if(row.type === 'folder') {
    editType = ''
  }
  const params = {
    id: row.id === '-1' ? null : editId.value,
    fileName: editName.value + editType,
    parentId: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].id : null,
    filePath: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].filePath + '/' + editName.value : null,
    isCommon: '0',
    datasetId: path.value[path.value.length - 1].datasetId,
    level: path.value[path.value.length - 1].id ? parseInt(path.value[path.value.length - 1].level) + 1 : '1'
  }
  if(row.filePath && !path.value[path.value.length - 1].id) {
    params.filePath = row.filePath.split('/')[0] + '/' + editName.value + editType
  }
  if (row.type !== 'folder') {
    params.filePrefix = '.docx'
    params.fileKey = uuidv4() + '-klCloud'
  }
  const msgTip = row.type === 'folder' ? '文件夹' : '文件';
  if (editName.value === '') {
    message.error(msgTip + '名称不能为空');
    return;
  }
  try {
    tableLoading.value = true;
    const response = row.id === '-1' ? await FolderAPI.createFolder(params) : await FolderAPI.updateFolder(params);
    if (response.code === 200) {
      editName.value = '';
      editId.value = '-1';
      message.success(row.id === '-1' ? msgTip + '新建成功' : '重命名成功');
      handleSearch()
      setQueryInterval()
    } else {
      message.error(response.msg);
    }
  } catch (error) {
    tableLoading.value = false;
    console.log('handleEditNameConfirm error:', error);
  }
}

const handleEditNameCancel = (row) => {
  if (row.id === '-1') {
    // 删除filelist 第一项
    fileList.value.shift();
  } else {
    editName.value = '';
    editId.value = '-1';
  }
  setQueryInterval()
};

const setQueryInterval = () => {
  timer.value = setInterval(() => {
    loadFolderContent(path.value[path.value.length - 1]);
  }, 10000);
}

// 返回上一级
const handleGoBack = () => {
  if (path.value.length > 1) {
    path.value.pop(); // 移除最后一项
    const currentFolderId = path.value[path.value.length - 1].id; // 获取上一级的文件夹 ID
    loadFolderContent(path.value[path.value.length - 1]); // 加载上一级文件夹内容
  }
};

// 解析设置
const handleParseSetting = (row) => {
  parseSettingsFormRef.value.open(row, 'public');
}
const parseTimers = ref(new Map())
// 清除所有定时器的方法
const clearAllTimers = () => {
  parseTimers.value.forEach((timer, id) => {
    clearInterval(timer);
    console.log(`已清除ID为 ${id} 的定时器`);
  });
  parseTimers.value.clear();
};

// 清除单个定时器的方法
const clearParseTimer = (id) => {
  if (parseTimers.value.has(id)) {
    clearInterval(parseTimers.value.get(id));
    parseTimers.value.delete(id);
  }
};

// 解析
const handleParse = async (row) => {
  console.log(row);
  if(!row.fileUrl) {
    message.warning('文件上传中，请稍候');
    return
  }
  if(row.analysisStatus === '1') {
    message.warning('文件解析中，请稍候');
    return
  }
	clearParseTimer(row.id);
  row.parseLoading = true;
  const params = {
    id: row.id,
    type: '1',
    documentId: row.documentId, 
    klCloudAnalysisId: row.klCloudAnalysisId ? row.klCloudAnalysisId : null
  }
  try {
    const analysResponse = await FolderAPI.getCloudAnalysisBackConfig(params);
    const { klCloudAnalysisId, documentId, datasetId, id } = analysResponse.data;
    row.analysisStatus = '1';
    const _timer = setInterval(async () => {
      const resultResponse= await FolderAPI.getCloudAnalysisBackResultsConfig({
        klCloudAnalysisId,
        datasetId,
        documentId,
        id,
				type: '1',
      })
      if (resultResponse.analysisStatus !== '1') {
        row.analysisStatus = resultResponse.analysisStatus
				row.parseLoading = false;
        clearParseTimer(row.id);
      }
    }, 20000)
		parseTimers.value.set(row.id, _timer);
  } catch {
		clearParseTimer(row.id);
		row.parseLoading = false;
    message.error('解析失败')
  } finally {
  }
};

const handleDocumentRefresh = async (row) => {
  tableLoading.value = true
  const response = await handleDocumentList(row.fileName)
  tableLoading.value = false
  message.success('刷新成功！！！')
  if (response.code === 0) {
    const documentData = response.data.docs
    const chunkRun = documentData[0]?.run
    if (chunkRun === 'DONE' || 'FAIL') {
      const params = {
        id: row.id,
        analysisStatus: chunkRun === 'DONE' ? '2' : '3',
      }
      const response = await FolderAPI.updateFolder(params)
      handleSearch()
    }
  }
}

const handleDocumentList = async (name) => {
  const response = await axios({
    url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_data}/documents`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `${apikeys.ragflow_token}`,
      Accept: '*/*'
    },
    params: { offset: 1, limit: 10, keywords: name }
  })
  const { data } = response
  return data
};

const handleDocumentId = async (id, documentId, method) => {
  // documentId
  const params = {
    id,
    documentId,
    analysisStatus: '1',
    klCloudAnalysisMethod: method
  }
  const response = await FolderAPI.updateFolder(params)
  handleSearch()
}

const handleRunDocumentByIds = async (id) => {
  try {
    const response = await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${apikeys.kl_cloud_data}/chunks`,
      method: 'post',
      headers: {
        Authorization: `${apikeys.ragflow_token}`,
      },
      data: {
        document_ids: [id]
      }
    })
  } finally {
  }
}

// 处理分享状态变化
const handleShareChange = async (row) => {
  console.log('分享状态变化:', row.name, row.shardStatus);
  try {
    // 修改状态的二次确认
    const text = row.shardStatus === '1' ? '分享' : '取消分享'
    await message.confirm('确认要' + text + '吗?')
    // 发起修改状态
    const response = row.shardStatus === '1' ? await FolderAPI.createCloudShare(row.id) : await FolderAPI.deleteCloudShare(row.id)
    console.log(response);

    // await FolderAPI.updateUserStatus(row.id, row.shardStatus)
    // 刷新列表
    handleSearch()
  } catch {
    // 取消后，进行恢复按钮
    row.shardStatus =
      row.shardStatus === '0' ? '1' : '0'
  }
  // 这里可以添加分享状态变化的逻辑
};

const handleShare = (row) => {
  console.log('分享:', row.name);
  // 这里可以添加分享逻辑
  shareDialogRef.value.open(row);
}

const computedStyle = computed(() => {
  if (path.value.length > 1) {
    return 'margin: 0 8px;';
  }
  return 'margin: 0 8px 0 0;';
});

const uploadFormRef = ref();
const uploadMultipleFormRef = ref();

const handleUploadFile = () => {
  console.log('上传文件');
  const nowPath = path.value[path.value.length - 1]
  if (nowPath.id) {
    uploadMultipleFormRef.value.open('0');
  } else {
    message.warning('根目录下不能上传文件！！！')
  }
};

// 下载文件
const handleDownload = async (row) => {
  console.log(row);
  const { fileUrl, fileName } = row
  if(!fileUrl) {
    message.warning('文件处理中， 请稍后')
    return
  }
  await downloadFileFunction(fileUrl, fileName)
  // window.open(row.fileUrl);
};

const imgViewRef = ref()
const handleView = (row) => {
  console.log('查看:', row.name, path.value[path.value.length - 1]);
  if (!row.fileUrl) {
    message.warning('文件处理中， 请稍后')
    return
  }
  const imgType = ['.jpg', '.jpeg', '.png',]
  const fileType = row.fileUrl?.substring(row.fileUrl.lastIndexOf('.')) || 'docx'
  if (imgType.includes(fileType)) {
    console.log(fileType);
    imgViewRef.value.open(row)
  } else {
    docViewRef.value.open('view', path.value[path.value.length - 1], row, true, '知识库');
  }
};

const handleEdit = (row) => {
  console.log('编辑:', row.name);
  // if (row.filePrefix !== '.docx') {
  //   message.warning('暂时仅支持在线编辑docx文件！！！')
  //   return
  // }
  if(!row.fileUrl) {
    message.warning('文件处理中， 请稍后')
    return
  }
  docViewRef.value.open('update', path.value[path.value.length - 1], row, true, '知识库');
}

const handleNewFile = () => {
  console.log('新建文件');
  docViewRef.value.open('create', path.value[path.value.length - 1], true, '知识库');
}

const handleMove = (row) => {
  console.log('移动:', row.name);
  moveDialogRef.value.open(row);
}

const reportViewRef = ref();
// 查看报告
const handleReportView = (row) => {
  console.log('查看报告:', row.name);
  reportViewRef.value.open(row);
}
const knowledgeDialogRef = ref()
// 搜索知识库
const handleSearchKnowledge = () => {
  // if (!queryParams.value.fileName) {
  //   message.warning('请输入搜索内容')
  //   return
  // }
  handleSearch();
  // knowledgeDialogRef.value.open(
  //   queryParams.value.fileName,
  //   {
  //     ragflow_url: apikeys.ragflow_url,
  //     ragflow_token: apikeys.ragflow_token,
  //     kl_cloud_data: apikeys.kl_cloud_data
  //   }
  // )
}

const timer = ref()
const knowledgeId = ref()
const isAdmin = ref(false)

// 初始化加载根目录
onMounted(() => {
	const { id, name, datasetId, isSuperAdmin } = route.query
  console.log('初始化加载根目录:', isSuperAdmin);
  const userInfo = userStore.getUser
  console.log(userInfo);
  
  isAdmin.value = isSuperAdmin === 'true'
  knowledgeId.value = id
	path.value.push({
		id: '',
    datasetId,
    knowledgeId: id,
		fileName: name,
    level: '1'
	})
  handleSearch();
  setQueryInterval()
});

onUnmounted(() => {
  clearInterval(timer.value)
  clearAllTimers();
});

const assignPermissionDialogRef = ref()
// 分配权限
const handleAssignPermission = (row, type) => {
  console.log('分配权限:', type);
  assignPermissionDialogRef.value.open(row, type);
}
</script>

<style scoped lang="scss">
.align-center {
  align-items: center;
}

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.breadcrumb-wrapper {
  display: flex;
  height: 48px;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 10px;

  .back-button {
    padding: 0;
    font-size: 14px;
  }
}

.name-column {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .name-wrapper {
    width: 600px;
  }

  .name-text {
    cursor: pointer;
    align-items: center;
    display: flex;

    &:hover {
      color: #409eff;
    }
  }

  .more-actions {
    cursor: pointer;
    color: #666;
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-left: 12px;

    &:hover {
      color: #409eff;
    }
  }
}

.button-group {
  flex: 1;
  display: flex;
  align-items: center;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 420px;
  display: inline-block;
}
</style>