<template>
    <el-container class="ai-layout">
      <!-- 左侧：对话列表 -->
      <!-- <ConversationList 
        :active-id="String(activeConversationId ?? '')" 
        ref="conversationListRef"
        v-show="false"
        @on-conversation-create="handleConversationCreateSuccess" 
        @on-conversation-click="handleConversationClick"
        @on-conversation-clear="handleConversationClear" 
        @on-conversation-delete="handlerConversationDelete" /> -->
      <!-- 右侧：对话详情 -->
      <el-container class="detail-container">
        <el-header class="header">
          <div class="title">
            {{ '知识检索' }}
            <!-- {{ activeConversation?.title ? activeConversation?.title : '对话' }} -->
            <span v-if="activeMessageList.length">({{ activeMessageList.length }})</span>
          </div>
          <div class="btns" v-if="activeConversation">
            <el-button type="primary" bg plain size="small" @click="openChatConversationUpdateForm">
              <span>{{ modelConfig.modelName }}</span>
              <Icon icon="ep:setting" class="ml-10px" />
            </el-button>
            <el-button size="small" class="btn" @click="handlerMessageClear">
              <Icon icon="heroicons-outline:archive-box-x-mark" color="#787878" />
            </el-button>
            <el-button size="small" class="btn" @click="handleGoBottomMessage">
              <Icon icon="ep:download" color="#787878" />
            </el-button>
            <el-button size="small" class="btn" @click="handleGoTopMessage">
              <Icon icon="ep:top" color="#787878" />
            </el-button>
          </div>
        </el-header>
  
        <!-- main：消息列表 -->
        <el-main class="main-container">
          <div>
  
            <div class="message-container">
              <!-- 情况一：消息加载中 -->
              <MessageLoading v-if="activeMessageListLoading" />
              <!-- 情况二：无聊天对话时 -->
              <MessageNewConversation v-if="!activeConversation" @on-new-conversation="handleConversationCreate" />
              <!-- 情况三：消息列表为空 -->
              <MessageListEmpty 
                v-if="!activeMessageListLoading && messageList.length === 0 && activeConversation"
                @on-prompt="doSendMessage" />
              <!-- 情况四：消息列表不为空 -->
              <MessageList 
                v-if="!activeMessageListLoading && messageList.length > 0" 
                ref="messageRef"
                :conversation="activeConversation" 
                :list="messageList" 
                @on-delete-success="handleMessageDelete"
                @on-edit="handleMessageEdit" 
                @on-refresh="handleMessageRefresh" />
            </div>
          </div>
        </el-main>
  
        <!-- 底部 -->
        <el-footer class="footer-container">
          <!-- 已选择的文件列表 -->
          <div v-if="fileData.length && showFileList" class="selected-files">
            <div class="file-header">
              <span class="file-title">已选择文件</span>
              <el-button link type="danger" size="small" @click="handleClearFiles">
                <Icon icon="ep:delete" class="mr-5px" />清空
              </el-button>
            </div>
            <div class="file-list">
              <div v-for="(file, index) in fileData" :key="index" class="file-item">
                <el-tag class="file-tag" closable @close="handleRemoveFile(index)">
                  {{ file.fileName }}
                </el-tag>
              </div>
            </div>
          </div>
  
          <form class="prompt-from">
            <textarea 
              class="prompt-input" 
              v-model="prompt" 
              @keydown="handleSendByKeydown" 
              @input="handlePromptInput"
              @compositionstart="onCompositionstart" 
              @compositionend="onCompositionend"
              placeholder="问我任何问题...（Shift+Enter 换行，按下 Enter 发送）"></textarea>
            <div class="prompt-btns">
              <div class="left-section">
                <!-- <el-radio-group v-model="configType" @change="handleConfigTypeChange" size="small">
                  <el-radio-button @click="handleChatTypeClick(1)" label="模型" value="1" />
                  <el-radio-button @click="handleChatTypeClick(2)" label="RAG" value="2" />
                </el-radio-group> -->
                <el-button-group>
                  <el-button :type="modelConfig.configType == '1' ? 'primary' : 'default'" size="default" @click="handleChatTypeClick('1')">
                    智能体
                  </el-button>
                  <el-button :type="modelConfig.configType == '2' ? 'primary' : 'default'" size="default" @click="handleChatTypeClick('2')">
                    知识库
                  </el-button>
                </el-button-group>
              </div>
              <div class="right-section">
                <el-button type="primary" size="default" @click="handleChooseTips">
                  <Icon icon="ep:info-filled" />
                </el-button>
                <el-button type="primary" size="default" @click="handleChooseFile" :loading="chooseFileLoading">
                    <Icon icon="fa:cloud-upload" />
                </el-button>
                <el-button 
                    :type="prompt ? 'primary' : 'info'" 
                    size="default" 
                    @click="handleSendByButton" 
                    :loading="conversationInProgress"
                    v-if="conversationInProgress == false">
                    <!-- <Icon icon="ep:video-play" /> -->
                     <Icon icon="fa:arrow-circle-o-right" />
                </el-button>
                <el-button type="danger" size="default" @click="stopStream()" v-if="conversationInProgress == true">
                    <Icon icon="fa:stop-circle" />
                </el-button>
              </div>
            </div>
          </form>
        </el-footer>
      </el-container>
  
      <!-- 更新对话 Form -->
      <ConversationUpdateForm 
        ref="conversationUpdateFormRef" 
        @success="handleConversationUpdateSuccess"
        @cancel="handleConversationUpdateCancel"
        />
      <ChooseFile ref="chooseFileRef" @success="getChooseList" :temporaryKbId="temporaryKbId" :token="token" />
      <ChooseTips ref="chooseTipsRef" @success="chooseTipsSuccess"/>
    </el-container>
  </template>
  
  <script setup lang="ts">
  import { ChatMessageApi, ChatMessageVO } from '@/api/ai/chat/message'
  import { ChatConversationApi, ChatConversationVO } from '@/api/ai/chat/conversation'
  import ConversationList from './components/conversation/ConversationListHome.vue'
  import ConversationUpdateForm from './components/conversation/ConversationUpdateForm.vue'
  import MessageList from './components/message/MessageList.vue'
  import MessageListEmpty from './components/message/MessageListEmpty.vue'
  import MessageLoading from './components/message/MessageLoading.vue'
  import MessageNewConversation from './components/message/MessageNewConversation.vue'
  import { 
    sendMessage, 
    getKlSessionMessage, 
    getKlMessageConfig, 
    deleteKlSessionMessageBySessionId,
    saveKlMessageConfig,
    deleteKlSessionMessage
   } from '@/api/knowledge/model'
  import ChooseFile from './components/ChooseFile/index.vue'
  import { getArrangeModelPage } from '@/api/knowledge/aiSettings/model'
	import { getKlModelSession, saveKlModelSession, saveKlSessionMessage, deleteKlModelSession } from '@/api/knowledge/model'
  import { getklklArrangeChatPage } from '@/api/knowledge/aiSettings/knowledge'
  import ChooseTips from './components/ChooseFile/ChooseTips.vue'
  import { useUserStore } from '@/store/modules/user'
  import axios from 'axios'
  import { getCloudUser } from '@/api/knowledge/docmangement'
  /** AI 聊天对话 列表 */
  defineOptions({ name: 'AiChat' })
  
  const route = useRoute() // 路由
  const message = useMessage() // 消息弹窗
  
  const chooseFileRef = ref();
  
  // 聊天对话
  const conversationListRef = ref()
  const activeConversationId = ref<number | null>(null) // 选中的对话编号
  const activeConversation = ref<ChatConversationVO | null>(null) // 选中的 Conversation
  const conversationInProgress = ref(false) // 对话是否正在进行中。目前只有【发送】消息时，会更新为 true，避免切换对话、删除对话等操作
  
  // 消息列表
  const messageRef = ref()
  const activeMessageList = ref<KlMessage[]>([]) // 选中对话的消息列表
  const activeMessageListLoading = ref<boolean>(false) // activeMessageList 是否正在加载中
  const activeMessageListLoadingTimer = ref<any>() // activeMessageListLoading Timer 定时器。如果加载速度很快，就不进入加载中
  // 消息滚动
  const textSpeed = ref<number>(50) // Typing speed in milliseconds
  const textRoleRunning = ref<boolean>(false) // Typing speed in milliseconds
  
  // 发送消息输入框
  const isComposing = ref(false) // 判断用户是否在输入
  const conversationInAbortController = ref<any>() // 对话进行中 abort 控制器(控制 stream 对话)
  const inputTimeout = ref<any>() // 处理输入中回车的定时器
  const prompt = ref<string>() // prompt
  const enableContext = ref<boolean>(true) // 是否开启上下文
  // 接收 Stream 消息
  const receiveMessageFullText = ref('')
  const receiveMessageDisplayedText = ref('')
  const fileData = ref([]);
  const temporaryKbId = ref('');
  const token = ref('');
  
  const userStore = useUserStore()
  const apikeys = userStore.getAppConfig
  
  interface ModelConfig {
    configType: string |'1' | '2'  // 1: 模型, 2: RAG
    configId: string
    role: string
    modelName: string
    tokenNum: number
    contextNum: number
  }
  
  // 修改 modelConfig 的定义，添加类型
  const modelConfig = ref<ModelConfig>({
    configType: '1',
    configId: '',
    role: '',
    modelName: '',
    tokenNum: 1,
    contextNum: 1,
  })
  
  // 在 script setup 的开头添加类型定义
  interface KlMessage {
    id: number
    conversationId: number | null
    type: string
    userId: string
    roleId: string
    model: number
    modelId: number
    content: string
    tokens: number
    createTime: Date
    roleAvatar: string
    userAvatar: string
    inquiryContent: string
    answerContent: string
  }
  
  // =========== 【聊天对话】相关 ===========
  
  /** 获取对话信息 */
  // const getConversation = async (id: number | null) => {
  //   if (!id) {
  //     return
  //   }
  //   const conversation: ChatConversationVO = await ChatConversationApi.getChatConversationMy(id)
  //   if (!conversation) {
  //     return
  //   }
  //   activeConversation.value = conversation
  //   activeConversationId.value = conversation.id
  // }
  
  const configType = ref('1');
  
  /**
   * 点击某个对话
   *
   * @param conversation 选中的对话
   * @return 是否切换成功
   */
  const handleConversationClick = async (conversation: ChatConversationVO) => {
    // 对话进行中，不允许切换
    if (conversationInProgress.value) {
      message.alert('对话中，不允许切换!')
      return false
    }
    // 更新选中的对话 id
    activeConversationId.value = conversation.id
    activeConversation.value = conversation
    // 刷新 message 列表
    await getMessageList()
    // 滚动底部
    scrollToBottom(true)
    // 清空输入框
    prompt.value = ''
    return true
  }
  
  /** 删除某个对话*/
  const handlerConversationDelete = async (delConversation: ChatConversationVO) => {
    // 删除的对话如果是当前选中的，那么就重置
    if (activeConversationId.value === delConversation.id) {
      await handleConversationClear()
    }
  }
  /** 清空选中的对话 */
  const handleConversationClear = async () => {
    // 对话进行中，不允许切换
    if (conversationInProgress.value) {
      message.alert('对话中，不允许切换!')
      return false
    }
    activeConversationId.value = null
    activeConversation.value = null
    activeMessageList.value = []
  }
  
  /** 修改聊天对话 */
  const conversationUpdateFormRef = ref()
  const openChatConversationUpdateForm = async () => {
    
    conversationUpdateFormRef.value.open(modelConfig.value, modelList.value, ragList.value)
  }
  const handleConversationUpdateSuccess = async () => {
    const configRes = await getKlMessageConfig()
    const { configType, configId, role, modelName,tokenNum = 1, contextNum = 1  } = configRes[0]
    modelConfig.value = {
      configType,
      configId: String(configId),
      role,
      modelName,
      tokenNum,
      contextNum
    }
  }
  
  const handleConversationUpdateCancel = async () => {
    configType.value = modelConfig.value.configType
  }
  
  /** 处理聊天对话的创建成功 */
  const handleConversationCreate = async () => {
    // 创建对话
    await conversationListRef.value.createConversation()
  }
  /** 处理聊天对话的创建成功 */
  const handleConversationCreateSuccess = async () => {
    // 创建新的对话，清空输入框
    prompt.value = ''
  }
  
  // =========== 【消息列表】相关 ===========
  
  /** 获取消息 message 列表 */
  const getMessageList = async () => {
    try {
      if (activeConversationId.value === null) {
        return
      }
      // Timer 定时器，如果加载速度很快，就不进入加载中
      activeMessageListLoadingTimer.value = setTimeout(() => {
        activeMessageListLoading.value = true
      }, 60)
      const data = await getKlSessionMessage({
        klModelSessionId: activeConversationId.value,
        pageNum: 1,
        pageSize: 1000
      })
  
      const configRes = await getKlMessageConfig()
      const { configType, configId, role, modelName, tokenNum = 1, contextNum = 1 } = configRes[0]
      modelConfig.value = {
        configType,
        configId: String(configId),
        role,
        modelName,
        tokenNum,
        contextNum
      }
      // 获取消息列表并转换格式
      activeMessageList.value = data.map(item => ({
        id: item.id,
        conversationId: activeConversationId.value,
        type: item.type,
        userId: item.userId || '',
        roleId: item.roleId || '',
        model: item.model || 0,
        modelId: item.modelId || 0,
        content: item.type === 'user' ? item.inquiryContent : item.answerContent,
        tokens: item.tokens || 0,
        createTime: item.createTime,
        roleAvatar: item.roleAvatar || '',
        userAvatar: item.userAvatar || '',
        inquiryContent: item.inquiryContent || '',
        answerContent: item.answerContent || '',
        file: item.file || []
      }))
  
      // 滚动到最下面
      await nextTick()
      await scrollToBottom()
    } finally {
      // time 定时器，如果加载速度很快，就不进入加载中
      if (activeMessageListLoadingTimer.value) {
        clearTimeout(activeMessageListLoadingTimer.value)
      }
      // 加载结束
      activeMessageListLoading.value = false
    }
  }
  
  /**
   * 消息列表
   *
   * 和 {@link #getMessageList()} 的差异是，把 systemMessage 考虑进去
   */
  const messageList = computed(() => {
    if (activeMessageList.value.length > 0) {
      return activeMessageList.value
    }
    // 没有消息时，如果有 systemMessage 则展示它
    if (activeConversation.value?.systemMessage) {
      return [
        {
          id: 0,
          type: 'system',
          content: activeConversation.value.systemMessage
        }
      ]
    }
    return []
  })
  
  /** 处理删除 message 消息 */
  const handleMessageDelete = async(id: any) => {
    console.log('ssss');
  
    if (conversationInProgress.value) {
      message.warning('回答中，不能删除!')
      return
    }
    await deleteKlSessionMessage(id)
    message.success('删除成功！')
    // 刷新 message 列表
    getMessageList()
  }
  
  /** 处理 message 清空 */
  const handlerMessageClear = async () => {
    if (!activeConversationId.value) {
      return
    }
    try {
      // 确认提示
      await message.delConfirm('确认清空对话消息？')
      // 清空对话
      await deleteKlSessionMessageBySessionId(activeConversationId.value)
      // 刷新 message 列表
      activeMessageList.value = []
    } catch { }
  }
  
  /** 回到 message 列表的顶部 */
  const handleGoTopMessage = () => {
    messageRef.value.handlerGoTop()
  }
  
  /** 滚动到 message 列表的底部 */
  const handleGoBottomMessage = () => {
    messageRef.value.handleGoBottom()
  }
  
  // =========== 【发送消息】相关 ===========
  
  /** 处理来自 keydown 的发送消息 */
  const handleSendByKeydown = async (event) => {
    // 判断用户是否在输入
    if (isComposing.value) {
      return
    }
    const content = prompt.value?.trim() as string
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // 插入换行
        prompt.value += '\r\n'
        event.preventDefault() // 防止默认的换行行为
      } else {
        if (conversationInProgress.value) {
          event.preventDefault()
          message.warning('对话中，请稍后操作或点击停止回答!')
          return
        }
        // 发送消息
        await doSendMessage(content)
        event.preventDefault() // 防止默认的提交行为
      }
    }
  }
  
  /** 处理来自【发送】按钮的发送消息 */
  const handleSendByButton = () => {
    doSendMessage(prompt.value?.trim() as string)
  }
  
  /** 处理 prompt 输入变化 */
  const handlePromptInput = (event) => {
    // 非输入法 输入设置为 true
    if (!isComposing.value) {
      // 回车 event data 是 null
      if (event.data == null) {
        return
      }
      isComposing.value = true
    }
    // 清理定时器
    if (inputTimeout.value) {
      clearTimeout(inputTimeout.value)
    }
    // 重置定时器
    inputTimeout.value = setTimeout(() => {
      isComposing.value = false
    }, 400)
  }
  // TODO @芋艿：是不是可以通过 @keydown.enter、@keydown.shift.enter 来实现，回车发送、shift+回车换行；主要看看，是不是可以简化 isComposing 相关的逻辑
  const onCompositionstart = () => {
    isComposing.value = true
  }
  const onCompositionend = () => {
    // console.log('输入结束...')
    setTimeout(() => {
      isComposing.value = false
    }, 200)
  }
  
  // 添加一个控制文件列表显示的变量
  const showFileList = ref(true)
  
  /** 真正执行【发送】消息操作 */
  const doSendMessage = async (content: string) => {
    if (content.length < 1) {
      message.error('发送失败，原因：内容为空！')
      return
    }
    if (activeConversationId.value == null) {
      message.error('还没创建对话，不能发送!')
      return
    }
    if ( conversationInProgress.value) {
      message.warning('对话中，请稍后操作或点击停止回答!')
      return
    }
    try {
      // 清空输入框
      prompt.value = ''
      // 先隐藏文件列表
      showFileList.value = false
      // 执行发送
      await doSendMessageStream({
        conversationId: activeConversationId.value,
        content: content
      } as ChatMessageVO)
    } finally {
      // 清空输入框和文件列表
      prompt.value = ''
      fileData.value = []
      showFileList.value = true
      conversationInProgress.value = false
    }
  }
  
  /** 真正执行【发送】消息操作 */
  const doSendMessageStream = async (userMessage: ChatMessageVO) => {
    console.log('===');
  
    // 创建 AbortController 实例，以便中止请求
    conversationInAbortController.value = new AbortController()
    // 标记对话进行中
    conversationInProgress.value = true
    // 设置为空
    receiveMessageFullText.value = ''
  
    try {
      // 1.1 先添加两个假数据，等 stream 返回再替换
      activeMessageList.value.push({
        // id: -1,
        conversationId: activeConversationId.value,
        type: 'user',
        content: userMessage.content,
        createTime: new Date(),
        file: fileData.value
      } as unknown as ChatMessageVO)
      activeMessageList.value.push({
        // id: -2,
        conversationId: activeConversationId.value,
        type: 'assistant',
        content: '思考中<span class="chat-dots"><span class="dot">.</span><span class="dot">.</span><span class="dot">.</span></span>',
        createTime: new Date()
      } as ChatMessageVO)
      // 1.2 滚动到最下面
      await nextTick()
      await scrollToBottom() // 底部
      // 1.3 开始滚动
      textRoll()
  
      // 2. 发送 event stream
      let isFirstChunk = true // 是否是第一个 chunk 消息段
      await sendMessage(
        {
          "klModelSessionId": activeConversationId.value,
          "inquiryContent": userMessage.content,
          "configType": modelConfig.value.configType,
          "configId": modelConfig.value.configId,
          "role": modelConfig.value.role,
          "configName": modelConfig.value.modelName,
          "tokenNum": modelConfig.value.tokenNum,
          "contextNum": modelConfig.value.contextNum,
          "file": fileData.value
        },
        conversationInAbortController.value,
        async (res) => {
          const { code, data, msg } = JSON.parse(res.data)
          console.log('data===', data);
  
          if (code !== 200) {
            message.alert(`对话异常! ${msg}`)
            return
          }
  
          // 如果内容为空，就不处理。
          if (!data.answerContent) {
            return
          }
          // 首次返回需要添加一个 message 到页面，后面的都是更新
          if (isFirstChunk) {
            isFirstChunk = false
            // 弹出两个假数据
            activeMessageList.value.pop()
            activeMessageList.value.pop()
  
            // 更新返回的数据
            activeMessageList.value.push({
              id: -1,
              conversationId: activeConversationId.value,
              type: 'user',
              content: data.inquiryContent,
              file: data.file,
              createTime: new Date()
            } as unknown as KlMessage)
            activeMessageList.value.push({
              id: -2,
              conversationId: activeConversationId.value,
              type: 'assistant',
              content: data.answerContent,
              createTime: new Date()
            } as KlMessage)
          }
          // debugger
          receiveMessageFullText.value = receiveMessageFullText.value + data.answerContent
          console.log(receiveMessageFullText.value);
  
          // 滚动到最下面
          await scrollToBottom()
        },
        (error) => {
          // message.alert(`对话异常! ${error}`)
          activeMessageList.value.pop()
          activeMessageList.value.push({
            id: -3,
            conversationId: activeConversationId.value,
            type: 'assistant',
            content: '系统超时请重试！',
            createTime: new Date()
          } as ChatMessageVO)
          stopStream()
          throw Error(error)
        },
        () => {
          stopStream()
        }
      )
    } catch (error) {
      console.log(error);
  
    }
  }
  
  /** 停止 stream 流式调用 */
  const stopStream = async () => {
    // tip：如果 stream 进行中的 message，就需要调用 controller 结束
    if (conversationInAbortController.value) {
      conversationInAbortController.value.abort()
    }
    // 设置为 false
    conversationInProgress.value = false
  }
  
  /** 编辑 message：设置为 prompt，可以再次编辑 */
  const handleMessageEdit = (message: ChatMessageVO) => {
    prompt.value = message.content
  }
  
  /** 刷新 message：基于指定消息，再次发起对话 */
  const handleMessageRefresh = (messages: ChatMessageVO) => {
    if(conversationInProgress.value) {
      message.warning('对话进行中，请稍后重试！')
      return
    }
    doSendMessage(messages.content)
  }
  
  // ============== 【消息滚动】相关 =============
  
  /** 滚动到 message 底部 */
  const scrollToBottom = async (isIgnore?: boolean) => {
    await nextTick()
    if (messageRef.value) {
      messageRef.value.scrollToBottom(isIgnore)
    }
  }
  
  /** 自提滚动效果 */
  const textRoll = async () => {
    let index = 0
    try {
      // 只能执行一次
      if (textRoleRunning.value) {
        return
      }
      // 设置状态
      textRoleRunning.value = true
      receiveMessageDisplayedText.value = ''
      const task = async () => {
        // 调整速度
        const diff =
          (receiveMessageFullText.value.length - receiveMessageDisplayedText.value.length) / 10
        if (diff > 5) {
          textSpeed.value = 10
        } else if (diff > 2) {
          textSpeed.value = 30
        } else if (diff > 1.5) {
          textSpeed.value = 50
        } else {
          textSpeed.value = 100
        }
        // 对话结束，就按 30 的速度
        if (!conversationInProgress.value) {
          textSpeed.value = 10
        }
  
        if (index < receiveMessageFullText.value.length) {
          receiveMessageDisplayedText.value += receiveMessageFullText.value[index]
          index++
  
          // 更新 message
          const lastMessage = activeMessageList.value[activeMessageList.value.length - 1]
          lastMessage.content = receiveMessageDisplayedText.value
          // 滚动到住下面
          await scrollToBottom()
          // 重新设置任务
          timer = setTimeout(task, textSpeed.value)
        } else {
          // 不是对话中可以结束
          if (!conversationInProgress.value) {
            textRoleRunning.value = false
            clearTimeout(timer)
          } else {
            // 重新设置任务
            timer = setTimeout(task, textSpeed.value)
          }
        }
      }
      let timer = setTimeout(task, textSpeed.value)
    } catch { }
  }
  
  /** 初始化 **/
  onMounted(async () => {
    // 获取列表数据
    // activeMessageListLoading.value = true
    // const response = await getCloudUser()
    // temporaryKbId.value = response.temporaryKbId;
    // token.value = response.apitoken;
    // await getMessageList()
    // await getModelList()
  })

  const emit = defineEmits(['success'])

  // 首页进入
	const handleChatToHome = async (chat : any, chatItem: any) => {
    await getModelList()
    if(chatItem.id) {
      activeConversationId.value = chatItem.id
      activeConversation.value = chatItem
    } else {
      const conversationId = await saveKlModelSession(
        { name: '新建对话', isTemporary: true }
      )
      console.log(chat);
      activeConversationId.value = conversationId.id
      activeConversation.value = conversationId
      // 写入对话
      emit('success', conversationId)
      const model = modelList.value && modelList.value[0]
      // answer  question
      const params = {
        klModelSessionId: conversationId.id,
        inquiryContent: chat.question,
        answerContent: chat.answer,
        configType: 1,
        configId: model.id,
        configName: model.modelName,
      }
      const response = await saveKlSessionMessage(params)
      console.log(response);
    }
		
		await getMessageList()
  }

	defineExpose({ handleChatToHome })
  
  const modelList = ref([])
  const ragList = ref([])
  // 获取模型列表
  const getModelList = async () => {
    const { records: modelRecords } = await getArrangeModelPage({
      pageNum: 1,
      status: '0',
      pageSize: 1000
    })
    modelList.value = modelRecords
  
    // 获取RAG列表
    const { records: ragRecords } = await getklklArrangeChatPage({
      pageNum: 1,
      pageSize: 1000
    })
    ragList.value = ragRecords
  }
  
  // 添加 loading 状态
  const chooseFileLoading = ref(false)
  
  // 修改选择文件的处理方法
  const handleChooseFile = () => {
    // 传递当前已选择的文件列表到弹窗组件
    chooseFileRef.value.open(fileData.value)
  }
  
  // 修改获取选择列表的方法
  const getChooseList = async (fileObj) => {
    const { fileList } = fileObj;
    console.log('fileList', fileList);
    chooseFileLoading.value = true
    try {
      const promiseList = fileList.map(async (item) => {
        const content = await getDocumentContent(item.documentId, item.apiKey, item.Authorization)
        return {
          ...item,
          content
        }
      })
      const result = await Promise.all(promiseList)
  
      const selectedFile = result.map(item => {
        return {
          "fileName": item.fileName,
          "fileUrl": item.fileUrl,
          "fileId": item.documentId,
          "arrangeBaseId": item.apiKey,
          "fileContent": item.content,
          "id": item.id,
        }
      })
  
      // 检查重复文件
      const newFiles = selectedFile.filter(newFile => {
        return !fileData.value.some(existingFile =>
          existingFile.fileId === newFile.fileId &&
          existingFile.arrangeBaseId === newFile.arrangeBaseId
        )
      })
  
      if (newFiles.length !== selectedFile.length) {
        message.warning('文件重复')
      }
  
      fileData.value = [...fileData.value, ...newFiles]
    } catch (error) {
      console.error('获取文件内容失败:', error)
      message.error('获取文件内容失败')
    } finally {
      chooseFileLoading.value = false
    }
  }
  
  // 获取文档内容
  const getDocumentContent = async (documentId, key, Authorization) => {
    try {
      const response = await axios({
        url: `${apikeys.ragflow_url}/api/v1/datasets/${key}/documents/${documentId}/chunks`,
        method: 'get',
        headers: {
          'Content-Type': 'application/json',
          Authorization: Authorization,
          Accept: '*/*'
        },
      })
      const { data } = response.data
      const { chunks } = data || {}
      let str = '';
      (chunks || []).forEach(item => {
        str += item.content
      })
      return str
    } catch (error) {
      console.log(error);
      return ''
    }
  }
  
  /** 移除选中的文件 */
  const handleRemoveFile = (index: number) => {
    fileData.value.splice(index, 1)
  }
  
  /** 清空所有文件 */
  const handleClearFiles = () => {
    fileData.value = []
  }
  
  // const handleConfigTypeChange = async(type: number) => {
  //   conversationUpdateFormRef.value.open(modelConfig.value, type)
  // }
  const handleChatTypeClick = async(type: string) => {
    // conversationUpdateFormRef.value.open(modelConfig.value, type, modelList.value, ragList.value)
    const selectedItem = type === '1' ? modelList.value[0] : ragList.value[0]
    const data = {
      ...modelConfig.value,
      configType: type,
      configId: selectedItem?.id,
      modelName: type === '1' ? selectedItem?.modelName : selectedItem?.chatName
    }
    await saveKlMessageConfig(data)
    message.success('对话配置已更新')
    await handleConversationUpdateSuccess()
  }
  const chooseTipsRef = ref()
  const handleChooseTips = () => {
    chooseTipsRef.value && chooseTipsRef.value.open()
  }
  
  const chooseTipsSuccess = async(data: string) => {
    prompt.value = data
  }
  </script>
  
  <style lang="scss" scoped>
  .ai-layout {
    position: absolute;
    flex: 1;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }
  
  .conversation-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 10px 0;
  
    .btn-new-conversation {
      padding: 18px 0;
    }
  
    .search-input {
      margin-top: 20px;
    }
  
    .conversation-list {
      margin-top: 20px;
  
      .conversation {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        flex: 1;
        padding: 0 5px;
        margin-top: 10px;
        cursor: pointer;
        border-radius: 5px;
        align-items: center;
        line-height: 30px;
  
        &.active {
          background-color: #e6e6e6;
  
          .button {
            display: inline-block;
          }
        }
  
        .title-wrapper {
          display: flex;
          flex-direction: row;
          align-items: center;
        }
  
        .title {
          padding: 5px 10px;
          max-width: 220px;
          font-size: 14px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
  
        .avatar {
          width: 28px;
          height: 28px;
          display: flex;
          flex-direction: row;
          justify-items: center;
        }
  
        // 对话编辑、删除
        .button-wrapper {
          right: 2px;
          display: flex;
          flex-direction: row;
          justify-items: center;
          color: #606266;
  
          .el-icon {
            margin-right: 5px;
          }
        }
      }
    }
  
    // 角色仓库、清空未设置对话
    .tool-box {
      line-height: 35px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--el-text-color);
  
      >div {
        display: flex;
        align-items: center;
        color: #606266;
        padding: 0;
        margin: 0;
        cursor: pointer;
  
        >span {
          margin-left: 5px;
        }
      }
    }
  }
  
  // 头部
  .detail-container {
    background: #ffffff;
  
    .header {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      background: #fbfbfb;
      box-shadow: 0 0 0 0 #dcdfe6;
  
      .title {
        font-size: 18px;
        font-weight: bold;
      }
  
      .btns {
        display: flex;
        width: 300px;
        flex-direction: row;
        justify-content: flex-end;
        //justify-content: space-between;
  
        .btn {
          padding: 10px;
        }
      }
    }
  }
  
  // main 容器
  .main-container {
    margin: 0;
    padding: 0;
    position: relative;
    height: 100%;
    width: 100%;
  
    .message-container {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      overflow-y: hidden;
      padding: 0;
      margin: 0;
    }
  }
  
  // 底部
  .footer-container {
    display: flex;
    flex-direction: column;
    height: auto;
    margin: 0;
    padding: 0;
  
    .selected-files {
      padding: 10px 20px 0 20px;
  
      .file-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
  
        .file-title {
          font-size: 14px;
          color: #606266;
        }
      }
  
      .file-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
  
        .file-item {
          display: flex;
          align-items: center;
          gap: 4px;
  
          .file-tag {
            // max-width: 200px;
            // overflow: hidden;
            // text-overflow: ellipsis;
            // white-space: nowrap;
          }
  
          .delete-icon {
            cursor: pointer;
            font-size: 16px;
            opacity: 0.8;
            transition: all 0.2s;
  
            &:hover {
              opacity: 1;
              transform: scale(1.1);
            }
          }
        }
      }
    }
  
    .prompt-from {
      display: flex;
      flex-direction: column;
      height: auto;
      border: 1px solid #e3e3e3;
      border-radius: 10px;
      margin: 10px 20px 20px 20px;
      padding: 9px 10px;
    }
  
    .prompt-input {
      height: 80px;
      //box-shadow: none;
      border: none;
      box-sizing: border-box;
      resize: none;
      padding: 0 2px;
      overflow: auto;
    }
  
    .prompt-input:focus {
      outline: none;
    }
  
    .prompt-btns {
      display: flex;
      justify-content: space-between; /* 左右分开对齐 */
      align-items: center; /* 垂直居中 */
      padding-bottom: 0;
      padding-top: 5px;
    }
  
    /* 可选：为右侧按钮组添加间距 */
    .right-section {
      display: flex;
      gap: 8px; /* 按钮之间的间距 */
    }
  }
  </style>
  