<template>
	<base-knowledge-setting
		:isPermission="true"
		@click-knowledge="handleClickKnowledge"
		:getData="getData"
	/>
</template>

<script setup>
import { getKlDatasetAdmin } from '@/api/knowledge/docmangement/index.js';
import BaseKnowledgeSetting from './components/BaseKnowledgeSetting.vue';

const router = useRouter() // 路由
const handleClickKnowledge = (item) => {
	router.push({
		path: '/knowledge/management',
		query: {
			datasetId: item.datasetId,
			id: item.id,
			name: item.name,
			isSuperAdmin: item.isSuperAdmin
		}
	})
};

const getData = async () => {
	const res = await getKlDatasetAdmin();
	console.log(res);
	return res;
};

</script>

<style scoped lang="scss">
</style>