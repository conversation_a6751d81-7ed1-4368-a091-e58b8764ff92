import request from '@/config/axios'

// 
export const getArrangeModelPage = (params) => {
  return request.get({ url: '/v1/kownledge/klArrangeModel/page', params })
}
// 
export const addArrangeModel = (data) => {
  return request.post({ url: '/v1/kownledge/klArrangeModel', data })
}

// 
export const updateArrangeModel = (data) => {
  return request.put({ url: '/v1/kownledge/klArrangeModel', data })
}

// 
export const deleteArrangeModel = (id) => {
  return request.delete({ url: `/v1/kownledge/klArrangeModel/${id}` })
}

// 
export const getArrangeModel = (id) => {
  return request.get({ url: `/v1/kownledge/klArrangeModel/${id}` })
}
