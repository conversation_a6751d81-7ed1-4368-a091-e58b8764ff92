<template>
    <div class="inline-tag-input">
      <div class="tag-input-container" @click="focusInput">
        <!-- 已选择的标签显示在输入框内 -->
        <span v-for="(tag, index) in modelValue" :key="index" class="input-tag">
          {{ tag }}
          <span class="tag-remove" @click.stop="removeTag(index)">×</span>
        </span>
        
        <!-- 实际输入框 -->
        <input
          ref="inputRef"
          v-model="inputValue"
          type="text"
          class="tag-input-field"
          :placeholder="placeholder"
          @keydown.enter="addTag"
          @keydown.backspace="handleBackspace"
          @blur="addTagOnBlur"
        />
      </div>
      
      <!-- 默认标签选择区域 -->
      <div v-if="showDefaultTags && defaultTags.length > 0" class="default-tags">
        <span class="default-tags-title">常用标签:</span>
        <el-tag
          v-for="(tag, index) in defaultTags"
          :key="'default-' + index"
          type="info"
          class="default-tag"
          @click="selectDefaultTag(tag)"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  
  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => []
    },
    defaultTags: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '输入后按回车'
    },
    showDefaultTags: {
      type: Boolean,
      default: true
    },
    allowDuplicates: {
      type: Boolean,
      default: false
    }
  })
  
  const emit = defineEmits(['update:modelValue'])
  
  const inputValue = ref('')
  const inputRef = ref(null)
  
  // 添加标签
  const addTag = () => {
    if (inputValue.value.trim()) {
      if (props.allowDuplicates || !props.modelValue.includes(inputValue.value.trim())) {
        const newTags = [...props.modelValue, inputValue.value.trim()]
        emit('update:modelValue', newTags)
        inputValue.value = ''
      }
    }
  }
  
  // 失去焦点时也尝试添加标签
  const addTagOnBlur = () => {
    if (inputValue.value.trim()) {
      addTag()
    }
  }
  
  // 删除标签
  const removeTag = (index) => {
    const newTags = [...props.modelValue]
    newTags.splice(index, 1)
    emit('update:modelValue', newTags)
    nextTick(() => {
      inputRef.value.focus()
    })
  }
  
  // 选择默认标签
  const selectDefaultTag = (tag) => {
    if (props.allowDuplicates || !props.modelValue.includes(tag)) {
      const newTags = [...props.modelValue, tag]
      emit('update:modelValue', newTags)
    }
    nextTick(() => {
      inputRef.value.focus()
    })
  }
  
  // 处理退格键
  const handleBackspace = () => {
    if (inputValue.value === '' && props.modelValue.length > 0) {
      const newTags = [...props.modelValue]
      newTags.pop()
      emit('update:modelValue', newTags)
    }
  }
  
  // 点击容器聚焦输入框
  const focusInput = () => {
    inputRef.value.focus()
  }
  </script>
  
  <style scoped>
  .inline-tag-input {
    width: 100%;
  }
  
  .tag-input-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    min-height: 40px;
    padding: 0 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: white;
    cursor: text;
  }
  
  .tag-input-container:focus-within {
    border-color: #409eff;
  }
  
  .input-tag {
    display: inline-flex;
    align-items: center;
    height: 24px;
    margin: 3px;
    padding: 0 8px;
    background-color: #f0f2f5;
    border-radius: 4px;
    font-size: 12px;
    color: #606266;
  }
  
  .tag-remove {
    margin-left: 4px;
    color: #909399;
    cursor: pointer;
  }
  
  .tag-remove:hover {
    color: #f56c6c;
  }
  
  .tag-input-field {
    flex: 1;
    min-width: 100px;
    height: 30px;
    padding: 0 5px;
    border: none;
    outline: none;
    font-size: 14px;
  }
  
  .default-tags {
    margin-top: 10px;
  }
  
  .default-tags-title {
    margin-right: 10px;
    color: #909399;
    font-size: 14px;
  }
  
  .default-tag {
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
  }
  
  .default-tag:hover {
    opacity: 0.8;
  }
  </style>