(function (window, undefined) {
    
    // 插件初始化函数
    window.Asc.plugin.init = function () {
        console.log('My Plugin initialized');
        
        // 获取用户信息
        var userName = window.Asc.plugin.info.userName;
        var lang = window.Asc.plugin.info.lang;
        var editorType = window.Asc.plugin.info.editorType;
        
        console.log('User:', userName);
        console.log('Language:', lang);
        console.log('Editor Type:', editorType);
        
        // 添加工具栏菜单项
        window.Asc.plugin.executeMethod('AddToolbarMenuItem', [getToolbarItems()]);
    };

    // 插件按钮点击处理
    window.Asc.plugin.button = function (id) {
        if (id === 0) {
            // 确定按钮
            insertText();
        } else {
            // 取消按钮
            closePlugin();
        }
    };

    // 翻译函数
    window.Asc.plugin.onTranslate = function () {
        // 这里可以添加多语言支持
        document.querySelector('h2').textContent = window.Asc.plugin.tr('我的插件');
    };

    // 获取工具栏菜单项配置
    function getToolbarItems() {
        return {
            guid: window.Asc.plugin.info.guid,
            tabs: [
                {
                    id: 'MyPluginTab',
                    text: '我的插件',
                    items: [
                        {
                            id: 'insertText',
                            text: '插入文本',
                            type: 'button',
                            hint: '插入自定义文本',
                            icons: 'resources/icon.png',
                            lockInViewMode: true,
                            enableToggle: false,
                            separator: false
                        },
                        {
                            id: 'insertParagraph',
                            text: '插入段落',
                            type: 'button',
                            hint: '插入新段落',
                            icons: 'resources/icon.png',
                            lockInViewMode: true,
                            enableToggle: false,
                            separator: false
                        }
                    ]
                }
            ]
        };
    }

    // 工具栏菜单点击事件
    window.Asc.plugin.attachToolbarMenuClickEvent('insertText', function () {
        console.log('Insert Text clicked');
        insertSampleText();
    });

    window.Asc.plugin.attachToolbarMenuClickEvent('insertParagraph', function () {
        console.log('Insert Paragraph clicked');
        insertSampleParagraph();
    });

    // 插入文本函数
    function insertText() {
        var textInput = document.getElementById('textInput');
        var textArea = document.getElementById('textArea');
        
        var singleLineText = textInput.value;
        var multiLineText = textArea.value;
        
        var textToInsert = singleLineText;
        if (multiLineText) {
            textToInsert = multiLineText;
        }
        
        if (textToInsert) {
            insertIntoDocument(textToInsert);
        } else {
            alert('请输入要插入的文本');
        }
    }

    // 插入示例文本
    function insertSampleText() {
        insertIntoDocument('这是通过插件插入的示例文本');
    }

    // 插入示例段落
    function insertSampleParagraph() {
        var sampleParagraph = '这是一个示例段落。\n\n这个段落展示了如何通过 OnlyOffice 插件插入格式化的文本内容。';
        insertIntoDocument(sampleParagraph);
    }

    // 插入内容到文档
    function insertIntoDocument(text) {
        var editorType = window.Asc.plugin.info.editorType;
        
        if (editorType === 'word') {
            // Word 文档
            window.Asc.plugin.callCommand(function () {
                var oDocument = Api.GetDocument();
                var oParagraph = Api.CreateParagraph();
                oParagraph.AddText(text);
                oDocument.InsertContent([oParagraph]);
            });
        } else if (editorType === 'cell') {
            // Excel 表格
            window.Asc.plugin.callCommand(function () {
                var oWorksheet = Api.GetActiveSheet();
                var oRange = oWorksheet.GetSelection();
                oRange.SetValue(text);
            });
        } else if (editorType === 'slide') {
            // PowerPoint 演示文稿
            window.Asc.plugin.callCommand(function () {
                var oPresentation = Api.GetPresentation();
                var oSlide = oPresentation.GetCurrentSlide();
                var oShape = Api.CreateShape('rect', 200 * 36000, 100 * 36000, Api.CreateNoFill(), Api.CreateStroke(0, Api.CreateNoFill()));
                oShape.SetPosition(100 * 36000, 100 * 36000);
                var oContent = oShape.GetDocContent();
                oContent.RemoveAllElements();
                var oParagraph = Api.CreateParagraph();
                oParagraph.AddText(text);
                oContent.Push(oParagraph);
                oSlide.AddObject(oShape);
            });
        }
        
        console.log('Text inserted:', text);
    }

    // 关闭插件
    function closePlugin() {
        window.Asc.plugin.executeCommand('close', '');
    }

    // 全局函数，供 HTML 调用
    window.insertText = insertText;
    window.closePlugin = closePlugin;

})(window, undefined);
