<template>
  <el-dialog 
    v-model="dialogVisible" 
    :scroll="true"
    :fullscreen="true"
    :destroy-on-close="true"
    :title="dialogTitle">
    <div class="content">
      <header>
          <h1>国家电网职称晋升指南</h1>
          <p><strong>版本号：</strong>2.3 | <strong>生效日期：</strong>2023年8月</p>
      </header>

      <section>
          <h2>一、职称体系说明</h2>
          <table>
              <tr><th>体系类型</th><th>适用对象</th><th>等级范围</th><th>对接标准</th></tr>
              <tr>
                  <td>高校科研体系</td>
                  <td>高校/科研院所合作专家</td>
                  <td>P1-P4</td>
                  <td>《高等教育职称评审办法》</td>
              </tr>
              <tr>
                  <td>企业应用体系</td>
                  <td>国家电网内部技术专家</td>
                  <td>E1-E4</td>
                  <td>《国家电网职称管理办法》</td>
              </tr>
          </table>
      </section>

      <!-- 在原有代码的 <section> 区块后添加以下内容 -->

  <!-- 晋升条件明细表 -->
  <section>
      <h2>二、晋升条件明细表</h2>
      
      <h3>2.1 企业应用体系（E类）</h3>
      <table>
          <tr>
              <th>目标职称</th>
              <th>技术成果要求</th>
              <th>项目经验要求</th>
              <th>专项条件</th>
          </tr>
          <tr>
              <td>教授级高工</td>
              <td>
                  <ul style="margin:0;padding-left:20px">
                      <li>发明专利≥5项（第一发明人）</li>
                      <li>SCI/EI论文≥3篇</li>
                  </ul>
              </td>
              <td>
                  <ul style="margin:0;padding-left:20px">
                      <li>主持特高压工程（≥750kV）</li>
                      <li>担任智能电网项目总工≥2次</li>
                  </ul>
              </td>
              <td>
                  <ul style="margin:0;padding-left:20px">
                      <li>注册电气工程师证</li>
                      <li>参与国网标准制定</li>
                  </ul>
              </td>
          </tr>
      </table>

      <h3>2.2 高校科研体系（P类）</h3>
      <table>
          <tr>
              <th>目标职称</th>
              <th>科研要求</th>
              <th>产业转化要求</th>
          </tr>
          <tr>
              <td>二级教授</td>
              <td>国家级科研项目负责人</td>
              <td>国网合作项目经费≥500万</td>
          </tr>
      </table>
  </section>

  <!-- 申报材料清单 -->
  <section>
      <h2>三、申报材料清单</h2>
      <div class="material-examples">
          <!-- 基础材料示例 -->
          <div class="example-card">
              <h3>3.1 基础材料示例</h3>
              <div class="file-sample">
                  <!-- 申请表预览 -->
                  <div class="form-preview">
                      <div class="form-header">
                          <h4>国家电网职称晋升申请表</h4>
                          <div class="form-meta">
                              <span>申请编号：SGCC-2023-0001</span>
                              <span>申请日期：2023-08-01</span>
                          </div>
                      </div>
                      
                      <table class="form-table">
                          <tr>
                              <th width="25%">姓名</th>
                              <td width="75%">张三</td>
                          </tr>
                          <tr>
                              <th>工号</th>
                              <td>SGCC-2020-01892</td>
                          </tr>
                          <tr>
                              <th>申请职称</th>
                              <td>高级工程师（E3）</td>
                          </tr>
                          <tr>
                              <th>工作单位</th>
                              <td>××省电力公司技术中心</td>
                          </tr>
                          <tr>
                              <th colspan="2" class="section-title">主要业绩</th>
                          </tr>
                          <tr>
                              <td colspan="2">
                                  <ul class="achievement-list">
                                      <li>主持±800kV××换流站建设项目</li>
                                      <li>获省部级科技进步一等奖（2021）</li>
                                  </ul>
                              </td>
                          </tr>
                      </table>
                      
                      <div class="signature-area">
                          <div class="signature-line">申请人签字：_______________</div>
                          <div class="signature-line">部门盖章：<span class="stamp">[电子章]</span></div>
                      </div>
                  </div>
          
                  <!-- 格式要求 -->
                  
                  <div class="file-requirements">
                      <h4>格式要求：</h4>
                      <ul class="requirement-list">
                          <li>文件格式：PDF/A4纵向</li>
                          <li>命名规则：<code>单位编号-姓名-申请表.pdf</code></li>
                          <li>签字要求：手写签名+部门电子章</li>
                      </ul>
                  </div>
              </div>
          </div>

          <!-- 技术成果证明示例 -->
          <div class="example-card">
              <h3>3.2 技术成果证明示例</h3>
              <table class="material-table">
                  <thead>
                      <tr>
                          <th width="25%">材料类型</th>
                          <th width="35%">合格样例</th>
                          <th width="40%">常见问题</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr>
                          <td>发明专利证书</td>
                          <td>
                              <ul class="sample-list">
                                  <li>包含授权公告页</li>
                                  <li>国家知识产权局红章扫描件</li>
                                  <li>专利号清晰可见</li>
                              </ul>
                          </td>
                          <td>
                              <div class="problem-alert">
                                  <span class="alert-icon">⚠</span>
                                  受理通知书不能替代授权证书
                              </div>
                          </td>
                      </tr>
                      <tr>
                          <td>论文收录证明</td>
                          <td>
                              <ul class="sample-list">
                                  <li>图书馆开具的检索报告</li>
                                  <li>包含ISSN/ISBN编号</li>
                                  <li>DOI号标注在右上角</li>
                              </ul>
                          </td>
                          <td>
                              <div class="problem-alert">
                                  <span class="alert-icon">⚠</span>
                                  会议论文集不予认可
                              </div>
                          </td>
                      </tr>
                  </tbody>
              </table>
          </div>

          <!-- 推荐信示例 -->
          <div class="example-card">
              <h3>3.3 推荐信模板</h3>
              <div class="recommendation-template">
                  <div class="letterhead">
                      <div class="header-text">
                          <h4>国家电网公司职称评审委员会</h4>
                          <p>地址：北京市西城区西长安街86号</p>
                      </div>
                  </div>
                  
                  <div class="letter-body">
                      <p class="salutation">尊敬的评审委员会：</p>
                      
                      <div class="content-section">
                          <p>兹推荐我单位<strong>张三</strong>同志（工号：SGCC-2020-01892）申报<strong>高级工程师</strong>职称。</p>
                          
                          <p class="indent">该同志在特高压直流输电领域的主要贡献包括：</p>
                          <ol class="achievement-list">
                              <li>主持完成±800kV××换流站建设项目（2019-2022）</li>
                              <li>获省部级科技进步一等奖（2021年）</li>
                              <li>牵头编制Q/GDW 11874-2020标准</li>
                          </ol>
                      </div>
          
                      <div class="signature-block">
                          <p>推荐单位：××省电力公司技术中心</p>
                          <p>联系人：李××（技术总监）</p>
                          <p>联系电话：0571-××××××××</p>
                          <p class="letter-date">2023年8月15日</p>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>

      <section>
          <h2>四、评审流程说明</h2>
          <div class="process-detail">
              <h3>4.1 完整评审流程</h3>
              <div class="timeline">
                  <div class="timeline-item">
                      <div class="timeline-marker"></div>
                      <div class="timeline-content">
                          <h4>第一阶段：材料筹备（1-3月）</h4>
                          <ul>
                              <li>填写《职称晋升申请表》</li>
                              <li>准备近五年业绩证明材料</li>
                          </ul>
                      </div>
                  </div>
                  <div class="timeline-item">
                      <div class="timeline-marker"></div>
                      <div class="timeline-content">
                          <h4>第二阶段：单位推荐（4月）</h4>
                          <ul>
                              <li>部门负责人填写推荐意见</li>
                              <li>人力资源部资格预审</li>
                          </ul>
                      </div>
                  </div>
              </div>
          </div>
      </section>
      
      <section>
          <h2>五、评审流程图解</h2>
          <div class="mermaid">
                <div v-html="svgCode"></div>
          </div>
      </section>
      <section>
          <h2>六、附录</h2>
          <ul class="checklist">
              <li><a href="https://sgcc.com/policy" target="_blank">《国家电网职称管理办法》全文下载</a></li>
              <li><a href="https://sgcc.com/journals" target="_blank">电力核心期刊目录（2023版）</a></li>
          </ul>
      </section>

      <footer class="no-print">
          <p>联系部门：人力资源部职称管理办公室</p>
          <p>联系电话：400-800-5000</p>
      </footer>
    </div>
  </el-dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as FolderAPI from '@/api/knowledge/docmangement'
import mermaid from 'mermaid';

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'SystemUserForm' })

const message = useMessage() // 消息弹窗
const emit = defineEmits(['success'])

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const dialogType = ref('')

const mermaidCode = ref(`
graph TD
    A[材料初审] --> B{形式审查}
    B -->|通过| C[专家盲审]
    B -->|不通过| D[退回补正]
    C --> E{评审委员会终审}
    E -->|通过| F[公示]
    E -->|不通过| G[申诉通道]
`)

mermaid.initialize({ 
	startOnLoad: true,
	themeVariables: { primaryColor: '#fff' }
});
const open = async(id) => {
  dialogVisible.value = true
  dialogTitle.value = '查看报告'
  const response = await FolderAPI.generatePromotionReports(id)
  console.log(response);
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const svgCode = ref('')

onMounted(async() => {
	const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`
    // 渲染图表
	const { svg } = await mermaid.render(id, mermaidCode.value)
	svgCode.value = svg
})


</script>

<style scoped lang="scss">
// :root {
//     $sgcc-blue: #005BAC;
//     $sgcc-gray: #4A4A4A;
//     $sgcc-gray-light: #f8f9fa;
// }
$sgcc-blue: #005BAC;
$sgcc-gray: #4A4A4A;
$sgcc-gray-light: #f8f9fa;

body {
    font-family: "微软雅黑", sans-serif;
    line-height: 1.6;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2, h3 {
    color: $sgcc-blue;
    margin: 1.5em 0 1em;
}

h1 { border-bottom: 3px solid $sgcc-blue; }

table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5em 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

ul {
    display: block;
    list-style-type: disc;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 40px;
    unicode-bidi: isolate;
}

th, td {
    padding: 12px;
    border: 1px solid #ddd;
    text-align: left;
}

th {
    background-color: $sgcc-blue;
    color: white;
}

.mermaid {
    background: $sgcc-gray-light;
    padding: 20px;
    border-radius: 8px;
    margin: 2em 0;
}

.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 2em;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 5px;
    width: 16px;
    height: 16px;
    background: $sgcc-blue;
    border-radius: 50%;
}

.material-examples {
    margin: 2em 0;
    background: white;
    border-radius: 8px;
}

@media (max-width: 768px) {
    body { padding: 10px; }
    table { display: block; overflow-x: auto; }
}

@media print {
    .no-print { display: none; }
    table { box-shadow: none; }
}

/* 优化后的样式 */
.recommendation-template {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 25px;
    font-family: "宋体", SimSun, serif;
    line-height: 1.8;
}

.letterhead {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    border-bottom: 2px solid $sgcc-blue;
    padding-bottom: 15px;
}

.letter-logo {
    height: 45px;
    margin-right: 20px;
}

.header-text h4 {
    color: $sgcc-blue;
    margin: 0;
    font-size: 1.1em;
}

.header-text p {
    color: #666;
    margin: 5px 0 0;
    font-size: 0.9em;
}

.letter-body {
    font-size: 14px;
}

.salutation {
    font-weight: 600;
    margin: 15px 0;
}

.indent {
    text-indent: 2em;
    margin: 10px 0;
}

.achievement-list {
    margin: 10px 0 10px 40px;
}

.achievement-list li {
    margin: 8px 0;
    list-style-type: decimal;
}

.signature-block {
    margin-top: 30px;
    text-align: right;
}

.letter-date {
    margin-top: 20px;
    font-weight: 600;
}

@media (max-width: 768px) {
    .recommendation-template {
        padding: 15px;
    }
    
    .letter-logo {
        height: 35px;
    }
    
    .achievement-list {
        margin-left: 20px;
    }
}

/* 申请表预览样式 */
.form-preview {
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 4px;
    background: #fff;
    max-width: 600px;
}

.form-header {
    text-align: center;
    margin-bottom: 20px;
}

.form-header h4 {
    color: $sgcc-blue;
    margin: 0 0 10px;
}

.form-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.9em;
    color: #666;
}

.form-table {
    width: 100%;
    margin: 15px 0;
    border-collapse: collapse;
}

.section-title {
    background: $sgcc-blue;
    color: white;
    text-align: center;
    font-weight: bold;
}

.achievement-list {
    margin: 0;
    padding-left: 20px;
}

.signature-area {
    margin-top: 25px;
    padding-top: 15px;
    border-top: 2px solid $sgcc-blue;
}

.signature-line {
    margin: 15px 0;
}

.stamp {
    color: #c00;
    font-style: italic;
    margin-left: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-preview {
        padding: 15px;
    }
    
    .form-meta {
        flex-direction: column;
        gap: 5px;
    }
}

/* 新增分栏样式 */
.file-sample {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
    margin: 20px 0;
}

.form-preview {
    /* 保持原有样式 */
    height: fit-content;
}

.file-requirements {
    background: $sgcc-gray-light;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

/* 响应式处理 */
@media (max-width: 992px) {
    .file-sample {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .form-preview {
        max-width: 100%;
    }
}
  
</style>
