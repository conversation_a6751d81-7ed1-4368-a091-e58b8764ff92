<template>
  <el-dialog 
    v-model="dialogVisible" 
    fullscreen
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :title="dialogTitle">
    <component :is="currentComponent" />
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import MyCollect from '@/views/knowledge/docManagement/MyCollect.vue'
import MyLike from '@/views/knowledge/docManagement/MyLike.vue'
import MyShare from '@/views/knowledge/docManagement/MyShare.vue'

defineOptions({ name: 'MyDocumentList' })

const { t } = useI18n()
const message = useMessage()

// 定义类型映射
const typeMap = {
  collect: {
    title: '我的收藏',
    component: MyCollect
  },
  like: {
    title: '我的喜欢',
    component: MyLike
  },
  share: {
    title: '我的分享',
    component: MyShare
  }
}

const dialogVisible = ref(false)
const currentType = ref('') // 当前类型

// 计算属性获取当前标题
const dialogTitle = computed(() => {
  return typeMap[currentType.value]?.title || ''
})

// 计算属性获取当前组件
const currentComponent = computed(() => {
  return typeMap[currentType.value]?.component || null
})

/** 打开弹窗 */
const open = async (type) => {
  if (!typeMap[type]) {
    message.error('未知的类型')
    return
  }
  
  currentType.value = type
  dialogVisible.value = true
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.chat-list-dialog {
  position: relative;
  height: calc(100vh - 60px);
  background-color: var(--el-color-white);
}
</style>