<template>
  <el-popover 
    placement="left" 
    width="200" 
    trigger="click">
    <template #reference>
      <div class="tl-fixed">
        <p class="fixed-text">智能助手</p>
      </div>
    </template>
    <div class="pop-list">
      <el-row>
        <el-col v-for="(item, index) in chatList" :key="index" :span="24">
          <div class="pop-item"  style="text-align: left">
            <el-button @click="openDraw(item)" type="text">
              {{ item.name }}
            </el-button>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-popover>
  
  <el-drawer
    v-model="drawer"
    :title="chatName"
    direction="rtl"
    size="600px"
  >
    <!-- 缓存非 inner 类型的组件 -->
    <keep-alive v-if="!isInnerPage">
      <component
        :is="currentComponent"
        @cancel="handleCancel"
        v-bind="currentProps"
      />
    </keep-alive>
    <!-- 不缓存 inner 类型的组件（如 iframe） -->
    <component
      v-else
      :is="currentComponent"
      @cancel="handleCancel"
      v-bind="currentProps"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, shallowRef } from 'vue'
import TitleReport from './components/TitleReport.vue'
import TechnicalSpecifications from './components/TechnicalSpecifications.vue'
import IFrameComponent from './components/IFrameComponent.vue'
import { getAgentPage } from '@/api/system/agent/index'

const chatList = ref([
  {
    name: '职称晋级评估报告',
    component: TitleReport
  },
  {
    name: '通用可研生成技术规范书',
    component: TechnicalSpecifications
  },
  // {
  //   name: '腾讯元宝对话',
  //   component: IFrameComponent,
  //   type: 'inner',
  //   props: {
  //     url: 'https://yuanbao.tencent.com/chat/naQivTmsDa'
  //   }
  // },
  // {
  //   name: '格式化工具箱',
  //   component: IFrameComponent,
  //   type: 'inner',
  //   props: {
  //     url: 'https://www.toolhelper.cn/JSON/JSONFormat'
  //   }
  // }
])

const drawer = ref(false)
const chatName = ref('')
const currentComponent = shallowRef(null)
const currentProps = ref({})
const isInnerPage = ref(false) // 是否是内嵌页面

const openDraw = (chatItem) => {
  chatName.value = chatItem.name
  currentComponent.value = chatItem.component
  currentProps.value = chatItem.props || {}

  // 判断是否是 inner 类型（如 iframe）
  isInnerPage.value = chatItem.type === 'inner'
  
  drawer.value = true
}

const getAgentList = async () => {
  let chatAgent = []
  const { records } = await getAgentPage({ pageNum: 1, pageSize: 100 })
  if(records && records.length) {
    chatAgent = records.map((item : any) => {
      return {
        name: item.name,
        component: IFrameComponent,
        type: 'inner',
        props: {
          url: item.url
        }
      }
    })
  }
  chatList.value = chatList.value.concat(chatAgent)
}

onMounted(() => {
  getAgentList()
})


const handleCancel = () => {
  drawer.value = false
}
</script>

<style lang="scss" scoped>
.qa-container {
  margin: 0 auto;
  height: 100%;
  min-height: 200px;
  border: 1px solid #ccc;
  border-radius: 8px;
}
.detail-container {
  height: 100%;
}
// main 容器
.main-container {
  margin: 0;
  padding: 0;
  position: relative;
  height: 100%;
  width: 100%;

  .message-container {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-y: hidden;
    padding: 0;
    margin: 0;
  }
}

// 底部
.footer-container {
  display: flex;
  flex-direction: column;
  height: auto;
  margin: 0;
  padding: 0;

  .prompt-from {
    display: flex;
    flex-direction: column;
    height: auto;
    border: 1px solid #e3e3e3;
    border-radius: 10px;
    margin: 10px 20px 20px 20px;
    padding: 9px 10px;
  }

  .prompt-input {
    height: 80px;
    //box-shadow: none;
    border: none;
    box-sizing: border-box;
    resize: none;
    padding: 0 2px;
    overflow: auto;
  }

  .prompt-input:focus {
    outline: none;
  }

  .prompt-btns {
    display: flex;
    justify-content: space-between;
    padding-bottom: 0;
    padding-top: 5px;
  }

}
// 按钮
.tl-fixed {
    width: 50px;
    height: 130px;
    background-color: #5e60f3;
    position: fixed;
    z-index: 99;
    right: 30px;
    bottom: 10vh;
    cursor: pointer;
    border-radius: 25px;
    padding: 10px 0;
    .fixed-text {
      writing-mode: vertical-lr;
      font-size: 18px;
      font-weight: 700;
      color: #fff;
      line-height: 50px;
      display: inline-block;
      width: 100%;
      height: 100%;
      text-align: center;
      letter-spacing: 8px;
    }
}

</style>

<style lang="scss">
  .pop-list {
    width: 100%;
    .pop-item {
      text-align: center;
      > * {
        vertical-align: middle;
      }
    }
  }
  .el-popover {
    min-width: 50px;
  }
</style>
