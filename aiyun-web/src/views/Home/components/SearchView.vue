<template>
  <div>
    <div v-show="!isShowChat" class="content">
      <el-card class="card" v-loading="chatLoading">
        <template #header>
          <div class="flex">
            <p class="flex items-center flex-1">
              <img
                class="h-26px w-26px mr-5px"
                src="@/assets/ai/ai.png"
              />
              智能问答</p>
              <img
                @click.stop="handleShowChat"
                class="h-26px w-26px mr-5px cursor-pointer"
                src="@/assets/ai/AIchat.svg"
              />
          </div>
        </template>
        <div class="chat-result">
          <el-button link class="arrow-icon mr-20px" @click="arrowToogle" >
            <Icon :icon="isArrow ?'ep:arrow-down' : 'ep:arrow-up'" size="'16'" />
          </el-button>
          <el-scrollbar ref="scrollbarRef" :max-height="chatHeight">
            <div ref="innerRef">
              <MarkdownView class="left-text pr-20px" :content="chatMessage" />
            </div>
          </el-scrollbar>
        </div>
      </el-card>
      <div v-loading="searchLoading" v-infinite-scroll="load">
        <div v-if="list.length">
          <el-card v-for="item in list" :key="item.id" class="card">
            <el-popover
              placement="top"
              trigger="hover"
              :width="'70vw'"
              popper-class="popover-answer"
            >
              <template #reference>
                <p class="card-content" v-html="item.highlight + '...'"></p>
              </template>
              <div v-html="item.content" class="answer-pop-content"></div>
            </el-popover>
            <p class="card-footer" @click.stop="handleShowFile(item)">{{ item.document_keyword }}</p>
          </el-card>
        </div>
        <div v-else>
          未搜索到结果！
        </div>
        <p class="tips" v-if="searchLoading">Loading...</p>
        <p class="tips" v-if="noMore">没有更多了</p>
      </div>
    </div>
    <div v-if="isShowChat" class="chat-content">
      <el-button link class="close-icon" @click.stop="handleClose" >
        <Icon icon="ep:close-bold" :size="16" />
      </el-button>
      <div class="position-relative h-100%">
        <AiChat ref="aiChatRef" @success="handleChatSuccsess" />
      </div>
    </div>
    <DocView ref="docViewRef" />
  </div>
  </template>
  <script setup>
  
  import axios from 'axios'
  import MarkdownView from '@/components/MarkdownView/index.vue'
  import { fetchEventSource } from '@microsoft/fetch-event-source'
  import { useUserStore } from '@/store/modules/user'
   import * as homeApi from '@/api/home/<USER>'
  import AiChat from '@/views/knowledge/dataTraining/ChatToHome.vue'
  import DocView from '@/views/knowledge/docManagement/components/DocView.vue'
  
  defineOptions({ name: 'SearchView' })

  const userStore = useUserStore()
  const apikeys = userStore.getAppConfig
  
  const message = useMessage() // 消息弹窗
  const { t } = useI18n() // 国际化
  
  const list = ref([]) // 列表的数据
  const chatMessage = ref('')
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 10,
  })
  const isFirst = ref(true)
  const searchLoading = ref(false)
  const chatLoading = ref(false)
  const isShowChat = ref(false)
  const total = ref(0)
  const innerRef = ref()
  const disabled = computed(() => searchLoading.value || noMore.value)
  const searchParams = ref({})
  const isChating = ref(false)
  const noMore = computed(() => queryParams.pageNo * queryParams.pageSize >= total.value)
  const chatHeight = computed(() => {
    if (isArrow.value) {
      return '200px'
    } else {
      return '400px'
    }
  })

  const load = async () => {
    if (disabled.value) return
    queryParams.pageNo += 1
    getList()
  }
  
  /** 查询列表 */
  const getList = async () => {
    try {
      searchLoading.value = true
      isFirst.value = false
      const params = {
        question: searchParams.value.searchMessage,
        label: searchParams.value.checkValue,
        pageSize: queryParams.pageSize,
        pageNo: queryParams.pageNo,
      }
      if(searchParams.value.searchType) {
        params[searchParams.value.searchType] = searchParams.value.searchMessage
      }
      const response = await homeApi.getHomeRetrieval(params)
      const responseObj = JSON.parse(response)
      const { data } = responseObj
      console.log(JSON.parse(response));
      if(data) {
        list.value.push(...data.chunks)
        total.value = data.total
      }
      return
      // list.value = data.data.docs
    } finally {
      searchLoading.value = false
      isFirst.value = false
    }
  }
  
  const abortController = ref()
  const scrollbarRef = ref()
  const getChatMessage = async() => {
    chatLoading.value = true
    isChating.value = true
    abortController.value = new AbortController()
    homeApi.homePageStream({
      data: {
        inquiryContent: searchParams.value.searchMessage,
      },
      ctrl: abortController.value,
      onMessage: async(msg) => {
        const { code, data, message } = JSON.parse(msg.data)
        if(code === 200) {
          if(data.answerContent.includes('Sorry') && messageCount.value < 4) {
            messageCount.value++
            stopStream()
            getChatMessage()
            return
          } else if(data.answerContent.includes('Sorry')) {
            stopStream()
            chatLoading.value = false
            chatMessage.value = '抱歉，没有提供相关信息'
            return
          }
          chatLoading.value = false
          chatMessage.value = data.answerContent ? data.answerContent.replace(/##.*?\$\$/g, '') : ''
          setTimeout(() => {
            scrollbarRef.value.setScrollTop(innerRef.value.clientHeight)
          }, 1000);
        }
      },
      onClose: (err) => {
        isChating.value = false
        stopStream()
        console.log('结束');
      },
      onError: (err) => {
        stopStream()
        console.log('对话异常');
        
        // message.alert(`对话异常! ${err}`)
      },
    })
  }

  /** 停止 stream 流式调用 */
const stopStream = async () => {
  // tip：如果 stream 进行中的 message，就需要调用 controller 结束
  if (abortController.value) {
    abortController.value.abort()
  }
}
const conversationId = ref({})

const aiChatRef = ref()
const handleShowChat = async() => {
  console.log('handleShowChat');
  if(isChating.value) {
    message.warning('正在对话中，请稍后')
    return
  }
  isShowChat.value = true
  await nextTick()
  aiChatRef.value && aiChatRef.value.handleChatToHome({
    question: searchParams.value.searchMessage,
    answer: chatMessage.value
  }, conversationId.value)
}

const handleChatSuccsess = (chatItem) => {
  conversationId.value = chatItem
}

  const isArrow = ref(true)
  const arrowToogle = () => {
    isArrow.value = !isArrow.value
  }

  const handleClose = () => {
    console.log('关闭');
    isShowChat.value = false
  }
  const messageCount = ref(0)

   const docViewRef = ref()

  const handleShowFile = (row) => {
    console.log('handleShowFile', row);
    const item = {
      fileUrl: row.url,
      fileName: row.document_keyword
    }
    docViewRef.value.open('view', {}, item, true, '搜索结果' );
  }

  const open = async(params) => {
    searchParams.value = { ...params }
    await stopStream()
    list.value = []
    isShowChat.value = false
    conversationId.value = {}
    messageCount.value = 0
    getList()
    getChatMessage()
  }

  defineExpose({ open })

  </script>
  <!-- style="max-width: 600px; height: 36px;" -->
  <style lang="scss" scoped>
    .content-header {
      position: sticky;
      top: 36%;
      padding-right: 20px;
      // display: flex;
      // justify-content: center;
      .input-group {
        display: flex;
        justify-content: center;
        margin-top: 30px;
      }
      .search-logo {
        width: 320px;
        display: block;
        margin: 0 auto;
      }
    }
    .search-input {
      width: 600px;
      height: 40px;
      :deep(.el-input__wrapper) {
        border-start-start-radius: 30px !important;
        border-end-start-radius: 30px !important;
      }
      :deep(.el-input-group__append) {
        border-start-end-radius: 30px !important;
        border-end-end-radius: 30px !important;
        overflow: hidden;
        :hover {
          background-color: #1890ff;
        }
      }
      .search-btn {
        height: 100%;
        background-color: #1890ff;
        border-radius: 0;
        :deep(.is-loading) {
          color: aliceblue;
        }
      }
    }
    .content {
    	width: 100%;
      margin: 0 auto;
      position: relative;
      padding-right: 20px;
      .content-search {
        width: 100%;
        display: flex;
        justify-content: center;
        position: absolute;
        top: 0;
        .search-input {
          width: 100%;
        }
      }
      .card {
        margin-bottom: 16px;
      }
    }
    .chat-content {
      position: relative;
      width: 100%;
      height: 560px;
      padding-top: 20px;
      .close-icon {
        position: absolute;
        top: 0px;
        right: 10px;
      }
    }
    .card-content {
      font-size: 14px;
    }
    :deep(.el-card__body) {
      em {
        color: red;
        font-style: normal;
      }
    }
    .card-footer {
      font-size: 14px;
      margin-top: 5px;
      cursor: pointer;
      color: #1890ff;
      &:hover {
        text-decoration: underline;
      }
    }
    .tips {
      font-size: 14px;
      text-align: center;
    }
    .chat-result {
      position: relative;
      text-align: right;
      padding-top: 20px;
      .arrow-icon {
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  </style>
