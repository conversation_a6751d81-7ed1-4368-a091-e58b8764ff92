// A Chat plugin of AI
(function (window, undefined) {
    let messageHistory = null; // a reference to the message history DOM element
    let conversationHistory = null; // a list of all the messages in the conversation
    let messageInput = null;
    let lang = '';
    let langMap = null;
    let abortController = null;
    let lastRequest = null;
    let loadElement = null;
    let regenButton = null;
    let stopButton = null;
    let checkValue = null;
    let userName = null;
    // let customReqWindow = null;

    // initialize the plugin
    window.Asc.plugin.init = function () {
        userName = window.Asc.plugin.info.userName;
        console.log(userName);
        lang = window.Asc.plugin.info.lang.substring(0, 2);
        console.log('current lang: ', lang);

        langMap = new Map();
        langMap.set('en', 'English');
        langMap.set('zh', 'Chinese');

        messageHistory = document.querySelector('.message-history');
        conversationHistory = [];

        window.Asc.plugin.executeMethod('AddToolbarMenuItem', [getToolbarItems()]);
    };

    window.Asc.plugin.button = function () {
        this.executeCommand('close', '');
    };

    window.Asc.plugin.onTranslate = function () {
        document.getElementById('greeting').innerHTML = window.Asc.plugin.tr('Hi there! I am YunZhi, how can I help you today?');
        document.getElementById('chat-title').innerHTML = window.Asc.plugin.tr('YunZhi Chat');
        document.getElementById('clear-button').title = window.Asc.plugin.tr('Clear Chat');
        document.getElementById('send-button').title = window.Asc.plugin.tr('Send Message');
        document.getElementById('userInput').placeholder = window.Asc.plugin.tr('Type your message here...');
        document.getElementById('first-insert').title = window.Asc.plugin.tr('insert document');
        document.getElementById('stop-button').innerHTML = '<span>&#x25A0;</span>' + window.Asc.plugin.tr('stop');
        document.getElementById('regenerate-button').innerHTML = '<span>&#x21BA;</span>' + window.Asc.plugin.tr('regeneration');
    };

    function getToolbarItems() {
        let settings = {
            guid: window.Asc.plugin.info.guid,
            tabs: [
                {
                    id: 'YunZhiTools',
                    text: '云智',
                    items: [],
                },
            ],
        };
        if (Asc.plugin.info.editorType === 'word') {
            settings.tabs[0].items.push(
                {
                    id: 'onAnalyse',
                    text: '分析文本',
                    type: 'button',
                    hint: '分析文本',
                    icons: 'resources/images/icon/fenxi_1.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                },
                {
                    id: 'onComplete',
                    text: '完整文本',
                    type: 'button',
                    hint: '完整文本',
                    icons: 'resources/images/icon/wanzhengdu.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                },
                {
                    id: 'onGenerateDraft',
                    text: '生成草稿',
                    type: 'button',
                    hint: '生成草稿',
                    icons: 'resources/images/icon/caogaoxiang.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                },
                {
                    id: 'onRewrite',
                    text: '重写文本',
                    type: 'button',
                    hint: '重写文本',
                    icons: 'resources/images/icon/jiuzhengxingweixiu.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                },
                {
                    id: 'onShorten',
                    text: '缩短文本',
                    type: 'button',
                    hint: '缩短文本',
                    icons: 'resources/images/icon/gongju-suohui.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                },
                {
                    id: 'onExpand',
                    text: '扩展文本',
                    type: 'button',
                    hint: '扩展文本',
                    icons: 'resources/images/icon/kuozhan.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                }
            );
        } else if (Asc.plugin.info.editorType === 'cell') {
            settings.tabs[0].items.push(
                {
                    id: 'onExplainC',
                    text: '解释所选表格',
                    type: 'button',
                    hint: '解释所选表格',
                    icons: 'resources/images/icon/jieshi.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                },
                {
                    id: 'onSummariseC',
                    text: '总结所选表格',
                    type: 'button',
                    hint: '总结所选表格',
                    icons: 'resources/images/icon/zhuti.png',
                    lockInViewMode: true,
                    enableToggle: false,
                    separator: false,
                }
            );
        } else if (Asc.plugin.info.editorType === 'slide') {
            settings.tabs[0].items.push({
                id: 'onOutline',
                text: '获取主题大纲',
                type: 'button',
                hint: '获取主题大纲',
                icons: 'resources/images/icon/zhuti.png',
                lockInViewMode: true,
                enableToggle: false,
                separator: false,
            });
        }
        // Common Functions
        settings.tabs[0].items.push(
            {
                id: 'onSummarize',
                text: '总结文本',
                type: 'button',
                hint: '总结文本',
                icons: 'resources/images/icon/zongjie.png',
                lockInViewMode: true,
                enableToggle: false,
                separator: false,
            },
            {
                id: 'onExplain',
                text: '解释文本',
                type: 'button',
                hint: '解释文本',
                icons: 'resources/images/icon/jieshi.png',
                lockInViewMode: true,
                enableToggle: false,
                separator: false,
            },
            {
                id: 'onCorrect',
                text: '纠正拼写和语法',
                type: 'button',
                hint: '纠正拼写和语法',
                icons: 'resources/images/icon/jiuzhengxingweixiu.png',
                lockInViewMode: true,
                enableToggle: false,
                separator: false,
            },
            {
                id: 'translate',
                text: '翻译',
                items: [
                    {
                        id: 'translate_to_en',
                        text: '翻译成英文',
                        type: 'button',
                        hint: '翻译成英文',
                        icons: 'resources/images/icon/fanyi1.png',
                        lockInViewMode: true,
                        enableToggle: false,
                        separator: false,
                    },
                    {
                        id: 'translate_to_zh',
                        text: '翻译成中文',
                        type: 'button',
                        hint: '翻译成中文',
                        icons: 'resources/images/icon/fanyi2.png',
                        lockInViewMode: true,
                        enableToggle: false,
                        separator: false,
                    },
                ],
            },
            {
                id: 'clear_history',
                text: '清除对话历史',
                type: 'button',
                hint: '清除对话历史',
                icons: 'resources/images/icon/clean.png',
                lockInViewMode: true,
                enableToggle: false,
                separator: false,
            }
        );
        return settings;
    }

    // The functions in menu
    function getMenuItems(options) {
        console.log('context menu options: ', options);
        console.log(options.type);
        let settings = {
            guid: window.Asc.plugin.guid,
            items: [
                {
                    id: 'YunZhi',
                    text: generateText('YunZhi'),
                    items: [],
                },
            ],
        };
        switch (options.type) {
            case 'Target': {
                if (Asc.plugin.info.editorType === 'word') {
                    settings.items[0].items.push({
                        id: 'onClearHistory',
                        text: generateText('Clear chat history'),
                    });
                } else if (Asc.plugin.info.editorType === 'cell') {
                } else if (Asc.plugin.info.editorType === 'slide') {
                }
                break;
            }
            case 'Selection': {
                if (Asc.plugin.info.editorType === 'word') {
                    settings.items[0].items.push(
                        {
                            id: 'onAnalyse',
                            text: generateText('Analyse text'),
                        },
                        {
                            id: 'onComplete',
                            text: generateText('Complete text'),
                        },
                        {
                            id: 'onGenerateDraft',
                            text: generateText('Generate draft'),
                        },
                        {
                            id: 'onRewrite',
                            text: generateText('Rewrite text'),
                        },
                        {
                            id: 'onShorten',
                            text: generateText('Shorten text'),
                        },
                        {
                            id: 'onExpand',
                            text: generateText('Expand text'),
                        }
                    );
                } else if (Asc.plugin.info.editorType === 'cell') {
                    settings.items[0].items.push(
                        {
                            id: 'onExplainC',
                            text: generateText('Explain cells'),
                        },
                        {
                            id: 'onSummariseC',
                            text: generateText('Summarise cells'),
                        }
                    );
                } else if (Asc.plugin.info.editorType === 'slide') {
                    settings.items[0].items.push({
                        id: 'onOutline',
                        text: generateText('Get outline of a topic'),
                    });
                }
                // Common Functions
                settings.items[0].items.push(
                    {
                        id: 'onSummarize',
                        text: generateText('Summarize text'),
                    },
                    {
                        id: 'onExplain',
                        text: generateText('Explain text'),
                    },
                    {
                        id: 'onCorrect',
                        text: generateText('Correct spelling and grammar'),
                    },
                    {
                        id: 'translate',
                        text: generateText('translate'),
                        items: [
                            {
                                id: 'translate_to_en',
                                text: generateText('translate to English'),
                            },
                            {
                                id: 'translate_to_zh',
                                text: generateText('translate to Chinese'),
                            },
                        ],
                    },
                    {
                        id: 'clear_history',
                        text: generateText('clear chat history'),
                    }
                );
            }
        }
        return settings;
    }

    window.Asc.plugin.attachEvent('onContextMenuShow', function (options) {
        console.log('context menu options: ', options);
        if (!options) return;

        if (options.type === 'Selection' || options.type === 'Target') this.executeMethod('AddContextMenuItem', [getMenuItems(options)]);
    });

    //YunZhi plugin Functions
    window.Asc.plugin.attachToolbarMenuClickEvent('onSummarize', (data) => {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            if (!text && Asc.plugin.info.editorType === 'cell') {
                const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed summarize the selected text.');
                displayMessage(message, 'ai-message', true);
                return;
            }
            const prompt = summarizeContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Summarize the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // clear chat history
    window.Asc.plugin.attachToolbarMenuClickEvent('clear_history', function () {
        clearHistory();
    });

    // explain text in cell
    window.Asc.plugin.attachToolbarMenuClickEvent('onExplainC', function () {
        Asc.plugin.callCommand(
            function () {
                let oWorksheet = Api.GetActiveSheet();
                let selection = oWorksheet.GetSelection();
                let data = selection.GetValue();

                console.log('se', selection.GetValue());
                return data;
            },
            false,
            false,
            function (data) {
                console.log(' get select cell text', data);
                let tableString = formatTable(data);
                const prompt = explainCellPrompt(tableString, langMap.get(lang));
                const request = decoratePrompt(prompt);
                const message = window.Asc.plugin.tr('Explain the selected cells');
                displayMessage(message);
                chatRequest(request);
            }
        );
    });

    // summarise cells
    window.Asc.plugin.attachToolbarMenuClickEvent('onSummariseC', function () {
        Asc.plugin.callCommand(
            function () {
                let oWorksheet = Api.GetActiveSheet();
                let selection = oWorksheet.GetSelection();
                let data = selection.GetValue();

                console.log('se', selection.GetValue());
                return data;
            },
            false,
            false,
            function (data) {
                console.log(' get select cell text', data);
                let tableString = formatTable(data);
                const prompt = summariseCellPrompt(tableString, langMap.get(lang));
                const request = decoratePrompt(prompt);
                const message = window.Asc.plugin.tr('Summarise the selected cells');
                displayMessage(message);
                chatRequest(request);
            }
        );
    });

    // summarize slide
    window.Asc.plugin.attachToolbarMenuClickEvent('onSummariseS', function () {
        Asc.plugin.callCommand(
            function () {
                // Todo: implement the logic to get the current slide content
                return 'success';
            },
            false,
            false,
            function (data) {
                console.log(' get select cell text', data);
            }
        );
    });

    // generate outline of a topic
    window.Asc.plugin.attachToolbarMenuClickEvent('onOutline', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = slideOutlinePrompt(text, 5, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Get outline of a topic');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // generate slides from the outline
    window.Asc.plugin.attachToolbarMenuClickEvent('onGenerateSlides', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (topic) {
            const prompt = slideOutlinePrompt(topic, 5, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Generate slides');

            displayMessage(message);
            YunZhiChatRequest(request, '你是一位优秀的文档助手，请根据用户提供的主题帮助生成需要的文档片段', false, config).then((response) => {
                generateSlidesFromOutline(response.data[0].content);
            });
        });
    });

    // Correct Spelling and Grammar
    window.Asc.plugin.attachToolbarMenuClickEvent('onCorrect', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            if (!text && Asc.plugin.info.editorType === 'cell') {
                const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed correct selected spelling and grammar.');
                displayMessage(message, 'ai-message', true);
                return;
            }
            const prompt = correctContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Correct spelling and grammar');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // generate draft
    window.Asc.plugin.attachToolbarMenuClickEvent('onGenerateDraft', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = generateDraftPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Generate draft');
            displayMessage(message);
            chatRequest(request);
        });
    });

    //summarize text
    window.Asc.plugin.attachToolbarMenuClickEvent('onSummarize', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            if (!text && Asc.plugin.info.editorType === 'cell') {
                const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed summarize the selected text.');
                displayMessage(message, 'ai-message', true);
                return;
            }
            const prompt = summarizeContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Summarize the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // explain text
    window.Asc.plugin.attachToolbarMenuClickEvent('onExplain', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            if (!text && Asc.plugin.info.editorType === 'cell') {
                const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed explain the selected text.');
                displayMessage(message, 'ai-message', true);
                return;
            }
            const prompt = explainContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Explain the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // analyse text
    window.Asc.plugin.attachToolbarMenuClickEvent('onAnalyse', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = analyzeTextPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Analyse the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // complete text
    window.Asc.plugin.attachToolbarMenuClickEvent('onComplete', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = completeTextPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Complete the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // rewrite text
    window.Asc.plugin.attachToolbarMenuClickEvent('onRewrite', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = rewriteContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Rewrite the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // shorten text
    window.Asc.plugin.attachToolbarMenuClickEvent('onShorten', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = shortenContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Shorten the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // expand text
    window.Asc.plugin.attachToolbarMenuClickEvent('onExpand', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = expandContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Expand the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // translate into Chinese
    window.Asc.plugin.attachToolbarMenuClickEvent('translate_to_zh', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            translateHelper(text, 'Chinese');
        });
    });

    // translate into English
    window.Asc.plugin.attachToolbarMenuClickEvent('translate_to_en', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            translateHelper(text, 'English');
        });
    });

    // clear chat history
    window.Asc.plugin.attachContextMenuClickEvent('clear_history', function () {
        clearHistory();
    });

    // explain text in cell
    window.Asc.plugin.attachContextMenuClickEvent('onExplainC', function () {
        Asc.plugin.callCommand(
            function () {
                let oWorksheet = Api.GetActiveSheet();
                let selection = oWorksheet.GetSelection();
                let data = selection.GetValue();

                console.log('se', selection.GetValue());
                return data;
            },
            false,
            false,
            function (data) {
                console.log(' get select cell text', data);
                let tableString = formatTable(data);
                const prompt = explainCellPrompt(tableString, langMap.get(lang));
                const request = decoratePrompt(prompt);
                const message = window.Asc.plugin.tr('Explain the selected cells');
                displayMessage(message);
                chatRequest(request);
            }
        );
    });

    // summarise cells
    window.Asc.plugin.attachContextMenuClickEvent('onSummariseC', function () {
        Asc.plugin.callCommand(
            function () {
                let oWorksheet = Api.GetActiveSheet();
                let selection = oWorksheet.GetSelection();
                let data = selection.GetValue();

                console.log('se', selection.GetValue());
                return data;
            },
            false,
            false,
            function (data) {
                console.log(' get select cell text', data);
                let tableString = formatTable(data);
                const prompt = summariseCellPrompt(tableString, langMap.get(lang));
                const request = decoratePrompt(prompt);
                const message = window.Asc.plugin.tr('Summarise the selected cells');
                displayMessage(message);
                chatRequest(request);
            }
        );
    });

    // summarize slide
    window.Asc.plugin.attachContextMenuClickEvent('onSummariseS', function () {
        Asc.plugin.callCommand(
            function () {
                // Todo: implement the logic to get the current slide content
                return 'success';
            },
            false,
            false,
            function (data) {
                console.log(' get select cell text', data);
            }
        );
    });

    // generate outline of a topic
    window.Asc.plugin.attachContextMenuClickEvent('onOutline', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = slideOutlinePrompt(text, 5, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Get outline of a topic');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // generate slides from the outline
    window.Asc.plugin.attachContextMenuClickEvent('onGenerateSlides', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (topic) {
            const prompt = slideOutlinePrompt(topic, 5, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Generate slides');

            displayMessage(message);
            YunZhiChatRequest(request, '你是一位优秀的文档助手，请根据用户提供的主题帮助生成需要的文档片段', false, config).then((response) => {
                generateSlidesFromOutline(response.data[0].content);
            });
        });
    });

    // Correct Spelling and Grammar
    window.Asc.plugin.attachContextMenuClickEvent('onCorrect', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            if (!text && Asc.plugin.info.editorType === 'cell') {
                const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed correct selected spelling and grammar.');
                displayMessage(message, 'ai-message', true);
                return;
            }
            const prompt = correctContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Correct spelling and grammar');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // generate draft
    window.Asc.plugin.attachContextMenuClickEvent('onGenerateDraft', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = generateDraftPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Generate draft');
            displayMessage(message);
            chatRequest(request);
        });
    });

    //summarize text
    window.Asc.plugin.attachContextMenuClickEvent('onSummarize', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            if (!text && Asc.plugin.info.editorType === 'cell') {
                const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed summarize the selected text.');
                displayMessage(message, 'ai-message', true);
                return;
            }
            const prompt = summarizeContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Summarize the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // explain text
    window.Asc.plugin.attachContextMenuClickEvent('onExplain', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            if (!text && Asc.plugin.info.editorType === 'cell') {
                const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed explain the selected text.');
                displayMessage(message, 'ai-message', true);
                return;
            }
            const prompt = explainContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Explain the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // analyse text
    window.Asc.plugin.attachContextMenuClickEvent('onAnalyse', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = analyzeTextPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Analyse the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // complete text
    window.Asc.plugin.attachContextMenuClickEvent('onComplete', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = completeTextPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Complete the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // rewrite text
    window.Asc.plugin.attachContextMenuClickEvent('onRewrite', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = rewriteContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Rewrite the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // shorten text
    window.Asc.plugin.attachContextMenuClickEvent('onShorten', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = shortenContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Shorten the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // expand text
    window.Asc.plugin.attachContextMenuClickEvent('onExpand', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            const prompt = expandContentPrompt(text, langMap.get(lang));
            const request = decoratePrompt(prompt);
            const message = window.Asc.plugin.tr('Expand the selected text');
            displayMessage(message);
            chatRequest(request);
        });
    });

    // translate into Chinese
    window.Asc.plugin.attachContextMenuClickEvent('translate_to_zh', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            translateHelper(text, 'Chinese');
        });
    });

    // translate into English
    window.Asc.plugin.attachContextMenuClickEvent('translate_to_en', function () {
        window.Asc.plugin.executeMethod('GetSelectedText', null, function (text) {
            translateHelper(text, 'English');
        });
    });

    // Make sure the DOM is fully loaded before querying the DOM elements
    document.addEventListener('DOMContentLoaded', function () {
        // get references to the DOM elements
        messageInput = document.querySelector('#userInput');
        const sendButton = document.querySelector('.send-button');
        const clearButton = document.querySelector('.clear-button');
        stopButton = document.querySelector('.stop-button');
        regenButton = document.querySelector('.regenerate-button');
        // Agent mode now controlled by chat-footer.js via window.chatFooterAgentMode
        // checkValue will be set based on the agent mode: '1'=LLM, '2'=RAG
        function updateCheckValue() {
            if (window.chatFooterAgentMode === '1') {
                checkValue = 'LLM';
            } else if (window.chatFooterAgentMode === '2') {
                checkValue = 'RAG';
            }
        }

        // Update checkValue initially and on any potential changes
        updateCheckValue();
        setInterval(updateCheckValue, 100); // Poll for changes

        // send a message when the user clicks the send button
        function sendMessage() {
            // if the user clicked the send button, delete the last time request
            lastRequest = null;

            const message = messageInput.value;
            if (message.trim() !== '') {
                displayMessage(message);
                messageInput.value = '';
                chatRequest(conversationHistory);
            }
        }

        sendButton.addEventListener('click', sendMessage);
        clearButton.addEventListener('click', clearHistory);
        stopButton.addEventListener('click', stopChatRequest);
        regenButton.addEventListener('click', reSend);

        messageInput.addEventListener('keydown', function (event) {
            if (event.key === 'Enter') {
                event.preventDefault(); // prevent the default behavior of the Enter key
                if (event.shiftKey) {
                    // if the user pressed Shift+Enter, insert a newline character
                    messageInput.value += '\n';
                } else {
                    // if the user only pressed Enter, send the message
                    sendMessage();
                }
            }
        });
    });

    // utils functions for the plugin
    function configSettings(settings) {}

    function formatTable(data) {
        if (!Array.isArray(data)) {
            return 'Input must be an array.';
        }

        let table = '';
        for (let i = 0; i < data.length; i++) {
            let row = '';
            for (let j = 0; j < data[i].length; j++) {
                row += data[i][j] + '\t';
            }
            table += row.trim() + '\n';
        }
        return table.trim();
    }

    function decoratePrompt(prompt) {
        return [
            {
                role: 'user',
                content: prompt,
            },
        ];
    }

    function translateHelper(text, targetLanguage) {
        const str = `translate the selected text to ${targetLanguage}`;
        const message = window.Asc.plugin.tr(str);

        if (!text && Asc.plugin.info.editorType === 'cell') {
            const message = window.Asc.plugin.tr('Sorry, please select text in a cell to proceed translate selected text.');
            displayMessage(message, 'ai-message', true);
            return;
        }
        displayMessage(message);
        const prompt = translatePrompt(text, targetLanguage);
        const request = decoratePrompt(prompt);

        chatRequest(request);
    }

    function getSystemMessage() {
        const language = langMap.get(lang);
        if (Asc.plugin.info.editorType === 'word') {
            return `您是一位优秀的Word文档助手，具备文本分析、内容补全、草稿生成、文本改写等功能。除非用户明确要求使用其他语言，否则应尽量使用${language}回答用户问题。`;
        } else if (Asc.plugin.info.editorType === 'cell') {
            return `您是一位优秀的电子表格助手，能够解释单元格数据、汇总表格内容等。除非用户明确要求使用其他语言，否则应尽量使用${language}回答用户问题。`;
        } else if (Asc.plugin.info.editorType === 'slide') {
            return `您是一位优秀的幻灯片助手，可以提取主题大纲、根据大纲生成幻灯片等。除非用户明确要求使用其他语言，否则应尽量使用${language}回答用户问题。`;
        }
        return `您是一位优秀的助手，除非用户明确要求使用其他语言，否则应尽量使用${language}回答用户问题。`;
    }

    const reSend = () => {
        // delete the last message from the conversation history
        messageHistory.removeChild(messageHistory.lastChild);
        if (lastRequest) {
            // regenerate the last request
            chatRequest(lastRequest);
        } else {
            // regenerate the chat
            chatRequest(conversationHistory);
        }
    };

    const insertIntoDocument = (text) => {
        Asc.scope.insert = text;
        if (Asc.plugin.info.editorType === 'word') {
            Asc.plugin.callCommand(function () {
                let oDocument = Api.GetDocument();
                let oParagraph = Api.CreateParagraph();
                oParagraph.AddText(Asc.scope.insert);
                oDocument.InsertContent([oParagraph]);
            });
        } else if (Asc.plugin.info.editorType === 'cell') {
            Asc.plugin.callCommand(function () {
                let oWorksheet = Api.GetActiveSheet();
                let selection = oWorksheet.GetSelection();
                selection.SetValue(Asc.scope.insert);
            });
        } else if (Asc.plugin.info.editorType === 'slide') {
            Asc.plugin.callCommand(function () {
                let oPresentation = Api.GetPresentation();
                let oSlide = oPresentation.GetCurrentSlide();
                let oShape = Api.CreateShape('rect', 4986000, 2419200, Api.CreateNoFill(), Api.CreateStroke(0, Api.CreateNoFill()));
                oShape.SetPosition(3834000, 3888000);
                let oContent = oShape.GetDocContent();
                oContent.RemoveAllElements();
                let oParagraph = Api.CreateParagraph();
                let oRun = Api.CreateRun();
                let oTextPr = oRun.GetTextPr();
                oTextPr.SetFontSize(50);
                let oFill = Api.CreateSolidFill(Api.CreateRGBColor(0, 0, 0));
                oTextPr.SetTextFill(oFill);
                oParagraph.SetJc('left');
                oRun.AddText(Asc.scope.insert);
                oParagraph.AddElement(oRun);
                oContent.Push(oParagraph);
                oSlide.AddObject(oShape);
            });
        }
    };

    function clearHistory() {
        messageHistory.innerHTML = '';
        conversationHistory = [];
        messageInput.value = '';
    }

    function createAIMessageFrame(pin = false) {
        const messageElement = document.createElement('div');
        messageElement.classList.add('message');
        messageElement.classList.add('ai-message');

        // create an avatar element
        const avatarElement = document.createElement('div');
        const avatarImg = document.createElement('img');
        avatarElement.classList.add('avatar');
        avatarImg.src = 'resources/yunzhi-avatar.png';
        avatarImg.alt = `ai-message avatar`;

        loadElement = document.createElement('div');
        loadElement.classList.add('load-icon');
        loadElement.innerHTML = '&#x1F504;';
        if (pin) {
            loadElement.style.display = 'none';
        }
        avatarElement.appendChild(avatarImg);
        avatarElement.appendChild(loadElement);

        // create a message content element
        const bubbleContentElement = document.createElement('div');
        bubbleContentElement.classList.add('bubble');
        const messageContentElement = document.createElement('div');
        messageContentElement.classList.add('message-content');

        const actions = document.createElement('div');
        actions.classList.add('actions');
        const insertButton = document.createElement('button');
        insertButton.type = 'button';
        insertButton.title = window.Asc.plugin.tr('insert document');
        insertButton.innerHTML = '&#x2398;';
        insertButton.onclick = () => insertIntoDocument(messageContentElement.innerText);

        bubbleContentElement.appendChild(messageContentElement);
        actions.appendChild(insertButton);

        bubbleContentElement.appendChild(actions);

        messageElement.appendChild(avatarElement);
        messageElement.appendChild(bubbleContentElement);

        messageHistory.appendChild(messageElement);

        return messageContentElement;
    }

    function createUserMessageFrame() {
        // create a new message element
        const messageElement = document.createElement('div');
        messageElement.classList.add('message'); // Add a class for user messages
        messageElement.classList.add('user-message'); // Add a class for user messages

        // create an avatar element
        const avatarElement = document.createElement('div');
        const imgElement = document.createElement('img');
        avatarElement.classList.add('avatar');
        imgElement.src = 'resources/user.png';
        imgElement.alt = window.Asc.plugin.tr('user avatar');
        avatarElement.appendChild(imgElement);

        // create a message content element
        const bubbleContentElement = document.createElement('div');
        bubbleContentElement.classList.add('bubble');
        const messageContentElement = document.createElement('div');
        messageContentElement.classList.add('message-content');
        bubbleContentElement.appendChild(messageContentElement);

        // add the message element to the message history
        messageHistory.appendChild(messageElement);
        messageElement.appendChild(bubbleContentElement);
        messageElement.appendChild(avatarElement);

        //  scroll to the bottom of the message history
        messageHistory.scrollTop = messageHistory.scrollHeight;

        return messageContentElement;
    }

    const displayMessage = function (message, role = 'user', pin = false) {
        message = message.replace(/^"|"$/g, ''); // remove surrounding quotes
        message = message.replace(/\\n/g, '\n'); // replace \n with newline characters
        // add the message to the conversation history
        conversationHistory.push({ role: role, content: message });

        const messageContentElement = role === 'user' ? createUserMessageFrame() : createAIMessageFrame(pin);

        // split the message into lines and create a text node for each line
        const lines = message.split('\n');
        for (const line of lines) {
            const textNode = document.createTextNode(line);
            messageContentElement.appendChild(textNode);
            messageContentElement.appendChild(document.createElement('br'));
        }
    };
    function startChat(request) {
        lastRequest = request;
        regenButton.style.display = 'none';
        stopButton.style.display = 'block';
    }
    function endChat() {
        regenButton.style.display = 'block';
        if (loadElement) {
            loadElement.style.display = 'none';
        }
        stopButton.style.display = 'none';
    }
    function filterThinkTags(html) {
        // 正则表达式匹配 <think> 标签及其内部内容（含属性、换行符等特殊情况）
        const thinkTagRegex = /<think[^>]*>[\s\S]*?<\/think>/gi;
        return html.replace(thinkTagRegex, '');
    }
    function chatRequest(request) {
        startChat(request);
        let prompt = request;
        let systemMessage = getSystemMessage();
        abortController = new AbortController();
        console.log('prompt: ', prompt);
        YunZhiChatRequest(prompt, systemMessage, true, {}, abortController.signal, checkValue)
            .then((reader) => {
                console.log('SSE request started');
                let messageContentElement = createAIMessageFrame();
                displaySSEMessage(reader, messageContentElement, null)
                    .then((result) => {
                        console.log('Chat completed, result :', result);
                        conversationHistory.push({ role: 'assistant', content: result.response });
                        endChat();
                        console.log('current history: ', conversationHistory);
                    })
                    .catch((error) => {
                        endChat();
                        if (error.name === 'AbortError') {
                            console.log('The SSE request was stopped manually');
                        } else {
                            console.error('An error occurred: ', error);
                        }
                    });
            })
            .catch((err) => {
                endChat();
                let message = window.Asc.plugin.tr(err.message);
                displayMessage(message, 'ai-message', true);
                console.log('SSE request failed', err);
            });
    }

    function stopChatRequest() {
        if (abortController) {
            abortController.abort(); // stop SSE request
            loadElement.style.display = 'none';
        }
    }

    function generateText(text) {
        let lang = window.Asc.plugin.info.lang.substring(0, 2);
        return {
            en: text,
            [lang]: window.Asc.plugin.tr(text),
        };
    }
})(window, undefined);
