import request from '@/config/axios'
// 个人知识库查询v1/kownledge/klCloudIndividual/page
export const getCloudPage = (params) => {
  return request.get({ url: '/v1/kownledge/klCloudIndividual/page',  params })
}

// 新建文件夹/v1/kownledge/klCloudIndividual/save
export const createFolder = (data) => {
  return request.postOriginal({ url: '/v1/kownledge/klCloudIndividual/save', data })
}

// 重命名 /v1/kownledge/klCloudIndividual/update
export const updateFolder = (data) => {
    return request.putOriginal({ url: '/v1/kownledge/klCloudIndividual/update', data  })
}

// 删除 /v1/kownledge/klCloudIndividual/delete
export const deleteFolder = (id) => {
    return request.deleteOriginal({ url: '/v1/kownledge/klCloudIndividual/delete?id=' + id })
}

// 移动 /v1/kownledge/klCloudIndividual/movement
export const movementFolder = (params) => {
    return request.get({ url: '/v1/kownledge/klCloudIndividual/movement', params })
}

// 授权/admin-api/v1/kownledge/individualRole/save
export const saveDocPersonal = (data) => {
    return request.postOriginal({ url: '/v1/kownledge/individualRole/save', data })
}

// http://cloud.frp.yn.asqy.net/admin-api/v1/kownledge/individualRole/getByIndividualId 根据知识文件id获取角色id列表（get）
export const getIndividualRole = (params) => {
    return request.getOriginal({ url: '/v1/kownledge/individualRole/getByIndividualId', params })
}

// 删除权限与角色关系/kownledge/individualRole/deleteByid
export const deleteCloudButtonRole = (klCloudId) => {
  return request.delete({ url: `/v1/kownledge/individualRole/deleteByid/${klCloudId}` })
}

// 获取按钮权限 v1/kownledge/individualRole
export const getCloudButtonRole = (params) => {
  return request.get({ url: `/v1/kownledge/individualRole`, params })
}

// 保存按钮权限 v1/kownledge/individualRole
export const saveCloudButtonRole = (data) => {
  return request.post({ url: `/v1/kownledge/individualRole/save`, data })
}

// 根据权限查询列表/kownledge/klCloudIndividual/listByrole
export const getListByRole = (params) => {
  return request.get({ url: '/v1/kownledge/klCloudIndividual/listByrole', params })
}

// 文件下载/v1/kownledge/common/FileDownload
export function knowledgeFileDownload(data) {
  return request.postOriginal({
    url: '/v1/kownledge/common/FileDownload',
    contentType: 'application/json',
    responseType: 'blob',
    params: data
  })
}

// 获取当前登陆人知识库 /v1/kownledge/klCloudUser
export const getCloudUser = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudUser`, params })
}

// 后台解析（带配置）/v1/kownledge/common/chunkByklCloudAnalysisId
export const getCloudAnalysisBackConfig = (params) => {
  return request.getOriginal({ url: `/v1/kownledge/common/chunkByklCloudAnalysisId`, params })
}

// 解析结果（带配置） /chunkklCloudAnalysisIdResults
export const getCloudAnalysisBackResultsConfig = (params) => {
  return request.get({ url: `/v1/kownledge/common/chunkklCloudAnalysisIdResults`, params })
}
