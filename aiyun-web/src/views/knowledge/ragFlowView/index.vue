<template>
	<div class="iframe-container">
		<iframe :src="viewUrl" frameborder="0" style="width: 100%; height: calc(100vh - 160px)">
		</iframe>
	</div>

</template>
<script setup>
    import { useUserStore } from '@/store/modules/user'
    const userStore = useUserStore()
    const apikeys = userStore.getAppConfig
    import * as FolderAPI from '@/api/knowledge/docmangement'

  const { query, path } = useRoute() // 查询参数
  defineOptions({ name: 'SystemDept' })
  const viewUrl = ref('')

//   userEmail userPwd
  
  /** 初始化 **/
  onMounted(async () => {
		if (path.includes('rag-')) {
            const userRag = await FolderAPI.getCloudUser()
            console.log(userRag);
            const { email, password } = userRag
			const  pathArr = path.split('rag-')
			let newPath = ''
			pathArr.forEach((element, index) => {
				if (index !== 0) {
					newPath += `/${element}`
				}
			});
            viewUrl.value = `${apikeys.ragflow_url}/link?topath=${pathArr[1]}&useremail=${email}&userpwd=${password}` 
            return
			viewUrl.value = snailToken ? `${apikeys.snail_url}#${newPath} `:`${apikeys.snail_url}#/loginsso?redirect=${newPath}`
		}
  })
</script>

<style lang="scss" scoped>
	.iframe-container {
		width: 100%;
		height: 100%;
		background-color: #fff;
	}
</style>
  