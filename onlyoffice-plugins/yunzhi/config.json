{"name": "Yun<PERSON><PERSON>", "nameLocale": {"zh": "云智"}, "guid": "asc.{4B5EFD7F-1234-1EF4-FEAB-BFBF2E1D86AC}", "baseUrl": "", "version": "1.0.0", "offered": "liutao", "variations": [{"description": "Using AI assistants based on big models such as DeepSeek R1 and <PERSON><PERSON><PERSON>, easily generate high-quality articles, explain complex spreadsheet data, create PPT outlines, and more.", "descriptionLocale": {"zh": "使用基于DeepSeek R1、千问等大模型的 AI 助手，轻松生成高质量的文章、解释复杂的电子表格数据、创建 PPT 大纲等."}, "url": "index.html", "icons": ["resources/light/icon.png", "resources/light/<EMAIL>"], "icons2": [{"style": "light", "100%": {"normal": "resources/light/icon.png"}, "125%": {"normal": "resources/light/<EMAIL>"}, "150%": {"normal": "resources/light/<EMAIL>"}, "175%": {"normal": "resources/light/<EMAIL>"}, "200%": {"normal": "resources/light/<EMAIL>"}}, {"style": "dark", "100%": {"normal": "resources/dark/icon.png"}, "125%": {"normal": "resources/dark/<EMAIL>"}, "150%": {"normal": "resources/dark/<EMAIL>"}, "175%": {"normal": "resources/dark/<EMAIL>"}, "200%": {"normal": "resources/dark/<EMAIL>"}}], "store": {"background": {"light": "#7c84e8", "dark": "#5f55af"}, "screenshots": ["resources/store/screenshots/screen_1.png", "resources/store/screenshots/screen_2.png", "resources/store/screenshots/screen_3.png", "resources/store/screenshots/screen_4.png", "resources/store/screenshots/screen_5.png", "resources/store/screenshots/screen_6.png", "resources/store/screenshots/screen_7.png", "resources/store/screenshots/screen_8.png", "resources/store/screenshots/screen_9.png", "resources/store/screenshots/screen_10.png", "resources/store/screenshots/screen_11.png"], "icons": {"light": "resources/store/icons", "dark": "resources/store/icons"}, "categories": ["specAbilities", "work"]}, "isViewer": true, "EditorsSupport": ["word", "cell", "slide"], "isVisual": true, "isModal": true, "isInsideMode": true, "initDataType": "none", "initData": "", "isUpdateOleOnResize": true, "buttons": [], "events": ["onToolbarMenuShow", "onToolbarMenuClick", "onContextMenuShow", "onContextMenuClick"]}]}