<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item label="所属平台" prop="platform">
        <!-- <el-select v-model="formData.platform" placeholder="请选择所属平台">
          <el-option label="Qwen" value="Qwen" />
          <el-option label="Ollama" value="Ollama" />
          <el-option label="Deepseek" value="Deepseek" />
        </el-select> -->
        <common-select
          v-model="formData.platform"
          placeholder="请选择所属平台"
        />
      </el-form-item>
      <el-form-item label="模型名称" prop="modelName">
        <el-input v-model="formData.modelName" placeholder="请输入模型名称" />
      </el-form-item>
      <el-form-item label="模型标识" prop="modelId">
        <el-input v-model="formData.modelId" placeholder="请输入模型标识" />
      </el-form-item>
      <el-form-item label="API地址" prop="apiUrl">
        <el-input v-model="formData.apiUrl" placeholder="请输入API地址" />
      </el-form-item>
      <el-form-item label="API密钥" prop="tokenId">
        <el-input v-model="formData.tokenId" placeholder="请输入API密钥" />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" controls-position="right" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio-button value="0">启用</el-radio-button>
          <el-radio-button value="1">禁用</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="温度参数" prop="heat">
        <el-input-number 
          v-model="formData.heat" 
          :min="0" 
          :max="2" 
          :precision="2" 
          :step="0.1"
          controls-position="right" />
      </el-form-item>
      <el-form-item label="Token数" prop="tokenNum">
        <el-input-number v-model="formData.tokenNum" :min="1" controls-position="right" />
      </el-form-item>
      <el-form-item label="上下文数量" prop="contextNum">
        <el-input-number v-model="formData.contextNum" :min="1" controls-position="right" />
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input 
          v-model="formData.remark" 
          :maxlength="80" 
          :rows="3" 
          show-word-limit type="textarea"
          placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup>
import * as ModelApi from '@/api/knowledge/aiSettings/model'
import CommonSelect from '../components/CommonSelect.vue'


defineOptions({ name: 'ModelSettingForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  platform: undefined,
  modelName: undefined,
  modelId: undefined,
  tokenId: undefined,
  apiUrl: undefined,
  sort: undefined,
  status: undefined,
  remark: undefined,
  heat: 1.00, // 默认温度参数
  tokenNum: 1, // 默认Token数
  contextNum: 1, // 默认上下文数量
})

const formRules = reactive({
  platform: [{ required: true, message: '所属平台不能为空', trigger: 'blur' }],
  modelName: [{ required: true, message: '模型名称不能为空', trigger: 'blur' }],
  modelId: [{ required: true, message: '模型标识不能为空', trigger: 'blur' }],
  // tokenId: [{ required: true, message: 'API密钥不能为空', trigger: 'blur' }],
  apiUrl: [{ required: true, message: 'API地址不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type, id) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增' : '修改'
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ModelApi.getArrangeModel(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return

  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await ModelApi.addArrangeModel(data)
      message.success(t('common.createSuccess'))
    } else {
      await ModelApi.updateArrangeModel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    platform: undefined,
    modelName: undefined,
    modelId: undefined,
    tokenId: undefined,
    apiUrl: undefined,
    sort: undefined,
    status: undefined,
    remark: undefined,
    heat: 1.00, // 重置为默认温度参数
    tokenNum: 1, // 重置为默认Token数
    contextNum: 1, // 重置为默认上下文数量
  }
  formRef.value?.resetFields()
}
</script>