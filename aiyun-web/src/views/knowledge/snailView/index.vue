<template>
	<div class="iframe-container">
		<iframe :src="viewUrl" frameborder="0" style="width: 100%; height: calc(100vh - 60px)">
		</iframe>
	</div>

</template>
<script setup>
	import { useUserStore } from '@/store/modules/user'
	const userStore = useUserStore()
	const apikeys = userStore.getAppConfig

  const { query, path } = useRoute() // 查询参数
  defineOptions({ name: 'SystemDept' })
  const viewUrl = ref('')
  
  /** 初始化 **/
  onMounted(async () => {
		console.log(localStorage.getItem('token'));
		const snailToken = localStorage.getItem('token')
		if (path.includes('-')) {
			const  pathArr = path.split('-')
			let newPath = ''
			pathArr.forEach((element, index) => {
				if (index !== 0) {
					newPath += `/${element}`
				}
			});
			viewUrl.value = snailToken ? `${apikeys.snail_url}#${newPath} `:`${apikeys.snail_url}#/loginsso?redirect=${newPath}`
			console.log(viewUrl.value);
		}
  })
</script>

<style lang="scss" scoped>
	.iframe-container {
		width: 100%;
		height: 100%;
		background-color: #fff;
	}
</style>
  