import request from '@/config/axios'

// 查询文件列表分页 v1/kownledge/klCloud/page
export const getCloudPage = (params) => {
  return request.get({ url: '/v1/kownledge/klCloud/page', params })
}
// 分页查询 带权限listByrole/listByrole
export const getListByRole = (params) => {
  return request.get({ url: '/v1/kownledge/klCloud/listByrole', params })
}
// 查询文件列表api/knowledge/docmangement
export const getCloudList = (params) => {
  return request.get({ url: '/v1/kownledge/klCloud/list', params })
}

// 新建文件夹/v1/kownledge/klCloud/save
export const createFolder = (data) => {
  return request.postOriginal({ url: '/v1/kownledge/klCloud/save', data })
}
// 空白文件 /common/uploadinit
export const uploadInit = (params) => {
  return request.get({ url: '/v1/kownledge/common/uploadinit', params })
}

// 新建文件 多个/admin-api/v1/kownledge/klCloud/uploadSave
export const uploadFolderFileMore = (data) => {
  return request.upload({ url: '/v1/kownledge/klCloud/uploadSave', data })
}

// 重命名 /v1/kownledge/klCloud/update
export const updateFolder = (data) => {
  return request.putOriginal({ url: '/v1/kownledge/klCloud/update', data })
}

// 删除 /v1/kownledge/klCloud/delete
export const deleteFolder = (id) => {
  return request.deleteOriginal({ url: '/v1/kownledge/klCloud/delete?id=' + id })
}

// 文件上传v1/kownledge/common/upload
export const uploadFolderFile = (data) => {
  return request.upload({ url: '/v1/kownledge/common/upload', data })
}

// 喜欢 /v1/kownledge/KlCloudLikes/save
export const saveFolderLikes = (data) => {
  return request.postOriginal({ url: '/v1/kownledge/KlCloudLikes/save', data })
}

// 取消喜欢 /v1/kownledge/KlCloudLikes/{id}
export const deleteFolderLikes = (id) => {
  return request.delete({ url: `/v1/kownledge/KlCloudLikes/${id}` })
}

// 收藏 v1/kownledge/klCloudFavorites/save
export const saveFolderFavorites = (data) => {
  return request.postOriginal({ url: '/v1/kownledge/klCloudFavorites/save', data })
}

// 取消收藏/v1/kownledge/klCloudFavorites/{id}
export const deleteFolderFavorites = (id) => {
  return request.delete({ url: `/v1/kownledge/klCloudFavorites/${id}` })
}
// 推荐 v1/kownledge/klCloudPushes/save
export const saveFolderPushes = (data) => {
  return request.postOriginal({ url: '/v1/kownledge/klCloudPushes/save', data })
}

// 取消推荐/v1/kownledge/klCloudPushes/{klCloudId}
export const deleteFolderPushes = (klCloudId) => {
  return request.delete({ url: `/v1/kownledge/klCloudPushes/${klCloudId}` })
}

// 移动 /v1/kownledge/klCloud/movement
export const movementFolder = (params) => {
  return request.get({ url: '/v1/kownledge/klCloud/movement', params })
}

// 下载附件/admin-api/hours/project/project/supplierFileDownload
export function supplierFileDownload(data) {
  return request.post({
    url: '/hours/project/project/supplierFileDownload',
    contentType: 'application/json',
    responseType: 'blob',
    params: data
  })
}

// 解析设置查询  /v1/kownledge/cloudAnalysis/{id}
export const getCloudAnalysis = (id) => {
  return request.get({ url: `/v1/kownledge/cloudAnalysis/${id}` })
}

// 创建解析设置/v1/kownledge/cloudAnalysis/save
export const createCloudAnalysis = (data) => {
  return request.postOriginal({ url: '/v1/kownledge/cloudAnalysis/save', data })
}

// 修改解析设置/v1/kownledge/cloudAnalysis
export const updateCloudAnalysis = (data) => {
  return request.putOriginal({ url: '/v1/kownledge/cloudAnalysis', data })
}

// 分享 /v1/kownledge/klCloudShare/save

// 开启分享/v1/kownledge/klCloudShare/enable/{klCloudId}
export const createCloudShare = (id) => {
  return request.getOriginal({ url: `/v1/kownledge/klCloudShare/enable/${id}` })
}

// 取消分享 /v1/kownledge/klCloudShare/klCloudId/{klCloudId}
export const deleteCloudShare = (klCloudId) => {
  return request.delete({ url: `/v1/kownledge/klCloudShare/klCloudId/${klCloudId}` })
}

// 保存分享 v1/kownledge/klCloudShare/save
export const saveCloudShare = (data) => {
  return request.post({ url: `/v1/kownledge/klCloudShare/save`, data })
}

// 分享给我的文件 v1/kownledge/klCloudShare/shareWithMe
export const shareWithMe = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudShare/shareWithMe`, params })
}

// 删除分享
export const deleteShareById = (shareId) => {
  return request.delete({ url: `/v1/kownledge/klCloudShare/${shareId}` })
}

// 我分享的列表 /v1/kownledge/klCloudShare/page
export const shareMe = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudShare/page`, params })
}

// 我的收藏分页 kownledge/klCloudFavorites/page
export const getCloudFavoritesPage = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudFavorites/page`, params })
}

// 我的推荐分页 /kownledge/klCloudPushes/page
export const getCloudPushesPage = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudPushes/page`, params })
}

// 我的点赞 /kownledge/KlCloudLikes/page
export const getCloudLikesPage = (params) => {
  return request.get({ url: `/v1/kownledge/KlCloudLikes/page`, params })
}

// 获取当前登陆人知识库 /v1/kownledge/klCloudUser
export const getCloudUser = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudUser`, params })
}


// 文件下载/v1/kownledge/common/FileDownload
export function knowledgeFileDownload(data) {
  return request.postOriginal({
    url: '/v1/kownledge/common/FileDownload',
    contentType: 'application/json',
    responseType: 'blob',
    params: data
  })
}

// 专家文档 v1/kownledge/klCloud/generatePromotionReports?id=145
export const generatePromotionReports = (id) => {
  return request.getOriginal({ url: `/v1/kownledge/klCloud/generatePromotionReports?id=${id}`, request_timeout: 300000 })
}


// 保存按钮权限 v1/kownledge/klCloudButtonRole
export const saveCloudButtonRole = (data) => {
  return request.post({ url: `/v1/kownledge/klCloudButtonRole`, data })
}

// 保存按钮权限 v1/kownledge/klCloudButtonRole
export const getCloudButtonRole = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudButtonRole`, params })
}

// 查询角色
export const getUserRole = async () => {
  return await request.get({ url: '/system/role/getUserRole' })
}

// 删除权限与角色关系v1/kownledge/klCloudButtonRole/id/{id}
export const deleteCloudButtonRole = (klCloudId) => {
  return request.delete({ url: `/v1/kownledge/klCloudButtonRole/deleteByid/${klCloudId}` })
}

// 个人知识库查询 /klCloudIndividual/getMyIndividual
export const getMyIndividual = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudIndividual/getMyIndividual`, params })
}

// 获取标签 /kownledge/klCloudLabel/page
export const getCloudLabelPage = (params) => {
  return request.get({ url: `/v1/kownledge/klCloudLabel/page`, params })
}

// 后台解析 v1/kownledge/common/chunk
export const getCloudAnalysisBack = (params) => {
  return request.post({ url: `/v1/kownledge/common/chunk`, params, timeout: 1800000 })
}

// 解析结果/chunkResults
export const getCloudAnalysisBackResults = (params) => {
  return request.get({ url: `/v1/kownledge/common/chunkResults`, params })
}

// 后台解析（带配置）/v1/kownledge/common/chunkByklCloudAnalysisId
export const getCloudAnalysisBackConfig = (params) => {
  return request.getOriginal({ url: `/v1/kownledge/common/chunkByklCloudAnalysisId`, params, timeout: 1800000 })
}

// 解析结果（带配置） /chunkklCloudAnalysisIdResults
export const getCloudAnalysisBackResultsConfig = (params) => {
  return request.get({ url: `/v1/kownledge/common/chunkklCloudAnalysisIdResults`, params })
}

// 新建知识模型-知识库i v1/kownledge/klDataset
export const createKlDataset = (data) => {
  return request.post({ url: `/v1/kownledge/klDataset`, data })
}

// 修改 知识模型-知识库 v1/kownledge/klDataset
export const updateKlDataset = (data) => {
  return request.put({ url: `/v1/kownledge/klDataset`, data })
}

// 删除只是模型-知识库 v1/kownledge/klDataset/{id}
export const deleteKlDataset = (id) => {
  return request.delete({ url: `/v1/kownledge/klDataset/${id}` })
}

// 查询知识模型知识库 /v1/kownledge/klDataset
export const getKlDataset = (params) => {
  return request.get({ url: `/v1/kownledge/klDataset`, params })
}


// 查询单个知识模型知识库 v1/kownledge/klDataset/{id}
export const getKlDatasetById = (id) => {
  return request.get({ url: `/v1/kownledge/klDataset/${id}` })
}

// 查询知识库（管理员）v1/kownledge/klDataset
export const getKlDatasetAdmin = (params) => {
  return request.get({ url: `/v1/kownledge/klDataset`, params })
}

// 查询个人知识库 /v1/kownledge/klDataset/getKlDatasetByPermission
export const getKlDatasetPermission = (params) => {
  return request.get({ url: `/v1/kownledge/klDataset/getKlDatasetByPermission`, params })
}


// 知识库权限新增 /v1/kownledge/klDatasetPermission
export const createKlDatasetPermission = (data) => {
  return request.post({ url: `/v1/kownledge/klDatasetPermission`, data })
}

// 查询单个 知识库权限v1/kownledge/klDatasetPermission
export const getKlDatasetPermissionById = (params) => {
  return request.get({ url: `/v1/kownledge/klDatasetPermission`, params })
}

//查询赋权按钮列表 - 知识库权限管理信息 /v1/kownledge/klDatasetPermission/getUserKlDatasetPermission
export const getUserKlDatasetPermission = (params) => {
  return request.get({ url: `/v1/kownledge/klDatasetPermission/getUserKlDatasetPermission`, params })
}

// 查询agentlist /admin-api/v1/kownledge/common/agentList
export const getAgentList = (params) => {
  return request.get({ url: `/v1/kownledge/common/agentList`, params })
}
