<template>
  <el-dialog 
    v-model="dialogVisible" 
    :scroll="true"
    :fullscreen="true"
    :destroy-on-close="true"
    :title="dialogTitle">
    <!-- <div class="content" v-html="htmlContent">
    </div> -->
    <div v-loading="loading" element-loading-text="生成中，请稍候...">
      <iframe :src="viewUrl"  frameborder="0" style="width: 100%; height: calc(100vh - 60px)">
      </iframe>
    </div>
  </el-dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as FolderAPI from '@/api/knowledge/docmangement'
import { useUserStore } from '@/store/modules/user'
import { generatePromotionReports } from '@/api/home/<USER>'
import mermaid from 'mermaid';

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'ReportView' })

const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const emit = defineEmits(['success'])

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const dialogType = ref('')

const htmlContent = ref('')

const mermaidCode = ref(`
graph TD
    A[材料初审] --> B{形式审查}
    B -->|通过| C[专家盲审]
    B -->|不通过| D[退回补正]
    C --> E{评审委员会终审}
    E -->|通过| F[公示]
    E -->|不通过| G[申诉通道]
`)

mermaid.initialize({ 
	startOnLoad: true,
	themeVariables: { primaryColor: '#fff' }
});

const responseKey = ref()
const timer = ref()
const loading = ref(true)
const open = async(row) => {
  const id = row.id
  dialogVisible.value = true
  loading.value = true
  dialogTitle.value = '查看报告'
  const response = await generatePromotionReports({
    content: row?.content,
    loginUserid: userStore.getUser.id,
  })
  responseKey.value = response
  timer.value = setInterval(async () => {
    const result = await generatePromotionReports({
      content: row?.content,
      loginUserid: userStore.getUser.id,
      key: responseKey.value
    })
    const { status } = JSON.parse(result)
    // 成功
    if (status === 1) {
      clearInterval(timer.value)
      const baseUrl = 'http://127.0.0.1:48080'
      viewUrl.value = `${baseUrl}/admin-api/v1/kownledge/klCloud/getReports?key=${responseKey.value}`
      loading.value = false
    } else if(status === 2) {
      message.error('生成失败')
      clearInterval(timer.value)
    }
  }, 10000)
  
	// // 获取当前网站的地址
	// const baseUrl = window.location.origin
  // // const baseUrl = 'http://192.168.0.221:48082'
  // // const baseUrl = 'http://aiyun.frp.yn.asqy.net'
	// viewUrl.value = `${baseUrl}:48080/admin-api/v1/kownledge/klCloud/generatePromotionReports?id=${id}&loginUserid=${userStore.getUser.id}`
	// // const response = await FolderAPI.generatePromotionReports(id)
  // // htmlContent.value = response
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const svgCode = ref('')
const viewUrl = ref('')

// 监听 htmlContent 变化并重新渲染 Mermaid
// watchEffect(() => {
//   if (htmlContent.value) {
//     // 确保 DOM 更新完成
//     nextTick(() => {
//       mermaid.init(undefined, '.mermaid');
//     });
//   }
// });

onMounted(async() => {
	// mermaid.initialize({ startOnLoad: true });
	// const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`
  //   // 渲染图表
	// const { svg } = await mermaid.render(id, mermaidCode.value)
	// svgCode.value = svg
})


</script>

<style scoped lang="scss">
.content {
    font-family: "微软雅黑", sans-serif;
    line-height: 1.6;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}
  
</style>
