<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { VueFlow, useVueFlow, Panel, Node, Edge, EdgeMouseEvent } from '@vue-flow/core';
// import { Controls } from "@vue-flow/controls";
import { MiniMap } from "@vue-flow/minimap";
import { Background } from "@vue-flow/background";
import { ElMessage } from 'element-plus';
import { BaseNodeData, StartNodeData, LLMNodeData, KBNodeData, TemplateNodeData, HttpNodeData, SearchNodeData, } from '@/types/aiModel';

import '@vue-flow/core/dist/style.css';
import '@vue-flow/core/dist/theme-default.css';

import StartNode from './components/StartNode.vue';
import EndNode from './components/EndNode.vue';
import LLMNode from './components/LLMNode.vue';
import EdgeWithButton from './components/EdgeWithButton.vue';
import KnowledgeBaseNode from './components/KnowledgeBaseNode.vue';
import TemplateNode from './components/TemplateNode.vue';
import HttpNode from './components/HttpNode.vue';
import SearchNode from './components/SearchNode.vue';
import ParallelNode from './components/ParallelNode.vue';
import LoopNode from './components/LoopNode.vue';

const {
  onConnect,
  onNodeDragStop,
  addNodes,
  addEdges,
  setNodes,
  setEdges,
  setViewport,
  getViewport,
  project,
  updateNodeData
} = useVueFlow({
  defaultViewport: { x: 0, y: 0, zoom: 0.5 },
  defaultEdgeOptions: {
    animated: true,
    style: { stroke: '#409eff' }
  }
});

const nodeTypes = {
  start: markRaw(StartNode),
  end: markRaw(EndNode),
  llm: markRaw(LLMNode),
  kb: markRaw(KnowledgeBaseNode),
  template: markRaw(TemplateNode),
  http: markRaw(HttpNode),
  search: markRaw(SearchNode),
  parallel: markRaw(ParallelNode),
  loop: markRaw(LoopNode),
};

const nodes = ref<Node[]>([]);
const edges = ref<Edge[]>([]);

const onDrop = (event: DragEvent) => {
  event.preventDefault();
  event.stopPropagation();
  
  if (!event.dataTransfer) return;

  const type = event.dataTransfer.getData('application/vueflow-type');
  const label = event.dataTransfer.getData('application/vueflow-label');

  if (typeof type === 'undefined' || !type) return;

  const flowElement = document.querySelector('.flow-canvas') as HTMLDivElement;
  if (!flowElement) return;

  const flowRect = flowElement.getBoundingClientRect();
  
  const position = project({
    x: event.clientX - flowRect.left,
    y: event.clientY - flowRect.top,
  });

  let nodeData: BaseNodeData = {
    label,
    inputParams: [],
    outputParams: [],
  };

  switch (type) {
    case 'start':
      nodeData = {
        ...nodeData,
        description: '',
      } as StartNodeData;
      break;
    case 'llm':
      nodeData = {
        ...nodeData,
        model: '',
        apiKey: '',
        temperature: 0.7,
        maxTokens: 2048,
      } as LLMNodeData;
      break;
    case 'kb':
      nodeData = {
        ...nodeData,
        kbId: '',
        kbName: '',
        similarity: 0.7,
        topK: 3,
      } as KBNodeData;
      break;
    case 'template':
      nodeData = {
        ...nodeData,
        template: '',
      } as TemplateNodeData;
      break;
    case 'http':
      nodeData = {
        ...nodeData,
        url: '',
        method: 'GET',
        headers: {},
      } as HttpNodeData;
      break;
    case 'search':
      nodeData = {
        ...nodeData,
        engine: '',
        apiKey: '',
        topK: 3,
      } as SearchNodeData;
      break;
    case 'parallel':
      nodeData = {
        ...nodeData,
        label: '并行节点',
        inputParams: [],
        outputParams: [],
      };
      break;
    case 'loop':
      nodeData = {
        ...nodeData,
        label: '循环节点',
        maxIterations: 10,
        condition: '',
        inputParams: [],
        outputParams: [],
      };
      break;
  }

  const newNode = {
    id: `${type}-${Date.now()}`,
    type,
    position,
    data: nodeData,
  };

  addNodes([newNode]);
};

const onNodeClick = (event: any) => {
  console.log('Node clicked:', event);
};

const selectedEdgeId = ref<string >('');

const handleEdgeClick = (event: EdgeMouseEvent) => {
  const edge = event.edge;
  console.log('Edge clicked:', edge);
  if (edge && edge.id) {
    console.log('Edge clicked:', edge.id);
    selectedEdgeId.value = edge.id;
  } else {
    console.error('Invalid edge object:', edge);
  }
};

const handleNodeChange = (newNodes: Node[]) => {
  nodes.value = newNodes;
};

const handleNodeDataChange = (nodeId: string, newData: any) => {
  console.log('Node data changed:', nodeId, newData);
  
  const nodeIndex = nodes.value.findIndex(node => node.id === nodeId);
  if (nodeIndex !== -1) {
    const updatedNode = {
      ...nodes.value[nodeIndex],
      data: newData
    };
    const newNodes = [...nodes.value];
    newNodes[nodeIndex] = updatedNode;
    nodes.value = newNodes;
  }
};

const saveFlow = () => {
  // 构建树形结构的辅助函数
  const buildTree = (nodeId: string, visited = new Set<string>()) => {
    if (visited.has(nodeId)) return null; // 防止循环引用
    visited.add(nodeId);

    const node = nodes.value.find(n => n.id === nodeId);
    if (!node) return null;

    const childEdges = edges.value.filter(e => e.source === nodeId);
    const children = childEdges
      .map(edge => buildTree(edge.target, new Set(visited)))
      .filter(child => child !== null);

    return {
      id: node.id,
      type: node.type,
      data: node.data,
      children: children
    };
  };

  // 生成EL表达式的辅助函数
  const generateExpression = (node: any): string => {
    if (!node) return '';

    // 根据节点类型生成表达式
    let expression = '';
    switch (node.type) {
      case 'start':
        expression = 'START';
        break;
      case 'end':
        expression = 'END';
        break;
      case 'llm':
        expression = `LLM(model="${node.data.model}", temperature=${node.data.temperature})`;
        break;
      case 'kb':
        expression = `KB(id="${node.data.kbId}", topK=${node.data.topK})`;
        break;
      case 'template':
        expression = `TEMPLATE("${node.data.template}")`;
        break;
      case 'http':
        expression = `HTTP(url="${node.data.url}", method="${node.data.method}")`;
        break;
      case 'search':
        expression = `SEARCH(engine="${node.data.engine}", topK=${node.data.topK})`;
        break;
      case 'parallel':
        expression = 'WHEN';
        break;
      case 'loop':
        expression = `LOOP(maxIterations=${node.data.maxIterations}, condition="${node.data.condition}")`;
        break;
      default:
        expression = node.type.toUpperCase();
    }

    // 处理子节点
    if (node.children && node.children.length > 0) {
      if (node.type === 'parallel') {
        // 并行节点的子节点用 WHEN 包裹，并用逗号分隔
        const childExpressions = node.children.map(child => generateExpression(child));
        expression += `(${childExpressions.join(', ')})`;
      } else if (node.type === 'loop') {
        // 循环节点的子节点用 * 连接
        const childExpressions = node.children.map(child => generateExpression(child));
        expression += `(${childExpressions.join(' * ')})`;
      } else {
        // 其他节点的子节点直接连接，不生成 THEN
        const childExpressions = node.children.map(child => generateExpression(child));
        expression = `${expression}, ${childExpressions.join(', ')}`;
      }
    }

    return expression;
  };

  // 找到开始节点
  const startNode = nodes.value.find(node => node.type === 'start');
  
  // 构建树形结构
  const tree = startNode ? buildTree(startNode.id) : null;

  // 生成EL表达式
  const expression = tree ? generateExpression(tree) : '';

  // 保存原始数据、树形结构和EL表达式
  const flow = {
    original: {
      nodes: nodes.value,
      edges: edges.value,
    },
    tree: tree,
    expression: expression
  };

  // console.log('Flow saved:', JSON.stringify(flow, null, 2));
  console.log('Expression:', expression);
  ElMessage.success('保存成功');
};

onConnect((params) => {
  console.log('Edge created:', params);
  
  const edge = {
    ...params,
    animated: true,
    type: 'button',
    style: { stroke: '#409eff' },
  };
  addEdges([edge]);
});

onNodeDragStop((e) => {
  console.log('Node drag stopped:', e);
});

const zoomIn = () => {
  const { x, y, zoom } = getViewport();
  setViewport({ x, y, zoom: zoom + 0.05 });
};

const zoomOut = () => {
  const { x, y, zoom } = getViewport();
  setViewport({ x, y, zoom: zoom - 0.05 });
};

onMounted(() => {
  setViewport({ x: 0, y: 0, zoom: 0.5 });
  setNodes([]);
  setEdges([]);
});

const nodeTypeList = [
  { type: 'start', label: '开始', icon: 'ep:video-play', color: '#409eff' },
  { type: 'end', label: '结束', icon: 'ep:circle-close', color: '#409eff' },
  { type: 'llm', label: '大模型', icon: 'ep:chat-line-round', color: '#409eff' },
  { type: 'kb', label: '知识库', icon: 'ep:files', color: '#409eff' },
  { type: 'template', label: '内容模板', icon: 'ep:document', color: '#409eff' },
  { type: 'http', label: 'HTTP请求', icon: 'ep:link', color: '#409eff' },
  { type: 'search', label: '搜索引擎', icon: 'ep:search', color: '#409eff' },
  // { type: 'test', label: '测试', icon: Monitor, color: '#409eff' },
  { type: 'parallel', label: '并行节点', icon: 'ep:share', color: '#409eff' },
  { type: 'loop', label: '循环节点', icon: 'ep:refresh', color: '#409eff' },
];
</script>

<template>
  <div class="ai-model-container">
    <div class="node-panel">
      <div class="panel-header">业务节点</div>
      <div class="node-list">
        <div
          v-for="(item, index) in nodeTypeList"
          :key="index"
          :class="['node-item', item.type]"
          draggable="true"
          @dragstart="(event) => {
            if (event.dataTransfer) {
              event.dataTransfer.setData('application/vueflow-type', item.type);
              event.dataTransfer.setData('application/vueflow-label', item.label);
              event.dataTransfer.effectAllowed = 'move';
            }
          }"
        >
          <Icon :icon="item.icon" />
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="flow-container">
      <div class="flow-toolbar">
        <!-- 放大 缩小 -->
         <el-button type="primary" @click="zoomIn">
          <Icon icon="ep:zoom-in" />
        </el-button>
        <el-button type="primary" @click="zoomOut">
          <Icon icon="ep:zoom-out" />
        </el-button>
        <el-button type="primary" @click="saveFlow">
          <Icon icon="ep:collection" />
        </el-button>
      </div>

      <VueFlow
        v-model:nodes="nodes"
        v-model:edges="edges"
        :node-types="nodeTypes"
        :default-viewport="{ zoom: 0.5 }"
        :min-zoom="0.2"
        :max-zoom="4"
        class="flow-canvas"
        @dragover.prevent
        @drop="onDrop"
        @node-click="onNodeClick"
        @edge-click="handleEdgeClick"
        :fit-view="false"
      >
      
      <template #edge-button="buttonEdgeProps">
        <EdgeWithButton
          :id="buttonEdgeProps.id"
          :selectedEdgeId="selectedEdgeId"
          :source-x="buttonEdgeProps.sourceX"
          :source-y="buttonEdgeProps.sourceY"
          :target-x="buttonEdgeProps.targetX"
          :target-y="buttonEdgeProps.targetY"
          :source-position="buttonEdgeProps.sourcePosition"
          :target-position="buttonEdgeProps.targetPosition"
          :style="buttonEdgeProps.style"
        />
      </template>
      <!-- <template #node-menu="props">
        <ToolbarNode :id="props.id" :data="props.data" />
      </template> -->
        <Background pattern-color="#aaa" :gap="12" />
        <MiniMap />
        <!-- <Controls position="top-left" /> -->
        <!-- <Panel position="top-right" class="save-panel">
          <el-button type="primary" @click="saveFlow">保存</el-button>
        </Panel> -->
      </VueFlow>
    </div>
  </div>
</template>

<style scoped>
/* 引入css */
@import '@/styles/AiModel/index.scss';
</style>
