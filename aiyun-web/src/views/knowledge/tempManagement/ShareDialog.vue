<template>
  <Dialog
    title="分享设置"
    v-model="dialogVisible"
    width="800px"
  >
    <!-- 好友选择区域 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="formLoading"
      label-width="80px"
      label-position="top"
    >
      <!-- 好友选择区域 -->
      <el-form-item label="选择好友" prop="users">
        <div class="friend-select">
          <div class="tree-container">
            <el-scrollbar height="300px">
              <el-tree
                ref="treeRef"
                :data="friendTree"
                show-checkbox
                node-key="id"
                :props="treeProps"
                @check="handleNodeCheck"
              />
            </el-scrollbar>
          </div>
          
          <div class="transfer-buttons">
            <el-button 
              :disabled="!checkedKeys.length"
              @click="addSelected"
              style="margin-left: 12px;"
            >
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button 
              :disabled="!formData.users.length"
              @click="removeSelected"
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
          </div>
          
          <div class="selected-container">
            <div class="selected-title">已选好友（{{ formData.users.length }}）</div>
            <el-scrollbar height="245px">
              <div v-for="user in formData.users" :key="user.id" class="selected-item">
                {{ user.name }}
                <el-icon class="close-icon" @click="removeSingle(user.id)">
                  <Close />
                </el-icon>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </el-form-item>

      <!-- 权限设置 -->
      <el-form-item label="权限设置" prop="jurisdiction">
        <!-- <el-checkbox-group v-model="formData.jurisdiction">
          <el-checkbox label="0">只读</el-checkbox>
          <el-checkbox label="1">编辑</el-checkbox>
          <el-checkbox label="2">下载</el-checkbox>
        </el-checkbox-group> -->
        <el-radio-group v-model="formData.jurisdiction">
          <el-radio label="0">只读</el-radio>
          <el-radio label="1">编辑</el-radio>
          <el-radio label="2">下载</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 有效期设置 -->
      <el-form-item label="有效期" prop="allottedDay">
        <el-radio-group v-model="formData.allottedDay">
          <el-radio label="3">三天</el-radio>
          <el-radio label="7">七天</el-radio>
          <el-radio label="0">永久</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认分享</el-button>
    </template>
  </Dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ArrowRight, ArrowLeft, Close } from '@element-plus/icons-vue'
import * as TemplateAPI from '@/api/knowledge/tempmangement'
import * as UserApi from '@/api/system/user'
import * as RoleApi from '@/api/system/role'
const message = useMessage() // 消息弹窗

// 弹窗显示控制
const dialogVisible = ref(false)

// 表单引用
const formRef = ref(null)

const formLoading = ref(false)
// 表单数据
const formData = reactive({
  users: [],
  jurisdiction: '',
  allottedDay: ''
})
// 校验规则
const formRules = reactive({
  users: [
    { 
      validator: (_, value, callback) => {
        if (value.length === 0) {
          callback(new Error('请至少选择一个好友'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  jurisdiction: [
    {
      validator: (_, value, callback) => {
        if (value.length === 0) {
          callback(new Error('请至少选择一项权限'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  allottedDay: [
    { 
      required: true,
      message: '请选择有效期',
      trigger: 'change' 
    }
  ]
})

// 好友树数据
const friendTree = ref([])


const getTreeList = async() => {
  const userList = await UserApi.getAllUser()
  const roleList = await RoleApi.getRoleSimpleList()
  console.log(userList,roleList);
  friendTree.value = [
    {
      id: 1,
      name: '好友',
      children: userList.map(item => {
        return {
          id: item.id,
          name: item.nickname,
          type: '1',
        }
      })
    },
    {
      id: 2,
      name: '角色',
      children: roleList.map(item => {
        return {
          id: item.id,
          name: item.name,
          type: '2'
        }
      })
    }
  ]
  
}

// 树形配置
const treeProps = {
  label: 'name',
  children: 'children'
}

// 选中的用户
const selectedUsers = ref([])
const checkedKeys = ref([])
const treeRef = ref()

// 处理节点选中
const handleNodeCheck = (node, checked) => {
  checkedKeys.value = checked.checkedKeys
}

// 添加选中好友时更新表单数据
const addSelected = () => {
  const nodes = treeRef.value.getCheckedNodes(true)
  const newUsers = nodes.filter(n => !n.children)
    .filter(n => !formData.users.some(u => u.id === n.id))
  
  formData.users = [...formData.users, ...newUsers]
}

// 确认分享时触发表单校验
const handleConfirm = () => {
  formRef.value.validate(async(valid) => {
    if (valid) {
      const params = {
        ...formData,
        shardTo: formData.users,
        klTemplateFileId: rowId.value
      }
      formLoading.value = true
      const response =await TemplateAPI.saveCloudShare(params)
      if(response) {
        message.success('分享成功')
        dialogVisible.value = false
      } else {
        message.error('分享失败')
      }
      formLoading.value = false
    } else {
      console.log('校验未通过')
      return false
    }
  })
}

// 移除选中
const removeSelected = () => {
  formData.users = []
}

// 移除单个
const removeSingle = (id) => {
  formData.users = formData.users.filter((item) => item.id !== id)
  console.log("formData.users", formData.users);
  
}

const rowId = ref(null)
// 暴露打开方法
const open = (row) => {
  const { id } = row
  dialogVisible.value = true
  rowId.value = id
  getTreeList()
  console.log(formData);
  formData.users = []
  formData.jurisdiction=''
  formData.allottedDay= ''
}

defineExpose({ open })
</script>

<style scoped>
.friend-select {
  width: 100%;
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}
.tree-container, .selected-container {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  height: 300px;
}
.el-form-item {
  margin-bottom: 20px;
}
.el-form-item__label {
  padding-bottom: 8px !important;
}

.transfer-buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
}

.selected-title {
  font-weight: 500;
  margin-bottom: 10px;
}

.selected-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  margin: 4px 0;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.close-icon {
  cursor: pointer;
  color: #909399;
}

.close-icon:hover {
  color: #409eff;
}

.section-title {
  font-weight: 500;
  margin: 16px 0 12px;
}

.permission-section, .expiration-section {
  margin-top: 20px;
}
</style>