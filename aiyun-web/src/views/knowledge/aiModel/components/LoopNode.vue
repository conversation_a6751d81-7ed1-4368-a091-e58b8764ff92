<script setup lang="ts">
import { Handle, Position, useVueFlow } from '@vue-flow/core';
import { NodeToolbar } from '@vue-flow/node-toolbar';
import { Delete, Refresh } from '@element-plus/icons-vue';
import { markRaw } from 'vue';

defineOptions({ name: 'LoopNode' })

const { removeNodes } = useVueFlow();

const Icons = {
  Delete: markRaw(Delete),
  Refresh: markRaw(Refresh)
};

const props = defineProps<{
  id: string;
  data: {
    label: string;
    inputParams?: { name: string; type: string; key?: string; required?: boolean }[];
    outputParams?: { name: string; type: string }[];
    maxIterations?: number;
    condition?: string;
    toolbarVisible?: boolean;
  };
}>();

const emit = defineEmits(['updateNodeInternals']);
</script>

<template>
  <div class="node-wrapper">
    <NodeToolbar
      :is-visible="data.toolbarVisible"
      style="background-color: #fff;"
      :position="Position.Top"
      align="end"
      :offset="4"
    >
      <el-button
        @click.stop="removeNodes([props.id])"
      >
        <el-icon><component :is="Icons.Delete" /></el-icon>
      </el-button>
    </NodeToolbar>
    <div class="loop-node">
      <Handle
        type="target"
        :position="Position.Top"
        :id="`${props.id}-top`"
        class="horizontal-handle"
      />
      <Handle
        type="target"
        :position="Position.Left"
        :id="`${props.id}-left`"
        class="vertical-handle"
      />
      <div class="node-content">
        <div class="node-header">
          <el-icon><component :is="Icons.Refresh" /></el-icon>
          <span class="node-type">{{ data.label }}</span>
        </div>
        <div class="node-body">
          <div class="loop-icon">
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path
                fill="#9254de"
                d="M12,4V1L8,5l4,4V6c3.31,0,6,2.69,6,6c0,1.01-0.25,1.97-0.7,2.8l1.46,1.46C19.54,15.03,20,13.57,20,12c0-4.42-3.58-8-8-8zm0,14c-3.31,0-6-2.69-6-6c0-1.01,0.25-1.97,0.7-2.8L5.24,7.74C4.46,8.97,4,10.43,4,12c0,4.42,3.58,8,8,8v3l4-4l-4-4V16z"
              />
            </svg>
          </div>
          <div class="loop-info">
            <div v-if="data.maxIterations" class="info-item">
              最大循环次数: {{ data.maxIterations }}
            </div>
            <div v-if="data.condition" class="info-item">
              循环条件: {{ data.condition }}
            </div>
          </div>
        </div>
      </div>
      <Handle
        type="source"
        :position="Position.Bottom"
        :id="`${props.id}-bottom`"
        class="horizontal-handle"
      />
      <Handle
        type="source"
        :position="Position.Right"
        :id="`${props.id}-right`"
        class="vertical-handle"
      />
    </div>
  </div>
</template>

<style scoped>
.node-wrapper {
  position: relative;
}

.loop-node {
  background: white;
  border: 2px solid #9254de;
  border-radius: 8px;
  padding: 12px;
  min-width: 150px;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.node-type {
  font-weight: bold;
  color: #9254de;
  text-align: left;
}

.node-body {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.loop-icon {
  display: flex;
  justify-content: center;
  animation: rotate 2s linear infinite;
}

.loop-info {
  font-size: 12px;
  color: #666;
}

.info-item {
  margin: 4px 0;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.horizontal-handle {
  background-color: #9254de;
  width: 30px;
  height: 16px;
  border-radius: 5px;
}

.vertical-handle {
  background-color: #9254de;
  width: 16px;
  height: 30px;
  border-radius: 5px;
}
</style> 