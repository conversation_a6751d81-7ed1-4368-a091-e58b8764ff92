import request from '@/config/axios'
import exp from 'constants'

// 查询文件列表分页 v1/kownledge/templateFile/page
export const getCloudPage = (params) => {
  return request.get({ url: '/v1/kownledge/templateFile/page',  params })
}
// 查询文件列表/v1/kownledge/templateFile/list
export const getCloudList = (params) => {
  return request.get({ url: '/v1/kownledge/templateFile/list',  params })
}

// 新建文件夹/v1/kownledge/templateFile/save
export const createFolder = (data) => {
  return request.postOriginal({ url: '/v1/kownledge/templateFile/save', data })
}

// 重命名 /v1/kownledge/templateFile/update
export const updateFolder = (data) => {
	return request.putOriginal({ url: '/v1/kownledge/templateFile/update', data  })
}

// 删除 /v1/kownledge/templateFile/delete
export const deleteFolder = (id) => {
	return request.deleteOriginal({ url: '/v1/kownledge/templateFile/delete?id=' + id })
}

// 文件上传
export const uploadFolderFile = (data) => {
	return request.upload({ url: '/v1/kownledge/templateFile/upload', data })
}

// 喜欢 /v1/kownledge/templateLikes/save
export const saveFolderLikes = (data) => {
	return request.postOriginal({ url: '/v1/kownledge/templateLikes/save', data })
}

// 取消喜欢 /v1/kownledge/templateLikes/{klTemplateId}
export const deleteFolderLikes = (id) => {
	return request.delete({ url: `/v1/kownledge/templateLikes/${id}` })
}

// 收藏 /kownledge/templateFavorites
export const saveFolderFavorites = (data) => {
	return request.postOriginal({ url: '/v1/kownledge/templateFavorites', data })
}

// 取消收藏/v1/kownledge/templateFavorites/{id}
export const deleteFolderFavorites = (id) => {
	return request.delete({ url: `/v1/kownledge/templateFavorites/${id}` })
}
// 推荐 kownledge/templatePushes
export const saveFolderPushes = (data) => {
	return request.postOriginal({ url: '/v1/kownledge/templatePushes', data })
}

// 取消推荐/v1/kownledge/templatePushes/{templateFileId}
export const deleteFolderPushes = (templateFileId) => {
	return request.delete({ url: `/v1/kownledge/templatePushes/${templateFileId}` })
}

// 移动 /v1/kownledge/templateFile/movement
export const movementFolder = (params) => {
	return request.get({ url: '/v1/kownledge/templateFile/movement', params })
}

// 下载附件/admin-api/hours/project/project/supplierFileDownload
export function supplierFileDownload(data) {
	return request.post({
	  url: '/hours/project/project/supplierFileDownload',
	  contentType: 'application/json',
	  responseType: 'blob',
	  params: data
	})
  }

// 解析设置查询  /v1/kownledge/cloudAnalysis/{id}
export const getCloudAnalysis = (id) => {
	return request.get({ url: `/v1/kownledge/cloudAnalysis/${id}` })
}

// 创建解析设置/v1/kownledge/cloudAnalysis/save
export const createCloudAnalysis = (data) => {
	return request.postOriginal({ url: '/v1/kownledge/cloudAnalysis/save', data })
}

// 修改解析设置/v1/kownledge/cloudAnalysis
export const updateCloudAnalysis = (data) => {
	return request.putOriginal({ url: '/v1/kownledge/cloudAnalysis', data })
}

// 分享 /v1/kownledge/templateFileShare/save

// 开启分享/kownledge/templateFileShare/enable/{klTemplateId}
export const createCloudShare = (id) => {
	return request.getOriginal({ url: `/v1/kownledge/templateFileShare/enable/${id}` })
}

// 取消分享 v1/kownledge/templateFileShare/klTemplate/{klTemplateId}
export const deleteCloudShare = (templateFileId) => {
	return request.delete({ url: `/v1/kownledge/templateFileShare/klTemplate/${templateFileId}` })
}

// 保存分享 v1/kownledge/templateFileShare/save
export const saveCloudShare = (data) => {
	return request.post({ url: `/v1/kownledge/templateFileShare/save`, data })
}

// 分享给我的文件 v1/kownledge/templateFileShare/shareWithMe
export const shareWithMe = (params) => {
	return request.get({ url: `/v1/kownledge/templateFileShare/shareWithMe`, params })
}

// 我分享的列表 /v1/kownledge/templateFileShare/page
export const shareMe = (params) => {
	return request.get({ url: `/v1/kownledge/templateFileShare/page`, params })
}


// 文件下载v1/kownledge/templateFile/FileDownload
export function knowledgeFileDownload(data) {
	return request.postOriginal({
	  url: '/v1/kownledge/templateFile/FileDownload',
	  contentType: 'application/json',
	  responseType: 'blob',
	  params: data
	})
  }

//   文档生成v1/kownledge/kiDocEventLog
export const createDoc = (data) => {
	return request.postOriginal({ 
		url: '/v1/kownledge/kiDocEventLog', 
		data, 
		timeout: 1800000 
	})
}
  