<template>
  <Dialog 
    v-model="dialogVisible" 
    :scroll="true"
    :title="dialogTitle">
    <div class="content">
      <el-table v-loading="loading" :data="fileList">
        <el-table-column prop="fileName" label="文件名称">
          <template #default="{ row }">
            {{ row.klTemplateFile ? row.klTemplateFile.templateFileName : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" width="180" label="分享时间" />
        <el-table-column prop="creator" width="140" label="分享人" />

      </el-table>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TemplateAPI from '@/api/knowledge/tempmangement'

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'ShareList' })

const emit = defineEmits(['success'])

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
// 当前文件夹下的文件列表
const fileList = ref([]);

const loading = ref(false)

const getShareWithMeList = async () => {
  loading.value = true
  const params = {
    pageNum: 1,
    pageSize: 9999
  }
  const response =  dialogType.value === 'my' ? await TemplateAPI.shareWithMe() : await TemplateAPI.shareMe(params)
  loading.value = false
  fileList.value = dialogType.value === 'my' ? response : response.records
}

const fileId = ref('')
const dialogTitle = ref('')
const dialogType = ref('')
/** 打开弹窗 */
const open = async (type) => {
  dialogTitle.value = type === 'my' ? '共享文件列表' : '我的分享'
  dialogType.value = type
  dialogVisible.value = true
  getShareWithMeList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

</script>

<style lang="scss" scoped>
  .breadcrumb-wrapper {
    display: flex;
    height: 48px;
    align-items: center;
    margin-bottom: 10px;
    padding: 0 20px;
    .back-button {
      padding: 0;
      font-size: 14px;
    }
  }
  .file-item {
    line-height: 32px;
    padding: 8px;
    border-bottom: 1px solid #ccc;
    cursor: pointer;
  }
</style>
