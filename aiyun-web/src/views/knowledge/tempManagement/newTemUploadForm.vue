<template>
  <Dialog v-model="dialogVisible" :scroll="true" :title="dialogTitle">
    <el-form ref="formRef" class="form-container" v-loading="formLoading" :model="formData" :rules="formRules"
      label-width="80px" label-position="top">
      <!-- 文件上传行 -->
      <el-form-item label="文件上传" prop="fileName">
        <div class="file-upload-wrapper">
          <el-input v-model="formData.fileName" placeholder="请选择要上传的文件" readonly class="file-input">
            <template #append>
              <el-button type="primary" @click="triggerFileInput" :loading="uploading">
                {{ uploading ? '上传中...' : '选择文件' }}
              </el-button>
            </template>
          </el-input>
          <input type="file" ref="fileInput" @change="handleFileSelect" style="display: none"
            accept=".doc,.docx,.pdf" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { useUserStore } from '@/store/modules/user'
import { defaultProps, handleTree } from '@/utils/tree'
import * as TemplateAPI from '@/api/knowledge/createTem'
import * as FolderAPI from '@/api/knowledge/docmangement'
import * as DeptApi from '@/api/system/dept'

defineOptions({ name: 'UploadForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userStore = useUserStore()

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const userInfo = userStore.getUser

// 下拉选项（示例数据）
const categoryOptions = ref([
  { value: 'tech', label: '技术文档' },
  { value: 'product', label: '产品文档' },
  { value: 'process', label: '流程规范' }
])

const departmentOptions = ref([
  { value: 'dev', label: '研发部' },
  { value: 'product', label: '产品部' },
  { value: 'hr', label: '人力资源部' }
])
const fileInput = ref(null)
const uploading = ref(false)
const formData = ref({
  fileName: '',
  templateFilePath: '',
  templateFileUrl: '',
  versions: '',
  templateFilePrefix: '',
  knowledgeName: '',
  category: '',
  projectName: '',
  shareUser: '',
  department: '',
  knowledgeOwner: '',
  firstLabel: '',
  secondLabel: '',
  downloadPermission: '',
  knowledgeRecommend: '',
  knowledgeAbstract: '',
})
const props = defineProps({
  path: {
    type: Object,
    default: () => {
    }
  }
})
const { path } = toRefs(props)
const formRules = reactive({
  fileName: [{ required: true, message: '请上传文件', trigger: 'blur' }],
  // knowledgeName: [{ required: true, message: '知识名称不能为空', trigger: 'blur' }],
  // category: [{ required: true, message: '知识分类不能为空', trigger: 'change' }],
  // projectName: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
  // shareUser: [{ required: true, message: '分享人不能为空', trigger: 'blur' }],
  // department: [{ required: true, message: '所属部门不能为空', trigger: 'change' }],
  // knowledgeOwner: [{ required: true, message: '知识所属人不能为空', trigger: 'blur' }],
  // firstLabel: [{ required: true, message: '一级标签不能为空', trigger: 'change' }],
  // secondLabel: [{ required: true, message: '二级标签不能为空', trigger: 'change' }],
  // downloadPermission: [{ required: true, message: '下载权限不能为空', trigger: 'change' }],
  // knowledgeRecommend: [{ required: true, message: '知识推荐不能为空', trigger: 'change' }],
  // knowledgeAbstract: [{ required: true, message: '知识摘要不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const deptList = ref([]) // 树形结构

// 获取部门树
const getTree = async () => {
  const res = await DeptApi.getSimpleDeptList()
  deptList.value = res
}


/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  dialogTitle.value = '新增'
  resetForm()
  getTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    const params = {
      ...data,
      // saveStatus: '1',
      parentId: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].id : null,
      level: path.value[path.value.length - 1].id ? parseInt(path.value[path.value.length - 1].level) + 1 : '1',
    }
    const response = await TemplateAPI.createFolder(params)
    dialogVisible.value = false
    message.success('文件上传成功')
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click()
}

// 处理文件选择
const handleFileSelect = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件
  if (!validateFile(file)) return

  // 执行上传
  try {
    // uploading.value = true
    console.log(file);
    const fileResponse = await uploadFile(file)
    const { filePath, fileName, fileUrl, versions, filePrefix } = fileResponse.data
    console.log(fileName);
    formData.value.fileName = fileName // 上传成功后设置文件名
    formData.value.filePath = filePath
    formData.value.fileUrl = fileUrl
    // formData.value.knowledgeName = fileName.split('.')[0]
    // formData.value.versions = versions
    formData.value.filePrefix = filePrefix
    ElMessage.success('文件上传成功')
  } catch (error) {
    ElMessage.error('文件上传失败')
    console.error('上传失败:', error)
  } finally {
    uploading.value = false
    fileInput.value.value = '' // 清空input
  }
}

// 文件验证
const validateFile = (file) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]

  // if (!allowedTypes.includes(file.type)) {
  //   ElMessage.error('仅支持 PDF/Word 格式!')
  //   return false
  // }

  if (file.size > 100 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 100MB!')
    return false
  }

  return true
}

// 文件上传 TemplateAPI.uploadFolderFile
const uploadFile = async (file) => {
  return new Promise((resolve, reject) => {
    FolderAPI.uploadFolderFile({
      file,
      path: path.value[path.value.length - 1].id ? path.value[path.value.length - 1].templateFilePath : null,
    }).then((res) => {
      if (res.code === 200) {
        resolve(res)
      } else {
        reject(res)
      }
    })
  })
  // const response = await TemplateAPI.uploadFolderFile({
  //   file: file,
  //   // path: 
  // })

}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    fileName: '',
    templateFilePath: '',
    templateFileUrl: '',
    versions: '',
    templateFilePrefix: '',
    knowledgeName: '',
    category: '',
    projectName: '',
    shareUser: userInfo.nickname,
    department: userInfo.deptId,
    knowledgeOwner: userInfo.nickname,
    firstLabel: '',
    secondLabel: '',
    downloadPermission: '',
    knowledgeRecommend: '',
    knowledgeAbstract: '',
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.form-container {
  padding: 0 20px;
}

.file-upload-wrapper {
  width: 100%;
}

.file-input {
  :deep(.el-input-group__append) {
    padding: 0 20px;
    border: none;
  }

  :deep(.el-input__inner) {
    cursor: default;
    background-color: #fff;
  }
}

.el-upload__tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}
</style>
