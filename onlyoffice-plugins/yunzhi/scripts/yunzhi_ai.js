function YunZhiChatRequest(prompt, systemMessage, stream, config, signal, checkValue) {
    return new Promise(async (resolve, reject) => {
        console.log(checkValue);
        // const BASE_URL = "http://192.168.0.221:48082";
        const BASE_URL = 'https://aiyun.frp.yn.asqy.net';
        const userName = window.Asc.plugin.info.userName;
        let url = '';
        let authorization = '';
        let modelKey = '';
        let dialogId = '';
        const keyParams =
            checkValue == 'LLM'
                ? {
                      modelName: 'qwen-long',
                  }
                : {
                      userName: userName,
                  };
        const params = new URLSearchParams(keyParams);
        const keyUrl = `${BASE_URL}/admin-api/v1/kownledge/klCloudUser/getDialogId?${params.toString()}`;
        try {
            const response = await fetch(keyUrl);
            if (!response.ok) throw new Error('Request failed');
            const data = await response.json();
            const result = JSON.parse(data.data);
            modelKey = result.modelKey || '';
            dialogId = result.dialogId || '';
            authorization = result.authorization || '';
        } catch (error) {
            console.error('Error:', error);
        }
        if (checkValue == 'LLM') {
            url = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
            // authorization = "sk-6ed61e4310b540b38cb3952827aa17a4";
            authorization = modelKey;
        } else {
            // url = 'http://192.168.0.223/api/v1/chats_openai/498c390c1f4211f0a7940242ac120004/chat/completions'
            url = `${BASE_URL}/api/v1/chats_openai/${dialogId}/chat/completions`;
            // authorization = "ragflow-UxZTcwNDc4ZjRlMTExZWY4NTRmMDI0Mm";
            authorization = authorization;
        }

        const headers = {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + authorization,
        };

        if (stream) {
            headers['Accept'] = 'text/event-stream';
        }
        console.log('prompt', prompt);
        const msg = prompt.slice(-5).map((item) => ({
            role: item.role,
            content: item.content,
        }));
        console.log(msg);
        const data = {
            model: 'qwen-long',
            messages: msg,
            stream: true,
        };
        console.log('body: ', JSON.stringify(data));
        // 发起请求
        fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data),
            signal: signal, // 支持 AbortSignal
        })
            .then(async (response) => {
                console.log('Ollama Response:', response);
                if (!response.ok) {
                    // 如果响应不成功，解析错误信息
                    return response.json().then((errData) => {
                        let errorMessage = errData.error?.message || 'Unknown error occurred';
                        reject(new Error(errorMessage));
                    });
                }

                // 如果是流式响应，返回 reader
                if (stream) {
                    console.log('Streaming response detected.');
                    // const reader = response.body.getReader();
                    // console.log('reader:',reader);
                    const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
                    console.log('reader:', reader);
                    resolve(reader);
                } else {
                    // 如果是普通响应，返回解析后的 JSON
                    resolve(response.json());
                }
            })
            .catch((err) => {
                reject(err);
            });
    });
}
const parseValue = (str) => {
    // Use regular expressions to match complete JSON objects and parse them
    const pattern = /data:\s*({.*?})\s*\n/g;
    let match = pattern.exec(str);
    console.log('str:', str);
    console.log('match:', match);

    const jsonStr = match[1];
    return JSON.parse(jsonStr);
};
function displaySSEMessage(reader, currentDiv, resultContainer) {
    return new Promise((resolve, reject) => {
        reader
            .read()
            .then(function processResult(result) {
                console.log('result:', result.value);
                // console.log("result:",result)
                if (result.done || !result.value) {
                    resolve(resultContainer);
                    return;
                }
                let sseData = parseValue(result.value);
                // console.log("sseData:",sseData)
                if (sseData.choices[0].hasOwnProperty('finish_reason')) {
                    switch (sseData.choices[0].finish_reason) {
                        case 'stop':
                            console.log('finish SSE response');
                            break;
                        case 'length':
                            console.log('Reach the ceiling of tokens');
                            break;
                        case 'sensitive':
                            console.log('The representative model inference content is intercepted by the security audit interface.');
                            break;
                        case 'network_error':
                            console.log('Model inference exception');
                            break;
                        case 'tool_calls':
                            console.log('Call the function');
                            break;
                        default:
                    }
                }
                if (resultContainer === null) {
                    resultContainer = { response: '', prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };
                }
                const lines = sseData.choices[0].delta.content.split('\n');
                lines.forEach((line) => {
                    const fragment = line;
                    resultContainer.response += fragment;
                    if (fragment === '') {
                        currentDiv.appendChild(document.createElement('br'));
                    } else {
                        currentDiv.appendChild(document.createTextNode(fragment));
                    }
                });
                displaySSEMessage(reader, currentDiv, resultContainer).then(resolve).catch(reject);
            })
            .catch(reject);
    });
}
// window.Asc.sendRequest = sendRequest;
