<template>
  <el-container class="detail-container">
    <el-main class="main-container">
			<el-form
        ref="formRef"
        v-loading="formLoading"
        :model="formData"
        :rules="formRules"
        label-width="80px"
      >
      <el-form-item label="人员信息" prop="fileName">
        <div class="file-upload-wrapper">
          <el-input 
            v-model="formData.fileName" 
            placeholder="请选择要上传的文件"
            readonly
            class="file-input" 
            style="width: 450px"
          >
            <template #append>
              <el-button 
                type="primary" 
                @click="triggerFileInput"
                :loading="uploading || isParse"
              >
                {{ isParse ? '解析中' : uploading ? '上传中...' : '选择文件' }}
              </el-button>
            </template>
          </el-input>
          <input
            type="file"
            ref="fileInput"
            @change="handleFileSelect"
            style="display: none"
            accept=".doc,.docx,.pdf,.png,.jpg,.jpeg,.xlsx, .ppt, .pptx, .txt"
          />
        </div>
      </el-form-item>
		</el-form>
    </el-main>
    <!-- 底部 -->
    <el-footer class="footer-container">
			<el-button type="primary" v-if="isShowView"  @click="handleClick">查 看</el-button>
			<el-button type="primary" :disabled="submitDisabled" :loading="formLoading" @click="submit">确 定</el-button>
			<el-button @click="handleCancel">取 消</el-button>
    </el-footer>
	</el-container>
	<ReportView ref="reportViewRef" />
</template>
<script setup lang="ts">
import * as FolderAPI from '@/api/knowledge/docmangement'
import ReportView from './ReportView.vue';
import { generatePromotionReports } from '@/api/home/<USER>'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'TitleReport' })

const emit = defineEmits(['cancel', 'submit'])
const message = useMessage() // 消息弹窗
const userStore = useUserStore()

const formRef = ref()
const formData = ref({
	fileName: '',
	fileUrl: '',
})
const formRules = ref({})
const formLoading = ref(false)
const fileInput = ref(null)
const uploading = ref(false)
const isParse = ref(false)
const submitDisabled = ref(true)
const isShowView = ref(false)

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value && fileInput.value.click()
}

const uploadFile = async(file) => {
  return new Promise((resolve, reject) => {
    FolderAPI.uploadFolderFile({
      file,
    }).then((res) => {
      if (res.code === 200) {
        resolve(res)
      } else {
        reject(res)
      }
    })
  })
}

const analysData = ref({})

const timer = ref()

// 处理文件选择
const handleFileSelect = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  submitDisabled.value = true
  responseKey.value = ''
  // 执行上传
  try {
    uploading.value = true
    isShowView.value = false
    clearInterval(timer.value)
    const fileResponse = await uploadFile(file)
    const { fileName, fileUrl,} = fileResponse.data
    const params = {
      fileUrl,
      type: '3',
    }
    const analyResponse = await FolderAPI.getCloudAnalysisBack(params)
    isParse.value = true
    timer.value = setInterval(async () => {
      const resultResponse= await FolderAPI.getCloudAnalysisBackResults({
        ...analyResponse
      })
      if (resultResponse.analysisStatus !== '1') {
        submitDisabled.value = false
				analysData.value = resultResponse
        isParse.value = false
        clearInterval(timer.value)
      }
    }, 20000)
		formData.value.fileName = fileName // 上传成功后设置文件名
    formData.value.fileUrl = fileUrl
    ElMessage.success('文件上传成功')
  } catch (error) {
    ElMessage.error('文件上传失败')
    console.error('上传失败:', error)
  } finally {
    uploading.value = false
    fileInput.value.value = '' // 清空input
  }
}
const reportViewRef = ref()
const responseKey = ref()
const submit = async() => {
  formLoading.value = true
  clearInterval(timer.value)
  if(responseKey.value) {
    message.warning('已生成报告')
    return
  }
  const response = await generatePromotionReports({
    content: analysData.value?.content,
    loginUserid: userStore.getUser.id,
  })
  responseKey.value = response
  timer.value = setInterval(async () => {
    const result = await generatePromotionReports({
      content: analysData.value?.content,
      loginUserid: userStore.getUser.id,
      key: responseKey.value
    })
    const { status } = JSON.parse(result)
    // 成功
    if (status === 1) {
      clearInterval(timer.value)
      isShowView.value = true
      formLoading.value = false
    } else if(status === 2) {
      message.error('生成失败')
      formLoading.value = false
      clearInterval(timer.value)
    }
  }, 10000)
	// const content = '"姓名：高级工程师; 性别：3; 出生日期：2018 ——专家基本信息基本信息：专利信息; 姓名：专利号; 性别：专利名称; 出生日期：领域分类; None：是否为主发明人; None：贡献度百分比; None：专利地址; None：类型; None：专利摘要 ——专家基本信息姓名：CN202310001; 性别：智能传感器系统; 出生日期：电子信息; None：是; None：0.8; None：地址1; None：发明; None：基于深度学习的工业传感器监测系统 ——专家基本信息None：地址2; None：实用新型; None：采用磁吸结构的便携式电子设备支架 ——专家基本信息姓名：CN202310045; 性别：物联网通信协议; 出生日期：人工智能; None：地址1; None：实用新型 ——专家基本信息基本信息：教育经历; 姓名：学历; 性别：学校; 出生日期：专业; None：年份 ——专家基本信息姓名：王建国; 性别：男; 出生日期：1985-03-12 00:00:00 ——专家基本信息 ——专家基本信息 ——专家基本信息基本信息：职称信息; 姓名：职称名称; 性别：等级; 出生日期：获得年份 ——专家基本信息姓名：教授; 性别：1; 出生日期：2020 ——专家基本信息 ——专家基本信息 ——专家基本信息姓名：博士; 性别：清华大学; 出生日期：计算机科学; None：2003-2007 ——专家基本信息 ——专家基本信息姓名：硕士; 性别：麻省理工; 出生日期：电子信息; None：2007-2009 ——专家基本信息基本信息：工作经历; 姓名：公司名称; 性别：职位; 出生日期：开始日期; None：结束日期; None：工作描述 ——专家基本信息姓名：中科院自动化所; 性别：研究员; 出生日期：42064; None：至今; None：人工智能算法研究 ——专家基本信息姓名：华为技术; 性别：高级工程师; 出生日期：40360; None：43435; None：5G通信技术研发 ——专家基本信息 ——专家基本信息"'
	// reportViewRef.value.open(analysData.value?.content); 
  // isShowView.value = true
	// reportViewRef.value.open(content);
}

const handleClick = () => {
  reportViewRef.value.open(responseKey.value, 'title')
}
// 取消
const handleCancel = () => {
	emit('cancel')
}

</script>

<style lang="scss" scoped>
.qa-container {
  margin: 0 auto;
  height: 100%;
  min-height: 200px;
  border: 1px solid #ccc;
  border-radius: 8px;
}
.detail-container {
  height: 100%;
}
// main 容器
.main-container {
  margin: 0;
  padding: 0;
  position: relative;
  height: 100%;
  width: 100%;

  .message-container {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-y: hidden;
    padding: 0;
    margin: 0;
  }
}

// 底部
.footer-container {
	height: 36px;
	text-align: right;
}
// 按钮
.tl-fixed {
    width: 50px;
    height: 130px;
    background-color: #5e60f3;
    position: fixed;
    z-index: 99;
    right: 30px;
    bottom: 10vh;
    cursor: pointer;
    border-radius: 25px;
    padding: 10px 0;
    .fixed-text {
      writing-mode: vertical-lr;
      font-size: 18px;
      font-weight: 700;
      color: #fff;
      line-height: 50px;
      display: inline-block;
      width: 100%;
      height: 100%;
      text-align: center;
      letter-spacing: 8px;
    }
}

</style>

<style lang="scss">
  .pop-list {
    width: 100%;
    .pop-item {
      text-align: center;
      > * {
        vertical-align: middle;
      }
    }
  }
  .el-popover {
    min-width: 50px;
  }
</style>
