<script setup lang="ts">
import { <PERSON><PERSON>, Position, useVueFlow } from "@vue-flow/core";
import { NodeToolbar } from '@vue-flow/node-toolbar'
import { ArrowUp, ArrowDown, Delete, Edit, Check } from '@element-plus/icons-vue';
import { ref, markRaw } from 'vue';

const Icons = {
  ArrowUp: markRaw(ArrowUp),
  ArrowDown: markRaw(ArrowDown),
  Delete: markRaw(Delete),
  Edit: markRaw(Edit),
  Check: markRaw(Check)
};

defineOptions({ name: 'StartNode' })

const { updateNodeData, removeNodes } = useVueFlow()

const isExpanded = ref(true);
const editingIndex = ref(-1);
const isNewRow = ref(false);

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// const toggleEdit = (index: number) => {
//   if (editingIndex.value === index) {
//     editingIndex.value = -1;
//     isNewRow.value = false;
//   } else {
//     editingIndex.value = index;
//     isNewRow.value = false;
//   }
// };

const props = defineProps<{
  id: string;
  data: {
    label: string;
    inputParams?: Array<{ name: string; type: string; key?: string; required?: boolean }>;
    outputParams?: Array<{ name: string; type: string }>;
    description?: string;
    toolbarVisible?: boolean;
  }
}>();

const emit = defineEmits(['updateNodeInternals']);

const updateData = (key: string, value: any) => {
  updateNodeData(props.id, {
    ...props.data,
    [key]: value
  });
};

const addInputParam = () => {
  const inputParams = [...(props.data.inputParams || [])];
  inputParams.push({ name: '', type: '' });
  updateData('inputParams', inputParams);
  editingIndex.value = inputParams.length - 1;
  isNewRow.value = true;
};

const updateParam = (index: number, key: string, value: string) => {
  const params = [...(props.data.inputParams || [])];
  params[index] = { ...params[index], [key]: value };
  updateData('inputParams', params);
};

const removeParam = (index: number) => {
  const params = [...(props.data.inputParams || [])];
  params.splice(index, 1);
  updateData('inputParams', params);
};

</script>

<template>
  <div class="node-wrapper">
    <NodeToolbar  
      :is-visible="data.toolbarVisible" 
      style="background-color: #fff;"
      :position="Position.Top"
      align="end"
      :offset="4"
    >
      <el-button
        @click.stop="removeNodes([props.id])"
      >
        <el-icon><component :is="Icons.Delete" /></el-icon>
      </el-button>
    </NodeToolbar>
    <div class="start-node">
      <div class="node-header">
        <span class="node-type">{{ data.label }}</span>
        <el-button
          type="primary"
          link
          @click="toggleExpand"
        >
          <el-icon>
            <component :is="isExpanded ? Icons.ArrowUp : Icons.ArrowDown" />
          </el-icon>
        </el-button>
      </div>
      <div v-show="isExpanded" class="node-content">
        <div class="description">这是流程开始的地方</div>
        <div class="params-section">
          <div class="params-header">
            <span>设置开场白</span>
          </div>
          <div style="margin-bottom: 10px;">
            <el-input
              :model-value="data.description"
              @update:model-value="(val) => updateData('description', val)"
              placeholder="开场白"
              type="textarea"
              show-word-limit
              maxlength="200"
              :rows="4"
            />
          </div>
        </div>
        <div class="params-section">
          <div class="params-header">
            <span>输入</span>
            <el-button type="primary" size="small" @click="addInputParam">新增</el-button>
          </div>
          <div class="params-table">
            <div class="table-header">
              <div class="col-key">Key</div>
              <div class="col-name">名称</div>
              <div class="col-type">类型</div>
              <div class="col-options">可选项</div>
              <div class="col-actions">操作</div>
            </div>
            <div v-for="(param, index) in data.inputParams" :key="index" class="table-row">
              <div class="col-key">
                <ElInput
                  :model-value="param.key"
                  @update:model-value="(val) => updateParam(index, 'key', val)"
                  placeholder="k1"
                />
              </div>
              <div class="col-name">
                <ElInput
                  :model-value="param.name"
                  @update:model-value="(val) => updateParam(index, 'name', val)"
                  placeholder="问题"
                />
              </div>
              <div class="col-type">
                <ElSelect
                  :model-value="param.type"
                  @update:model-value="(val) => updateParam(index, 'type', val)"
                  placeholder="类型"
                >
                  <ElOption label="line" value="line" />
                  <ElOption label="paragraph" value="paragraph" />
                  <ElOption label="number" value="number" />
                  <ElOption label="options" value="options" />
                </ElSelect>
              </div>
              <div class="col-options">
                <ElSelect
                  :model-value="param.required"
                  @update:model-value="(val) => updateParam(index, 'required', val)"
                  placeholder="是否必填"
                >
                  <ElOption label="否" value="false" />
                  <ElOption label="是" value="true" />
                </ElSelect>
              </div>
              <div class="col-actions">
                <el-button type="danger" link @click="() => removeParam(index)">
                  <el-icon><component :is="Icons.Delete" /></el-icon>
                </el-button>
                <!-- <el-button 
                  type="primary" 
                  link 
                  @click="() => toggleEdit(index)"
                  v-if="editingIndex !== index && !isNewRow"
                >
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button 
                  type="success" 
                  link 
                  @click="() => toggleEdit(index)"
                  v-if="editingIndex === index || (isNewRow && index === data.inputParams.length - 1)"
                >
                  <el-icon><Check /></el-icon>
                </el-button> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <Handle
        type="source"
        :position="Position.Bottom"
        class="vue-flow-handle"
      />
    </div>
  </div>
</template>

<style scoped>
.start-node {
  width: 600px;
  background-color: white;
  border: 2px solid #409eff;
  border-radius: 8px;
  padding: 16px;
}

.node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.node-type {
  font-weight: bold;
  color: #409eff;
}

.description {
  color: #606266;
  margin-bottom: 16px;
}

.params-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.params-table {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 100px;
  gap: 8px;
  padding: 8px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e2e8f0;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 100px;
  gap: 8px;
  padding: 8px;
  border-bottom: 1px solid #e2e8f0;
  align-items: center;
}

.table-row:last-child {
  border-bottom: none;
}

.col-key,
.col-name,
.col-type,
.col-options {
  display: flex;
  align-items: center;
}

.col-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

:deep(.el-input__wrapper) {
  box-shadow: none !important;
  border: 1px solid #e2e8f0;
}

:deep(.el-input__wrapper:hover) {
  border-color: #409eff;
}

:deep(.el-select) {
  width: 100%;
}
.vue-flow-handle {
  background-color: #2563eb;
  width: 30px;
  height: 16px;
  border-radius: 5px;
}

.node-wrapper {
  position: relative;
}
</style> 