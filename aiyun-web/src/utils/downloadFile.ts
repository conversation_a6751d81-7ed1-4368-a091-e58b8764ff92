import { knowledgeFileDownload } from "@/api/knowledge/docmangement";
import download from "./download";

export const downloadFileFunction = async (url: string, fileName: string) => {
  const fileType = fileName.substring(fileName.lastIndexOf('.') + 1)
  const response = await knowledgeFileDownload({
    fileUrl: url
  });
  if (fileType === 'pdf') {
    download.pdf(response, fileName)
  } else if(fileType === 'docx') {
    download.word07(response, fileName)
  } else if(fileType === 'doc') {
    download.word(response, fileName)
  } else if(fileType === 'xlsx') {
    download.excel(response, fileName)
  } else if(fileType === 'xls') {
    download.excel(response, fileName)
  } else if(fileType === 'png') {
    download.imagesPng(response, fileName)
  } else if(fileType === 'jpg') {
    download.imagesJpg(response, fileName)
  } else if(fileType === 'jpeg') {
    download.imagesJpeg(response, fileName)
  } else if(fileType === 'zip') {
    download.zip(response, fileName)
  }
}