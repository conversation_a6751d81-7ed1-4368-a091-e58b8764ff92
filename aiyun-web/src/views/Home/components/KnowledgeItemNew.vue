<template>
  <div class="shortcut-section">
    <!-- <div class="shortcut-title">{{ knowledgeName }}</div> -->
    <div class="hr-item-title">
      <span>
        {{knowledgeName}}
      </span>
    </div>
		<el-skeleton :loading="loading" animated>
			<el-scrollbar height="360px">
				<div 
					class="knowledge-item" 
					v-for="(item, index) in publicKnowledgeList" 
					:key="index"
				>
					<div class="folder-name ellipsis">
            <el-tooltip placement="top" :content="item.klCloudFile">
              {{ item.klCloudFile }}
            </el-tooltip>
					</div>
					<div class="knowledge-label">
						<span 
              v-for="(label, count) in item.label"
              :key="count"
              @click="handleTagClick(item, label)"
              >
							<el-tag type="primary">{{ label }}</el-tag>
						</span>
					</div>
				</div>
			</el-scrollbar>
		</el-skeleton>
  </div>
</template>

<script setup>
import * as homeApi from '@/api/home/<USER>'

defineOptions({ name: 'KnowledgeItem' })

const message = useMessage() // 消息弹窗
const loading = ref(false)

const props = defineProps({
  knowledgeName: { 
    type: String, 
    default: '' 
  },
  datasetId: {
    type: String,
    required: true
  }
})

const publicKnowledgeList = ref([])

const emits = defineEmits(['tag-click'])

const handleTagClick = (item, label) => {
  const params = {
    ...item,
    label,
  }
  emits('tag-click', params)
}

watch(() => props.datasetId, async (newVal) => {
  if (newVal) {
    try {
			if (!newVal) return
			loading.value = true
      const res = await homeApi.getHomeKlDatsetFileLabel({
        datasetId: newVal
      })
			publicKnowledgeList.value = res
			loading.value = false
      // if (res.code === 200) {
      //   publicKnowledgeList.value = res.data
      // } else {
      //   message.error(res.message)
      // }
    } catch (error) {
      message.error(error.message)
      console.error('Error fetching knowledge data:', error)
    }
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.shortcut-section {
  background-color: #fff;
  .shortcut-title {
		font-size: 16px;
    font-weight: 500;
    height: 48px;
    line-height: 48px;
    padding: 0 10px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--el-font-size-base);
    font-weight: normal;
    color: var(--el-text-color-primary);
    border-bottom: 2px solid var(--el-border-color-lighter);
  }
  .hr-item-title {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    align-items: center;
    span {
      padding-left: 10px;
      position: relative;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 14px;
        background-color: #409EFF; 
        border-radius: 2px;
      }
    }
  }
  
	.knowledge-item {
		padding: 10px 20px;
		color: var(--el-text-color-primary);
		.folder-name {
			font-size: var(--el-font-size-base);
			line-height: 24px;
			margin-left: -10px;
		}
		.knowledge-label {
			font-size: 12px;
      cursor: pointer;
			span {
				margin-right: 5px;
				line-height: 32px;
			}
		}
	}
}
</style>