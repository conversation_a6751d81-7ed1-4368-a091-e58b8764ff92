<template>
    <div>
      <el-card shadow="never">
        <el-skeleton :loading="loading" animated>
          <el-row :gutter="16" justify="space-between">
            <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="flex items-center">
                <el-avatar :src="avatar" :size="70" class="mr-16px">
                  <img src="@/assets/imgs/logo_1.png" alt="" />
                </el-avatar>
                <div>
                  <div class="text-20px">
                    {{ t('workplace.welcome') }} {{ username }} 
                  </div>
                  <div class="mt-10px text-14px text-gray-500">
                    {{ t('workplace.happyDay') }}
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :xl="12" :lg="12" :md="12" :sm="24" :xs="24">
              <div class="h-70px flex items-center justify-end lt-sm:mt-10px">
                <div class="px-8px text-right">
                  <div class="mb-16px text-14px text-gray-400">在线文档数</div>
                  <CountTo
                    class="text-20px"
                    :start-val="0"
                    :end-val="klCloudcount"
                    :duration="2600"
                  />
                </div>
                <el-divider direction="vertical" />
                <div class="px-8px text-right">
                  <div class="mb-16px text-14px text-gray-400">文档生成数</div>
                  <CountTo
                    class="text-20px"
                    :start-val="0"
                    :end-val="klDoccount"
                    :duration="2600"
                  />
                </div>
                <el-divider direction="vertical" border-style="dashed" />
                <div class="px-8px text-right">
                  <div class="mb-16px text-14px text-gray-400">对话数</div>
                  <CountTo
                    class="text-20px"
                    :start-val="0"
                    :end-val="klModelSessioncount"
                    :duration="2600"
                  />
                </div>
              </div>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
    </div>
  
    <el-row class="mt-8px" :gutter="8" justify="space-between">
      <el-col :xl="16" :lg="16" :md="24" :sm="24" :xs="24" class="mb-8px">
        <el-card shadow="never">
          <template #header>
            <div class="h-3 flex justify-between custom-style">
              <!-- <span>{{ t('workplace.project') }}</span> -->
                <el-segmented v-model="segmentedValue" @change="handleSegmentedChange" :options="segmentedOptions" />
              <!-- <el-link
                type="primary"
                :underline="false"
                href="https://github.com/yudaocode"
                target="_blank"
              >
                {{ t('action.more') }}
              </el-link> -->
            </div>
          </template>
          <el-skeleton :loading="loading" animated>
            <el-row>
              <el-table :data="documentList" :height="280">
                <el-table-column prop="fileName" show-overflow-tooltip label="文档名称" />
                <el-table-column prop="filePath" show-overflow-tooltip label="文档路径" />
                <el-table-column prop="count" label="数量" width="80" />
              </el-table>
            </el-row>
          </el-skeleton>
        </el-card>
        <el-row class="mt-8px" :gutter="8" justify="space-between">
          <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
            <DocstatisticEcharts />
          </el-col>
          <el-col :xl="12" :lg="12" :md="24" :sm="24" :xs="24">
            <SystemAccessEcharts />
          </el-col>
        </el-row>
      </el-col>
      <el-col :xl="8" :lg="8" :md="24" :sm="24" :xs="24" class="mb-8px">
        <el-card shadow="never">
        <template #header>
          <div class="h-3 flex justify-between">
            <span>{{ t('workplace.shortcutOperation') }}</span>
          </div>
        </template>
        <el-skeleton :loading="loading" animated>
          <el-row>
            <el-col v-for="item in shortcut" :key="`team-${item.name}`" :span="8" class="mb-8px">
              <div class="flex items-center">
                <Icon :icon="item.icon" class="mr-8px" />
                <el-link type="default" :underline="false">
                  <router-link  :to="item.url">
                    <el-button link type="primary">{{ item.name }}</el-button>
                  </router-link>
                </el-link>
              </div>
            </el-col>
          </el-row>
        </el-skeleton>
      </el-card>
        <el-card shadow="never" class="mt-8px">
          <template #header>
            <div class="h-3 flex justify-between">
              <span>访问日志</span>
            </div>
          </template>
          <el-skeleton :loading="loading" animated>
            <el-scrollbar max-height="560px">
              <div v-for="(item, index) in loginLogList" :key="`dynamics-${index}`">
                <div class="flex items-center h-69px">
                  <el-avatar :src="avatar" :size="35" class="mr-16px">
                    <img src="@/assets/imgs/logo_1.png" alt="" />
                  </el-avatar>
                  <div>
                    <div class="text-14px">
                      {{ item.username }} - {{ item.userIp }}
                      <dict-tag class="ml-40px" :type="DICT_TYPE.SYSTEM_LOGIN_TYPE" :value="item.logType" />
                    </div>
                    <div class="mt-16px text-12px text-gray-400">
                      {{ formatTime(item.createTime, 'yyyy-MM-dd') }}
                    </div>
                  </div>
                </div>
                <el-divider style="margin: 0;"/>
              </div>
            </el-scrollbar>
          </el-skeleton>
        </el-card>
      </el-col>
    </el-row>
    
  </template>
  <script lang="ts" setup>
  import { set } from 'lodash-es'
  import { EChartsOption } from 'echarts'
  import { formatTime } from '@/utils'
  
  import { useUserStore } from '@/store/modules/user'
  import { DICT_TYPE } from '@/utils/dict'
  import { useWatermark } from '@/hooks/web/useWatermark'
  import type { Shortcut } from './types'
  import { pieOptions, } from './echarts-data'
  import DocstatisticEcharts from './components/DocstatisticEcharts.vue'
  import SystemAccessEcharts from './components/SystemAccessEcharts.vue'
  import * as homeApi from '@/api/home/<USER>'
  import * as LoginLogApi from '@/api/system/loginLog'
  
  defineOptions({ name: 'Home' })
  
  const { t } = useI18n()
  const userStore = useUserStore()
  const { setWatermark } = useWatermark()
  const loading = ref(true)
  const avatar = userStore.getUser.avatar
  const username = userStore.getUser.nickname
  const pieOptionsData = reactive<EChartsOption>(pieOptions) as EChartsOption
  const documentList = ref([])
  const segmentedValue = ref('点赞文档')
  const segmentedOptions = ['点赞文档', '收藏文档', '推荐文档', '下载文档']
  
  // 点赞
  const getHomeLikesCountList = async() => {
    documentList.value = await homeApi.getHomeLikesCount()
  }
  // 推荐
  const getHomePushesCountList = async() => {
    documentList.value = await homeApi.getHomePushesCount()
  }
  // 收藏
  const getHomeFavoritesCountList = async() => {
    documentList.value = await homeApi.getHomeFavoritesCount()
  }
  // 下载
  const getHomeDownloadCountList = async() => {
    documentList.value = await homeApi.getHomeDownloadCount()
  }
  const handleSegmentedChange = (value: string) => {
    switch (value) {
      case '点赞文档':
        getHomeLikesCountList()
        break
      case '收藏文档':
        getHomeFavoritesCountList()
        break
      case '推荐文档':
        getHomePushesCountList()
        break
      case '下载文档':
        getHomeDownloadCountList()
        break
     default:
    }
  }
  
  type logItem = {
    username: string
    userIp: string
    createTime: string
    logType: string
  }
  // 获取通知公告
  const loginLogList = ref<logItem[]>([])
  const getNotice = async () => {
    const data = await LoginLogApi.getLoginLogPage({
      pageNo: 1,
      pageSize: 10,
    })
    loginLogList.value = data.list
  }
  
  // 获取快捷入口
  let shortcut = reactive<Shortcut[]>([
    {
      name: '公共知识库',
      icon: 'fa:database',
      url: '/knowledge/docmanagement/publicdocmanagement'
    },
    {
      name: '个人知识库',
      icon: 'fa:folder',
      url: '/knowledge/docmanagement/mymanagement'
    },
    {
      name: '我的收藏',
      icon: 'fa:star',
      url: '/knowledge/docmanagement/myCollect'
    },
    {
      name: '我的推荐',
      icon: 'fa:thumbs-up',
      url: '/knowledge/docmanagement/myrecommend'
    },
    {
      name: '我的点赞',
      icon: 'fa:heart',
      url: '/knowledge/docmanagement/mylike'
    },
    {
      name: '我的分享',
      icon: 'fa:share-alt',
      url: '/knowledge/docmanagement/myshare'
    },
  ])

  
  const getAllApi = async () => {
    await Promise.all([
      getNotice(),
      getHomeCount(),
      getHomeLikesCountList()
    ])
    loading.value = false
  }

  const klCloudcount = ref(0)
  const klDoccount = ref(0)
  const klModelSessioncount = ref(0)
  const getHomeCount = async () => {
    const res = await homeApi.getHomeCount()
    console.log('res', res);
    klCloudcount.value = res.klCloudcount
    klDoccount.value = res.klDoccount
    klModelSessioncount.value = res.klModelSessioncount
  }

  getAllApi()
  </script>

  <style scoped>
  .custom-style .el-segmented {
    position: absolute;
    top: 10px;
    --el-segmented-item-selected-color: var(--el-text-color-primary);
    --el-segmented-item-selected-bg-color: #ffd100;
    --el-border-radius-base: 16px;
  }
    
  </style>
  