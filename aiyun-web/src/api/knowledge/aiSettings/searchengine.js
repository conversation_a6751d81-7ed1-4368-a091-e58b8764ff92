import request from '@/config/axios'

// 
export const getklArrangeSearchEnginePage = (params) => {
  return request.get({ url: '/v1/kownledge/klArrangeSearchEngine/page', params })
}
// 
export const addklArrangeSearchEngine = (data) => {
  return request.post({ url: '/v1/kownledge/klArrangeSearchEngine', data })
}

// 
export const updateklArrangeSearchEngine = (data) => {
  return request.put({ url: '/v1/kownledge/klArrangeSearchEngine', data })
}

// 
export const deleteklArrangeSearchEngine = (id) => {
  return request.delete({ url: `/v1/kownledge/klArrangeSearchEngine/${id}` })
}

// 
export const getklArrangeSearchEngine = (id) => {
  return request.get({ url: `/v1/kownledge/klArrangeSearchEngine/${id}` })
}
