<template>
  <el-menu
    background-color="#545c64"
    text-color="#fff"
    active-text-color="#ffd04b"
    v-if="!isSuperAdmin"
    class="my-menu"
    mode="horizontal"
    :ellipsis="false"
  >
    <el-menu-item index="0" disabled class="menu-logo">
      <img
        src="@/assets/ai/ai.png"
        class="h-32px w-32px mr-5px"
        alt="Element logo"
      />
    </el-menu-item>
    <el-sub-menu index="2">
      <template #title><el-icon><Document /></el-icon>我的文档</template>
      <el-menu-item index="2-1">
        <div class="button-items" @click="handleShowDocument('collect')">
          <Icon icon="fa:star" class="mr-[5px]" :size="14"/>
          <span>收藏文档</span>
        </div>
      </el-menu-item>
      <el-menu-item index="2-2">
        <div class="button-items" @click="handleShowDocument('like')">
          <Icon icon="fa:heart" class="mr-[5px]" :size="14"/>
          <span>点赞文档</span>
        </div>
      </el-menu-item>
      <el-menu-item index="2-3">
        <div class="button-items" @click="handleShowDocument('share')">
          <Icon icon="fa:share-square-o" class="mr-[5px]" :size="14"/>
          <span>转发文档</span>
        </div>
      </el-menu-item>
    </el-sub-menu>
    <el-menu-item index="3">
      <template #title><el-icon><Connection /></el-icon>行业动态</template>
    </el-menu-item >
    <el-sub-menu 
      index="4" 
      class="last-menu-item"
      popper-class="my-popper">
      <template #title><el-icon><More /></el-icon></template>
      <el-menu-item index="4-1">
        <div class="button-items" @click="handleCreate('knowledge')">
          <Icon icon="fa:database" class="mr-[5px]" :size="14"/>
          <span>维护知识库</span>
        </div>
      </el-menu-item>
      <el-divider style="margin: 4px 0" />
      <el-menu-item index="4-2">
        <div class="button-items" @click="handleCreate('docx')">
          <Icon icon="fa:file-word-o" class="mr-[5px]" :size="14"/>
          <span>新建文档</span>
        </div>
      </el-menu-item>
      <el-menu-item index="4-3">
        <div class="button-items" @click="handleCreate('xlsx')">
          <Icon icon="fa:file-excel-o" class="mr-[5px]" :size="14"/>
          <span>新建表格</span>
        </div>
      </el-menu-item>
      <el-menu-item index="4-4">
        <div class="button-items" @click="handleCreate('pptx')">
          <Icon icon="fa:file-excel-o" class="mr-[5px]" :size="14"/>
          <span>新建幻灯片</span>
        </div>
      </el-menu-item>
      <el-menu-item index="4-5">
        <div class="button-items" @click="handleCreate('upload')">
          <Icon icon="fa:file-excel-o" class="mr-[5px]" :size="14"/>
          <span>上传文件</span>
        </div>
      </el-menu-item>
      <el-divider style="margin: 4px 0" />
      <el-menu-item v-if="!isSuperAdmin" index="4-6">
        <div class="button-items" @click="toProfile">
          <Icon icon="ep:tools" class="mr-[5px]" :size="14"/>
          <span>个人中心</span>
        </div>
      </el-menu-item>
      <!-- /user/homeprofile -->
      <el-menu-item v-if="!isSuperAdmin" index="4-7">
        <div class="button-items" @click="loginOut">
          <Icon icon="ep:switch-button" class="mr-[5px]" :size="14"/>
          <span>退出系统</span>
        </div>
      </el-menu-item>
    </el-sub-menu>
  </el-menu>
  <div class="home-container">
      <div class="home-header">
        <!-- 搜索区域 -->
        <div class="search-header flex items-center">
          <!-- enter键 搜索 -->
          <el-input 
            v-model="searchKeyword" 
            size="large" 
            clearable
            placeholder="请输入" 
            ref="searchInputRef"
            class="flex-1 input-with-select"
            @focus="handleSearchFocus"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <div class="search-append">
                <div class="flex items-center justify-center w-40px">
                  <el-button type="primary" @click="handleSearch">
                    <Icon icon="ep:search" :size="16" />
                  </el-button>
                </div>
              </div>
            </template>
            <template #prepend>
              <el-select v-model="searchType" size="large" placeholder="所有类型">
                <el-option label="所有类型" value="" />
                <el-option label="主题" value="topic" />
                <el-option label="作者" value="creator" />
                <el-option label="关键词" value="keyword" />
                <!-- <el-option label="摘要" value="knowledgeAbstract" /> -->
              </el-select>
            </template>
          </el-input>
        </div>
        <div v-show="isSearchFocused" ref="searchViewRef" class="search-dropdown" @mousedown.stop @click.stop>
          <el-scrollbar max-height="720px" min-height="100px">
            <div v-show="!showSearchView" >
              <div class="search-history">
                <div class="doc-types">
                  <div class="type-list">
                    <el-checkbox-group v-model="checkValue">
                      <el-checkbox 
                        class="checkbox" 
                        v-for="(item, index) in checkList"
                        :key="index"
                        :label="item.name" 
                        :value="item.name" />
                    </el-checkbox-group>
                  </div>
                </div>
                <div class="history-header">
                  <span>搜索历史</span>
                  <el-button link type="primary" @click="clearHistory">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                
                <div class="history-list">
                  <div v-for="(item, index) in searchHistoryList" @click.stop="handleHistoryClick(item)" :key="index">
                    <div class="history-item">
                      <el-icon><Clock /></el-icon>
                      <span class="history-text">{{ item.content }}</span>
                      <el-icon class="delete-icon" @click.stop="removeHistory(item.id)"><Close /></el-icon>
                    </div>
                  </div>
                  <div v-for="(item, index) in searchDocList" :key="index">
                    <div class="history-doc" @click="handleHistoryDocClick(item)">
                      <el-icon><Document color="#1790ff" /></el-icon>
                      <div class="doc-desc">
                        <p class="doc-text-title">{{ item.fileName }}</p>
                        <p class="doc-text-time">{{ item.createTime }} &nbsp;&nbsp;你在 {{ item.serviceType }}&nbsp;&nbsp; {{ filterEventType(item.eventType) }}了这篇文档</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div 
              v-show="showSearchView" 
              class="search-view">
                <SearchView ref="searchResultRef" />
            </div>
          </el-scrollbar>
        </div>
        
      </div>
      <div class="home-body">
        <div class="left-side">
          <!-- 知识图谱 -->
          <div class=" knowledge-content">
            <el-row :gutter="16">
              <el-col 
                v-for="(item, index) in knowledgeList" 
                :key="index" 
                :span="8"
                >
                <!-- :span="getColSpan(index)" -->
                <KnowledgeItem 
                  :knowledgeName="item.name" 
                  :datasetId="item.datasetId"
                  @tag-click="tagClick"
                  />
              </el-col>
            </el-row>
          </div>
        </div>
        <!-- <div class="right-side right-container">
          <div class="right-content">
            <div class="shortcut-section">
              <div class="shortcut-title">
                <span>AI 对话</span>
                <el-button 
                  type="primary"
                  link
                  size="small"
                  @click="handleMoreChats"
                  >更多</el-button>
              </div>
              <el-skeleton :loading="loading" animated>
                <div v-if="!isConversationEmpty">
                  <div 
                    class="knowledge-item"  
                    v-for="conversationKey in Object.keys(conversationMap)"
                     :key="conversationKey">
                    <div class="folder-name">{{ conversationKey }}</div>
                    <div class="knowledge-lebel" >
                      <span 
                      v-for="conversation in conversationMap[conversationKey]" 
                      @click="handleChooseChat(conversation)"
                      :key="conversation.id">
                      {{ conversation.name }}
                    </span>
                    </div>
                  </div>
                </div>
                <div class="empty-tips" v-else>
                  暂无对话
                </div>
              </el-skeleton>
            </div>
    
            <div class="mt-16px rank-section">
              <div class="flex mb-16px tab-container position-relative">
                <el-tabs v-model="activeName" class="knowledge-tabs">
                  <el-tab-pane 
                    v-for="tab in rankTabs" 
                    :key="tab.key"
                    :label="tab.label" 
                    :name="tab.key" />
                </el-tabs>
                <el-button 
                  type="primary"
                  link
                  size="small"
                  v-show="activeName === 'docs'"
                  class="position-absolute right-10px top-20px"
                  @click="handleMoreDocs"
                  >更多</el-button>
              </div>
              <el-skeleton :loading="loading" animated>
                <div class="rank-list" v-if="rankList.length">
                  <div 
                    v-for="(item, index) in rankList" 
                    :key="index"
                    @click="handleView(item)"
                    class="rank-item"
                  >
                    <span :class="['rank-number', index < 3 ? 'top-three' : '']">{{ index + 1 }}</span>
                    <span class="rank-title">{{ item.fileName }}</span>
                  </div>
                </div>
                <div class="empty-tips" style="padding-bottom: 32px;" v-else>暂无数据</div>
              </el-skeleton>
            </div>
          </div>
        </div> -->
      </div>
      <FileWayDialog ref="FileWayDialogRef" @success="getFileWay" />
      <CreatKnowledge ref="creatKnowledgeRef" />
      <CreatFile ref="creatFileRef" @success="createFileSuccess"/>
      <ChatMessageDialog ref="ChatMessageDialogRef" />
      <ChatListDialog ref="ChatListDialogRef" />
      <DocListDialog ref="docListDialogRef" @success="getdocsList" />
      <DocView ref="docViewRef" />
      <MyDocumentList ref="myDocumentListRef" />
      <AgentAnswer />
      <TagDocments ref="tagDocmentsRef" @docment-click="handleView"/>
      <HomeProfile ref="homeprofileRef" />
      <UploadForm 
        ref="uploadFormRef" 
        :path="choosePath" 
        :create-folder="FolderAPI.createFolder" 
        :update-folder="FolderAPI.updateFolder" 
        />
    </div>
  
  </template>
  <script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { ChatConversationVO } from '@/api/ai/chat/conversation'
  import { getKlModelSession, } from '@/api/knowledge/model'
  import {More, Delete, Clock, Close, Document, Connection} from '@element-plus/icons-vue'
  import * as homeApi from '@/api/home/<USER>'
  import * as TemplateAPI from '@/api/knowledge/createTem'
  import * as FolderAPI from '@/api/knowledge/docmangement'
  import SearchView from './components/SearchView.vue'
  import FileWayDialog from './components/FileWayDialog.vue'
  import DocView from '@/views/knowledge/docManagement/components/DocView.vue'
  import { useTagsViewStore } from '@/store/modules/tagsView'
  import CreatKnowledge from './components/CreatKnowledge.vue'
  import CreatFile from './components/CreatFile.vue'
  import ChatMessageDialog from './components/ChatMessageDialog.vue'
  import ChatListDialog from './components/ChatListDialog.vue'
  import DocListDialog from './components/DocListDialog.vue'
  import { useUserStoreWithOut } from '@/store/modules/user'
  import MyDocumentList from './components/MyDocumentList.vue'
  import KnowledgeItem from './components/KnowledgeItem.vue'
  import AgentAnswer from '@/components/AgentAnswer/index.vue'
  import TagDocments from './components/TagDocments.vue'
  import HomeProfile from './components/HomeProfile.vue'
  import UploadForm from '@/views/knowledge/docManagement/components/UploadForm.vue'
  import { personKnowledge, specialKnowledge, publicKnowledge } from './components/data'
  import type { Shortcut } from './types'
  
  defineOptions({ name: 'Home' })

  const { t } = useI18n()
  const { replace } = useRouter()
  const message = useMessage() // 消息弹窗

  const tagsViewStore = useTagsViewStore()
  
  const userStore = useUserStoreWithOut()
  const roles = userStore.getRoles
  const loginUser = userStore.getUser
  const isSuperAdmin = roles.includes('super_admin')

  const searchViewParams = computed(() => {
    return {
      searchType: searchType.value,
      searchMessage: searchMessage.value,
      checkValue: checkValue.value,
    }
  })
  
  const loading = ref(true)
  
  // 搜索相关
  const searchType = ref('')
  const searchKeyword = ref('')

  // tab
  const activeName = ref('docs')

  const knowledgeList = ref([])
  const searchResultRef = ref()
  
  const filterEventType = (item: any) => {
    // 1增加2删除3修改4查看
    switch (item) {
      case '1':
        return '新增'
      case '2':
        return '删除'
      case '3':
        return '修改'
      case '4':
        return '查看'
      default:
        return ''
    }
  }
  
  // 知识排行相关
  const currentRankTab = ref('week')
  const rankTabs = [
    { key: 'docs', label: '我的文档' },
    { key: 'likes', label: '周下载排行' },
  ]
  
  const rankList = ref([])
  
  const FileWayDialogRef = ref(null)
  const handleCreate = (type: string) => {
    if (type === 'knowledge') {
      creatKnowledgeRef.value && creatKnowledgeRef.value.open()
    } else {
      FileWayDialogRef.value && FileWayDialogRef.value?.open(type);
    }
  }
  
  // 获取数据
  const getHomeDownloadCountList = async() => {
    const response = await homeApi.getHomeDownloadCount()
    rankList.value = response
  }

  // 获取知识库首页
  const getHomeKlDatset = async () => {
    knowledgeList.value = await homeApi.getHomeKlDatset()    
  }
  
  const klCloudcount = ref(0)
  const klDoccount = ref(0)
  const klModelSessioncount = ref(0)
  const getHomeCount = async () => {
    const res = await homeApi.getHomeCount()
    klCloudcount.value = res.klCloudcount
    klDoccount.value = res.klDoccount
    klModelSessioncount.value = res.klModelSessioncount
  }

  // 获取搜索历史
  const getSearchLog = async () => {
    const res = await homeApi.getSearchLog({
      creator: loginUser.id,
      pageNum: 1,
      pageSize: 10
    })
    searchHistoryList.value = res.records
  }

  const getLabelList = async () => {
    const res = await homeApi.getLabelPage({
      pageNum: 1,
      pageSize: 10
    })
    checkList.value = res.records
  }
  
  const getAllApi = async () => {
    await Promise.all([
      getHomeCount(),
      getHomeDownloadCountList(),
      getHomeKlDatset()
    ])
    loading.value = false
  }
  
  getAllApi()

  const isSearchFocused = ref(false)
  const searchHistoryList = ref([])

  const getColSpan = (index) => {
    return index % 2 === 0 ? 14 : 10
  }

  const checkList = ref([])
  const checkValue = ref([])

  const handleSearchFocus = () => {
    isSearchFocused.value = true
    showSearchView.value = false
    getSearchLog()
    getLabelList()
    getSearchDocList()
  }
  const creatKnowledgeRef = ref(null)
  const creatFileRef = ref(null)
  const choosePath = ref({})
  const uploadFormRef = ref(null)
  const getFileWay = (way: any, type: any) => {
    choosePath.value = way
    if (type === 'docx' || type === 'xlsx' || type === 'pptx') {
      creatFileRef.value && creatFileRef.value.open(way, type)
    } else if (type === 'upload') {
      nextTick(() => {
				uploadFormRef.value && uploadFormRef.value.open('0')
			})
    } 
  }

  const createFileSuccess = async(response: any) => {
    docViewRef.value.open('create', choosePath.value, response );
  }

  const handleHistoryDocClick = (item: any) => {
    docViewRef.value.open('view', {}, item, true, '搜索结果' );
  }

  // const handleSearchBlur = () => {
  //   // 使用setTimeout让点击历史记录和清除按钮的事件能够先触发
  //   setTimeout(() => {
  //     isSearchFocused.value = false
  //   }, 200)
  // }

  const clearHistory = async() => {
    // searchHistoryList.value = []
    try {
      await homeApi.deleteSearchLogAll({creator: loginUser.id})
      message.success('删除成功')
      getSearchLog()

    } catch (error) {
      console.error('删除失败:', error);
    }
  }

  const removeHistory = async(id: string) => {
    // searchHistoryList.value.splice(index, 1)
    await homeApi.deleteSearchLog(id)
    getSearchLog()
  }

  const handleHistoryClick = (item: any) => {
    searchKeyword.value = item.content
    showSearchView.value = true
    searchMessage.value = searchKeyword.value
    searchResultRef.value.open(
      searchViewParams.value
    )
  }

  const showSearchView = ref(false)
  const searchMessage = ref('')
  const searchInputRef = ref(null)
  const handleSearch = () => {
    console.log('search');
    if(!searchKeyword.value) {
      message.warning('请输入搜索内容')
      return
    }
    showSearchView.value = true
    searchMessage.value = searchKeyword.value
    searchResultRef.value.open(
      searchViewParams.value
    )
  }
  const searchViewRef = ref(null)

  const handleClickOutside = (event: any) => {
    if (
      isSearchFocused.value && 
      searchViewRef.value && 
      !searchViewRef.value.contains(event.target) && 
      searchInputRef.value && 
      !searchInputRef.value.$el.contains(event.target)
    ) {
      isSearchFocused.value = false
    }
  }

  /** 按照 creteTime 创建时间，进行分组 */
  const getConversationGroupByCreateTime = async (list: ChatConversationVO[]) => {
    // 排序、指定、时间分组(今天、一天前、三天前、七天前、30天前)
    // noinspection NonAsciiCharacters
    const groupMap = {
      置顶: [],
      我的对话:[],
    }
    for (const conversation of list) {
      // 置顶
      if (conversation.top === '0') {
        groupMap['置顶'].push(conversation)
        continue
      } else {
        groupMap['我的对话'].push(conversation)
      }
    }
    return groupMap
  }


  const conversationList = ref([])
  const conversationMap = ref({})
  const isConversationEmpty = computed(() => {
    return conversationList.value.length === 0
  })

  /** 获取对话列表 */
  const getChatConversationList = async () => {
    try {
      const { records } = await getKlModelSession({
        pageNum: 1,
        pageSize: 999,
        // 是否临时对话
        isTemporary: false
      })

      // 1.1 获取 对话数据
      conversationList.value = records
      // 1.2 排序
      conversationList.value.sort((a, b) => {
        return b.createTime - a.createTime
      })
      // // 1.3 没有任何对话情况
      if (conversationList.value.length === 0) {
        // activeConversationId.value = null
        conversationMap.value = {}
        return
      }
      // 2. 对话根据时间分组(置顶、今天、一天前、三天前、七天前、30 天前)
        conversationMap.value = await getConversationGroupByCreateTime(conversationList.value)
        console.log('conversationMap.value', conversationMap.value);
        
      } finally {
        console.log('加载');
        
      }
    }
  const ChatMessageDialogRef = ref(null)
  const handleChooseChat = (chatItem) => {
    console.log('handleChooseChat', chatItem);
    ChatMessageDialogRef.value && ChatMessageDialogRef.value.open(chatItem)
  }

  const ChatListDialogRef = ref(null)
  const handleMoreChats = () => {
    ChatListDialogRef.value && ChatListDialogRef.value.open()
  }

  const docListDialogRef = ref(null)
  const handleMoreDocs = () => {
    docListDialogRef.value && docListDialogRef.value.open(true)
  }

  const getdocsList = async () => {
    const response = await TemplateAPI.getCloudPage({
      pageNum: 1,
      pageSize: 10,
      isfile: 1
    });
    rankList.value = response.records
  }

  const tagDocmentsRef = ref()
  const tagClick = (tagParams) => {
    tagDocmentsRef.value.open(tagParams)
  }

  const homeprofileRef = ref()
  const toProfile = async () => {
    homeprofileRef.value && homeprofileRef.value.open()
  }

  const loginOut = async () => {
    try {
      await ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
      await userStore.loginOut()
      tagsViewStore.delAllViews()
      replace('/login?redirect=/index')
    } catch {}
  }

  const myDocumentListRef = ref()
  const handleShowDocument = (type) => {
    myDocumentListRef.value && myDocumentListRef.value.open(type)
  }

  // activeName监听
  watch(activeName, (newValue) => {
    if (newValue === 'docs') {
      getdocsList()
    }
    if (newValue === 'likes') {
      getHomeDownloadCountList()
    }
  })

  const docViewRef = ref()
  const handleView = (item) => {
    docViewRef.value && docViewRef.value.open('view', {}, item, true, '我的文档');
  }

  const searchDocList = ref([])
  const getSearchDocList = async () => {
    const response = await homeApi.getFileEventLog({
      pageNum: 1,
      pageSize: 5,
      creator: loginUser.id,
    })
    searchDocList.value = response.records
  }


  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    getChatConversationList()
    setTimeout(() => {
      getdocsList()
    }, 100);
  })

  onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
  })
  </script>

  <style lang="scss">
  /* 勿动 */
    .my-popover {
      min-width: 100px !important;
    }
  </style>
  
  <style scoped>
  .rank-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    max-width: 100%;
    padding-bottom: 36px;
    overflow: hidden;
  }

  .rank-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    cursor: pointer;
    width: 100%;
  }

  .rank-item:hover {
    color: var(--el-color-primary);
  }

  .rank-number {
    min-width: 20px;
    text-align: center;
  }

  .rank-number.top-three {
    color: var(--el-color-primary);
    font-weight: bold;
  }

  .rank-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 40px);
  }
  </style>
  
  <style scoped>
  .right-container {
    width: 100%;
    max-width: 500px;
    /* background-color: #f5f7f9; */
    
  }

  .right-content {
    width: 100%;
    margin-top: 10px;
    /* padding: 0 16px; */
  }

  .rank-section {
    width: 100%;
  }

  .tab-container {
    width: 100%;
    overflow-x: auto;
    flex-wrap: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tab-container::-webkit-scrollbar {
    display: none;
  }
  </style>
  
  <style lang="scss" scoped>

  .shortcut-title {
    font-size: 16px;
    font-weight: 500;
    height: 48px;
    line-height: 48px;
    padding: 0 10px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }


  .shortcut-item {
    height: 40px;
  }

  .shortcut-link {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    text-decoration: none;
    color: inherit;
    height: 100%;
    transition: all 0.3s;
    padding: 0 8px;
  }

  .shortcut-link:hover {
    color: var(--el-color-primary);
    transform: translateY(-2px);
  }

  .shortcut-icon {
    font-size: 16px;
    margin-right: 8px;
  }

  .shortcut-name {
    font-size: 14px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }
  </style>
  
  <style scoped>
  .search-area {
    padding: 24px;
    height: 90px;
    display: flex;
    align-items: center;
    position: relative;
  }

  .search-header {
    width: 100%;
  }

  .search-header .el-input__wrapper {
    box-shadow: none;
    height: 48px;
    line-height: 48px;
  }

  .search-header .el-input__inner {
    height: 48px;
    line-height: 48px;
  }

  .search-header .el-select .el-input__wrapper {
    height: 46px;
    line-height: 46px;
  }

  .search-header .el-input-group__prepend {
    background-color: #fff;
  }

  .search-header .el-input__prefix {
    color: var(--el-text-color-secondary);
    font-size: 18px;
  }

  .search-header .el-input-group__prepend {
    padding: 0;
    background-color: white !important;
    border-right: 1px solid var(--el-border-color);
  }

  .search-header .el-select {
    width: 110px !important;
  }

  .search-header .el-select .el-input {
    width: 110px !important;
  }

  .search-header .el-input-group__prepend .el-input__wrapper {
    border: none;
  }

  .mb-16px {
    margin-bottom: 16px;
  }
  </style>
  
  <style scoped>
  .search-history {
    margin-bottom: 20px;
    padding-right: 10px;
  }

  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .history-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .history-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fff;
    font-size: 14px;
    color: var(--el-text-color-regular);
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 4px;
  }

  .history-item:hover {
    background: var(--el-fill-color-light);
  }

  .history-item .el-icon {
    margin-right: 8px;
    font-size: 16px;
    color: #909399;
  }

  .history-text {
    flex: 1;
  }

  .history-item .delete-icon {
    margin-left: 8px;
    font-size: 16px;
    color: #909399;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .history-item:hover .delete-icon {
    opacity: 1;
  }

  .history-doc {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #fff;
    font-size: 14px;
    color: var(--el-text-color-regular);
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 4px;
  }

  .history-doc .el-icon {
    margin-right: 8px;
    font-size: 24px;
    color: #909399;
  }

  .history-doc .doc-desc {
    flex: 1;
  }
  .doc-text-time {
    font-size: 12px;
  }
  .doc-text-title {
    height: 24px;
    line-height: 24px;
  }

  .doc-types {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    border-top: none;
  }

  .type-header {
    margin-bottom: 12px;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
  .checkbox {
    margin-right: 16px;
  }
  .search-view {
    background-color: #fff;
  }
  </style>

<style lang="scss" scoped>
.home-container {
  width: 100%;
  padding: 10px;
  //padding-top: 0;
  .el-menu--horizontal > .el-menu-item:nth-child(1) {
    margin-right: auto;
  }
  .home-header {
    display: flex;
    position: relative;
    .search-header {
      flex: 1;
      // position: relative;
      // margin-right: 20px;
    }
    .search-append {
      display: flex;
      align-items: center;
    }
    .search-dropdown {
      position: absolute;
      top: 41px;
      background: white;
      border: 1px solid var(--el-border-color-light);
      border-top: none;
      border-radius: 0 0 8px 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      z-index: 2000;
      width: 100%;
      padding: 10px 4px 4px 16px;
      overflow-y: auto;
    }
    .home-create {
      width: 40px;
    }
  }
  .home-body {
    display: flex;
    width: 100%;
    .left-side {
      flex: 1;
      position: relative;
    }
    .right-side {
      width: 30%;
      min-width: 300px;
      .knowledge-lebel {
        cursor: pointer;
      }
    }
  }
}
.search-header  {
  :deep(.el-input-group__prepend)  {
    background-color: #fff;
    color: #fff;
  }
  :deep(.el-input-group__append) {
    background-color: #fff;
    padding: 0;
  }
  :deep(.el-divider--vertical) {
    margin: 0;
  }
}
.empty-tips {
  padding: 10px;
  font-size: 14px;
}
// 修改
.shortcut-section, .rank-section {
  background-color: #fff;
  .shortcut-title {
    font-size: var(--el-font-size-base);
    font-weight: normal;
    color: var(--el-text-color-primary);
    border-bottom: 2px solid var(--el-border-color-lighter);
  }
}
.knowledge-tabs {
  width: 100%;
  :deep(.el-tabs__nav-wrap) {
    padding-left: 10px;
    height: 48px;
  }

  :deep(.el-tabs__item) {
    padding: 0 8px;
    height: 48px;
  }
}
.knowledge-item {
  padding: 10px 20px;
  color: var(--el-text-color-primary);
  .folder-name {
    font-size: var(--el-font-size-base);
    line-height: 24px;
    margin-left: -10px;
    font-weight: 500;
  }
  .knowledge-lebel {
    font-size: 12px;
    span {
      margin-right: 5px;
      line-height: 32px;
    }
  }
}
.knowledge-content {
  .shortcut-section {
    margin-top: 10px;
  }
}

.button-items {
  display: flex;
  align-items: center;
  text-align: left;
  height: 32px;
  cursor: pointer;
  width: 100%;
  padding-left: 5px;
}

.last-menu-item {
  margin-left: auto;
  :deep(.el-sub-menu__icon-arrow) {
    display: none !important;
  }
  :deep(.el-sub-menu__title) {
    margin-right: -40px !important;
  }
}
.menu-logo {
  opacity: 1;
  cursor: default;
}
.my-menu {
  border-bottom: none;
}
</style>
<style>
.my-popper {
  margin-right: 5px;
}
.el-menu--horizontal.el-menu--popup-container {
  margin-right: 0;
}
</style>
