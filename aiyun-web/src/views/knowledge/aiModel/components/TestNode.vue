<template>
    <div class="custom-node">
      <input
        v-model="inputValue"
        @input="handleInputChange"
        placeholder="Enter something"
      />
    </div>
  </template>
  
  <script setup>
  import { ref, watch } from 'vue';
  import {  useVueFlow } from "@vue-flow/core";
  const { updateNodeData } = useVueFlow()
  
  // 接收 props
  const props = defineProps({
    data: {
      type: Object,
      required: true,
    },
		id: {
			type: String,
			required: true,
		},
  });
  
  // 初始化 input 的值
  const inputValue = ref(props.data.inputValue || '');
  
  // 监听 input 的变化
  const handleInputChange = (event) => {
    inputValue.value = event.target.value;
    console.log('Input changed:', props.id);
    console.log('Input changed:', props);
    // 更新节点的 data
    updateNodeData(props.id, {
        inputValue: inputValue.value,
    });
  };
  
  // 监听 node.data.inputValue 的变化（可选）
  // watch(
  //   () => props.data.inputValue,
  //   (newValue) => {
  //     inputValue.value = newValue;
  //   }
  // );
  </script>
  
  <style scoped>
  .custom-node {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
  }
  </style>