<template>
  <Dialog 
    v-model="dialogVisible" 
    :scroll="true"
    :before-close="beforeClose"
    :title="dialogTitle">
    <el-form
      ref="formRef"
      class="form-container"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      label-position="top"
    >
      <!-- 文件上传行 -->
      <el-form-item label="文件上传" prop="fileName">
        <div class="file-upload-wrapper">
          <el-input 
            v-model="formData.fileName" 
            placeholder="请选择要上传的文件"
            readonly
            class="file-input"
          >
            <template #append>
              <el-button 
                type="primary" 
                @click="triggerFileInput"
                :loading="uploading || isParse"
              >
                {{ isParse ? '解析中' : uploading ? '上传中...' : '选择文件' }}
              </el-button>
            </template>
          </el-input>
          <input
            type="file"
            ref="fileInput"
            @change="handleFileSelect"
            style="display: none"
            accept=".doc,.docx,.pdf,.png,.jpg,.jpeg,.xlsx, .ppt, .pptx, .txt, .wps"
          />
        </div>
      </el-form-item>

      <el-form-item label="文件名" prop="localName">
        <el-input v-model="formData.localName" placeholder="请输入文件名" />
      </el-form-item>
      <el-form-item label="标签" prop="label">
        <TagInput 
          v-model="formData.label" 
          :default-tags="defaultTags"
          placeholder="请输入标签"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="formData.projectName" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="所属人" prop="knowledgeOwner">
        <el-input v-model="formData.knowledgeOwner" placeholder="请输入所属人" />
      </el-form-item>
      <el-form-item label="所属部门" prop="department">
        <el-input v-model="formData.department" placeholder="请输入所属部门" />
      </el-form-item>
      <el-form-item label="知识所属年份" prop="year">
        <el-date-picker
          v-model="formData.year"
          type="year"
          placeholder="请选择知识所属年份"
        />
      </el-form-item>
      <!-- <el-form-item label="主题" prop="topic">
        <TagInput 
          v-model="formData.topic" 
          :default-tags="defaultTags"
          placeholder="请输入主题"
        />
      </el-form-item> -->

      <!-- 关键词 -->
      <!-- <el-form-item label="关键词" prop="keyword">
        <TagInput 
          v-model="formData.keyword" 
          :default-tags="defaultTags"
          placeholder="请输入主题"
        />
      </el-form-item> -->

      <!-- 文档下载权限 -->
      <!-- <el-form-item label="下载权限" prop="downloadPermission">
        <el-radio-group v-model="formData.downloadPermission">
          <el-radio :value="1">允许下载</el-radio>
          <el-radio :value="2">申请下载</el-radio>
        </el-radio-group>
      </el-form-item> -->

      <!-- 知识推荐 -->
      <!-- <el-form-item label="知识推荐" prop="knowledgeRecommend">
        <el-radio-group v-model="formData.knowledgeRecommend">
          <el-radio :value="1">推荐</el-radio>
          <el-radio :value="2">不推荐</el-radio>
        </el-radio-group>
      </el-form-item> -->

      <!-- 知识摘要 -->
      <!-- <el-form-item label="知识摘要" prop="knowledgeAbstract">
        <el-input
          v-model="formData.knowledgeAbstract"
          type="textarea"
          :rows="4"
          placeholder="请输入知识摘要"
          maxlength="200"
          show-word-limit
        />
      </el-form-item> -->

    </el-form>
    <template #footer>
      <el-button :disabled="submitDisabled" :loading="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import TagInput from '@/components/TagInput/index.vue'
import { CommonStatusEnum } from '@/utils/constants'
import { useUserStore } from '@/store/modules/user'
import { defaultProps, handleTree } from '@/utils/tree'
import * as FolderAPI from '@/api/knowledge/docmangement'
import * as DeptApi from '@/api/system/dept'
import { update } from 'lodash'

import { v4 as uuidv4 } from 'uuid'

defineOptions({ name: 'UploadForm' })


const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userStore = useUserStore()

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const userInfo = userStore.getUser

// 下拉选项（示例数据）
const categoryOptions = ref([
  { value: 'tech', label: '技术文档' },
  { value: 'product', label: '产品文档' },
  { value: 'process', label: '流程规范' }
])

const departmentOptions = ref([
  { value: 'dev', label: '研发部' },
  { value: 'product', label: '产品部' },
  { value: 'hr', label: '人力资源部' }
])
const defaultTags = ref([])
const fileInput = ref(null)
const uploading = ref(false)
const formData = ref({
  fileName: '',
  filePath: '',
  fileUrl: '',
  versions: '',
  filePrefix: '',
  // knowledgeName: '',
  category: '',
  shareUser: '',
  projectName: '',
  department: '',
  year: '',
  // department: '',
  knowledgeOwner: userInfo.nickname,
  // firstLabel: '',
  // secondLabel: '',
  // downloadPermission: '',
  // knowledgeRecommend: '',
  knowledgeAbstract: '',
  label: [],
  // topic: [],
  // keyword: []
})
const props = defineProps({
  path: {
    type: Object,
    default: () => {
    }
  },
  createFolder: {
    type: Function,
    default: () => {
    }
  },
  updateFolder: {
    type: Function,
    default: () => {
    }
  }
})
const submitDisabled = ref(true)
const { path } = toRefs(props)
const formRules = reactive({
  fileName: [{ required: true, message: '请上传文件', trigger: 'blur' }],
  knowledgeName: [{ required: true, message: '知识名称不能为空', trigger: 'blur' }],
  localName: [
    { required: true, message: '知识名称不能为空', trigger: 'blur' },
    // /^[\u4e00-\u9fa5a-zA-Z0-9-]+$/
    { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9-]+$/, message: '只能包含中文、英文、数字、下划线', trigger: 'blur' }
  ],
  category: [{ required: true, message: '知识分类不能为空', trigger: 'change' }],
  shareUser: [{ required: true, message: '分享人不能为空', trigger: 'blur' }],
  knowledgeOwner: [{ required: true, message: '知识所属人不能为空', trigger: 'blur' }],
  firstLabel: [{ required: true, message: '主题不能为空', trigger: 'change' }],
  secondLabel: [{ required: true, message: '关键词不能为空', trigger: 'change' }],
  downloadPermission: [{ required: true, message: '下载权限不能为空', trigger: 'change' }],
  knowledgeRecommend: [{ required: true, message: '知识推荐不能为空', trigger: 'change' }],
  // projectName: [{ required: true, message: '所属项目不能为空', trigger: 'blur' }],
  // department: [{ required: true, message: '所属部门不能为空', trigger: 'change' }],
  // year: [{ required: true, message: '年份不能为空', trigger: 'change' }],
  // knowledgeAbstract: [{ required: true, message: '知识摘要不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const deptList = ref([]) // 树形结构

// 获取部门树
// const getTree = async () => {
//   const res = await DeptApi.getSimpleDeptList()
//   deptList.value = res
// }

const isCommon = ref('0')
/** 打开弹窗 */
const open = async (common, row = {}) => {
  resetForm()
  isCommon.value = common
  dialogVisible.value = true
  isParse.value = false
  dialogTitle.value = row.id ? '编辑' : '新增'
  if (row.id) {
    submitDisabled.value = false
    const fileName = row.fileName
    const lastDotIndex = fileName.lastIndexOf('.');
    formData.value = { ...row }
    formData.value.label = row.label ? row.label : []
    formData.value.localName = lastDotIndex === -1 ? fileName : fileName.substring(0, lastDotIndex);
    formData.value.suffix = lastDotIndex === -1 ? '' : fileName.substring(lastDotIndex + 1)
  }
  getLabel()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    
    const filePath = path.value.id ? path.value.filePath + '/' + `${data.localName}.${data.suffix}` : null
    const params = { 
      ...data,
      fileName: `${data.localName}.${data.suffix}`,
      filePath,
      // saveStatus: '1',
      isCommon: isCommon.value,
      datasetId: path.value.datasetId,
      parentId: path.value.id ? path.value.id : null,
      level: path.value.id ? parseInt(path.value.level) + 1 : '1',
      analysisStatus:  "2",
      ...analysData.value
    }
    // const response = await FolderAPI.createFolder(params)
    const response = data.id ? await props.updateFolder(params) : await props.createFolder(params)
    dialogVisible.value = false
    message.success('文件上传成功')
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click()
}

const timer = ref(null)

const analysData = ref({})
const isParse = ref(false)
// 处理文件选择
const handleFileSelect = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  submitDisabled.value = true
  // 验证文件
  if (!validateFile(file)) return
  
  // 文件大小限制
  if (file.size > 50 * 1024 * 1024) {
    message.error('文件大小不能超过 50MB!')
    event.target.value = ''; // 清空文件选择
    return
  }
  if (file.size < 1) {
    message.error('文件内容不能为空!')
    event.target.value = ''; // 清空文件选择
    return
  }

  // 执行上传
  try {
    uploading.value = true
    console.log(file.name);
    const oldFileName = file.name
    const newFile = new File(
      [file], // 文件内容（Blob/ArrayBuffer）
      uuidv4() + file.name, // 新文件名
      {
        type: file.type, // 保持原 MIME 类型
        lastModified: file.lastModified, // 保持原修改时间
      }
    );
    const fileResponse = await uploadFile(newFile, oldFileName)
    const { filePath, fileName, fileUrl, versions, filePrefix} = fileResponse.data
    const params = {
      fileUrl,
      type: isCommon.value === '0' ? '1' : '2',
      datasetId: path.value.datasetId,
    }
    const analyResponse = await FolderAPI.getCloudAnalysisBack(params)
    isParse.value = true
    timer.value = setInterval(async () => {
      const resultResponse= await FolderAPI.getCloudAnalysisBackResults({
        ...analyResponse
      })
      if (resultResponse.analysisStatus !== '1') {
        analysData.value = resultResponse
        submitDisabled.value = false
        isParse.value = false
        clearInterval(timer.value)
      }
    }, 20000)
    const lastDotIndex = fileName.lastIndexOf('.');
    // analysData.value = await FolderAPI.getCloudAnalysisBack(params)
    formData.value.fileName = fileName // 上传成功后设置文件名
    formData.value.filePath = filePath
    // formData.value.knowledgeName = fileName.split('.')[0]
    formData.value.localName = fileName.substring(0, lastDotIndex)
    formData.value.suffix = fileName.split('.').pop()?.toLowerCase() || 'docx'
    formData.value.fileUrl = fileUrl
    formData.value.versions = versions
    formData.value.filePrefix = filePrefix
    ElMessage.success('文件上传成功')
  } catch (error) {
    ElMessage.error('文件上传失败')
    console.error('上传失败:', error)
  } finally {
    uploading.value = false
    fileInput.value.value = '' // 清空input
  }
}

// 文件验证
const validateFile = (file) => {
  // .doc,.docx,.pdf,.png,.jpg,.jpeg,.xlsx, .ppt, .pptx, .txt
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/png',
    'image/jpeg',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain'
  ]
  const name = file.name  
  const lastDotIndex = name.lastIndexOf('.');
  const fileType = lastDotIndex === -1 ? '' : name.substring(lastDotIndex + 1)
  if(fileType === 'wps')  {
    return true
  }
  
  if (!allowedTypes.includes(file.type)) {
    message.error('仅支持 PDF/Word/Excel/PPT/TXT/PNG/JPG/JPEG 格式!')
    return false
  }

  return true
}

// 文件上传 FolderAPI.uploadFolderFile
const uploadFile = async(file, oldFileName) => {
  const filePath = path.value.id ? path.value.filePath + '/' + oldFileName : null
  const newPath = path.value.id ? path.value.filePath + '/' + file.name : null
  return new Promise((resolve, reject) => {
    FolderAPI.uploadFolderFile({
      file,
      path: filePath,
      fileName: oldFileName,
      newPath
    }).then((res) => {
      if (res.code === 200) {
        resolve(res)
      } else {
        reject(res)
      }
    })
  })
  
}

// 获取标签
const getLabel = async () => {
  const res = await FolderAPI.getKlDatasetById(path.value.knowledgeId)
  defaultTags.value = res.label || []
}

const beforeClose = (done) => {
  if (formLoading.value) {
    ElMessage.warning('正在提交中，请稍后')
    return
  }
  if (uploading.value) {
    ElMessage.warning('文件正在上传中，请稍后')
    return
  }
  if (timer.value) {
    clearInterval(timer.value)
  }
  done()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    fileName: '',
    filePath: '',
    fileUrl: '',
    versions: '',
    filePrefix: '',
    knowledgeName: '',
    category: '',
    projectName: '',
    shareUser: userInfo.nickname,
    department: '',
    knowledgeOwner: userInfo.nickname,
    firstLabel: '',
    secondLabel: '',
    downloadPermission: '',
    knowledgeRecommend: '',
    knowledgeAbstract: '',
    label: [],
    // topic: [],
    // keyword: []
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
  .form-container {
    padding: 0 20px;
  }
  .file-upload-wrapper {
    width: 100%;
    max-width: 400px;
  }

.file-input {
  :deep(.el-input-group__append) {
    padding: 0 20px;
    border: none;
  }
  
  :deep(.el-input__inner) {
    cursor: default;
    background-color: #fff;
  }
}

.el-upload__tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}
</style>
