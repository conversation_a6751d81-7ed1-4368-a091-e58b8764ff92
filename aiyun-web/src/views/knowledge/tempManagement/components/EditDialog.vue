<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="title" 
    fullscreen
    :close-on-click-modal="false"
    :close-on-press-escape="false" 
    :destroy-on-close="true"
    :before-close="handleBeforeClose"
    class="ww-edit-dialog">
    <div class="content-body">
      <el-row :gutter="20" v-loading="loading">
        <el-col :span="!isCollapsed ? 18 : 24">
          <div :class="!isCollapsed ? 'content' : 'content pr-60px'" v-if="dialogVisible">
            <DocumentEditor id="docEditor" :documentServerUrl="`${apikeys.onlyoffice_url}`" :config="config" />
          </div>
        </el-col>
        <el-col :span="!isCollapsed ? 6 : 0" class="form-col">
          <el-form>
            <el-scrollbar class="scrollbar">
              <!-- 文档标题 -->
              <h4 class="ismust">文档主题 </h4>
              <el-form-item>
                <el-input v-model="formData.title" maxlength="50"/>
              </el-form-item>
              <div class="step-title">
                <h4 class="ismust">选择模板</h4>
                <el-button size="small" type="primary" @click="handleTemplate">查看模板</el-button>
              </div>
              <el-form-item>
                <el-select v-model="formData.documentTemplate" placeholder="请选择模板">
                  <el-option 
                    v-for="item in tempOptions" 
                    :key="item.id" 
                    :label="item.templateFileName"
                    :value="item.id" />
                </el-select>
              </el-form-item>
              <div>
                <div class="step-title">
                  <h4 class="ismust">设置文档范围</h4>
                  <el-button size="small" type="primary" @click="handleChoose">选择文件</el-button>
                </div>
              </div>
              <div v-if="chooseList.length > 0" style="margin: 10px 0;">
                <h4>已选择文件：</h4>
                <ul>
                  <li v-for="file in chooseList" :key="file.id" class="file-item">
                    <el-icon class="file-icon">
                      <Document />
                    </el-icon>
                    <span class="file-name">{{ file.fileName }}</span>
                    <el-button type="danger" :icon="Delete" circle size="small" @click="handleRemove(file)" />
                  </li>
                </ul>
              </div>
              <div>
                <div class="step-title">
                  <h4 >招标检索</h4>
                  <el-button size="small" type="primary" @click="handleChooseSearch">选择文件</el-button>
                </div>
              </div>
              <div v-if="chooseSearchList.length > 0" style="margin: 10px 0;">
                <h4>已选择文件：</h4>
                <ul>
                  <li v-for="file in chooseSearchList" :key="file.id" class="file-item">
                    <el-icon class="file-icon">
                      <Document />
                    </el-icon>
                    <span class="file-name">{{ file.fileName }}</span>
                    <el-button type="danger" :icon="Delete" circle size="small" @click="handleSearchRemove(file)" />
                  </li>
                </ul>
              </div>
            </el-scrollbar>
          </el-form>
          <div class="button-group">
            <el-button type="primary" @click="submitForm">生成文档</el-button>
            <el-button @click="closeDialog">取 消</el-button>
          </div>
        </el-col>
      </el-row>
      <!-- 展开按钮 -->
       <el-button @click="toggleCollapse" class="collapse-btn" type="text">
        <Icon :icon="isCollapsed ? 'ep:d-arrow-left' : 'ep:d-arrow-right'" />
        <!-- <i :class="isCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i> -->
        {{ isCollapsed ? '展开' : '收起' }}
      </el-button>
    </div>
    <el-dialog title="模板预览" v-model="templateDialogVisible" width="80%">
      <div class="template-editor" style="height: 600px;">
        <DocumentEditor id="templateEditor" :documentServerUrl="`${apikeys.onlyoffice_url}`" :config="templateConfig" />
      </div>
    </el-dialog>
    <ChooseFile ref="chooseFileRef" localDocName="documentChooseList" localKnowName="knowledgeChooseList" @success="getChooseList" />
    <ChooseFile ref="chooseSearchFileRef" localDocName="documentSearchChooseList" localKnowName="knowledgeSearchChooseList" :limit="1" @success="getChooseSearchList" />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { DocumentEditor } from '@onlyoffice/document-editor-vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useUserStore } from '@/store/modules/user'
import * as UserAPI from '@/api/system/user'
import * as TemplateAPI from '@/api/knowledge/createTem'
import { v4 as uuidv4 } from 'uuid'
import { useMessage } from '@/hooks/web/useMessage'
import { Delete, Document } from '@element-plus/icons-vue'
import ChooseFile from '../../docGeneration/ChooseFile.vue'
import * as TemplateAPI1 from '@/api/knowledge/tempmangement'

const message = useMessage()
const userStore = useUserStore()
const apikeys = userStore.getAppConfig

const dialogVisible = ref(false)
const templateDialogVisible = ref(false)
const loading = ref(false)
const title = ref('')
const tempOptions = ref([])
const chooseList = ref([])
const chooseSearchList = ref([])
const chooseFileRef = ref()
const chooseSearchFileRef = ref()
const docKey = ref('')
const docTitle = ref('')

const emit = defineEmits(['success'])



const formData = reactive({
  title: '',
  documentType: '',
  documentTemplate: ''
})

const config = ref({
  document: {
    fileType: 'docx',
    key: uuidv4(),
    title: '技术规范书',
    url: `${apikeys.file_server_url}/init.docx`
  },
  documentType: 'word',
  editorConfig: {
    lang: "zh-CN",
    mode: 'edit',
    callbackUrl: `${apikeys.file_callback_url}`,
    // callbackUrl: `http://aiyun.frp.yn.asqy.net/admin-api/v1/kownledge/common/onlyOfficeSave`,
    customization: {
      commentAuthorOnly: false,
      comments: true,
      compactHeader: false,
      compactToolbar: true,
      feedback: false,
      plugins: true,
      autosave: true,
      forcesave: true
    },
  }
})

const templateConfig = ref({
  document: {
    fileType: 'docx',
    key: uuidv4(),
    title: '技术规范书',
    url: `${apikeys.file_server_url}/init.docx`
  },
  documentType: 'word',
  editorConfig: {
    lang: "zh-CN",
    mode: 'view'
  }
})

const getTemplateList = async () => {
  try {
    const res = await TemplateAPI1.getCloudList()
    tempOptions.value = res.map(item => ({
      ...item,
      id: `${item.id}`,
    }))
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  }
}

const isCollapsed = ref(false)
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleTemplate = () => {
  if (!formData.documentTemplate) {
    message.warning('请先选择模板')
    return
  }
  const template = tempOptions.value.find(t => t.id === formData.documentTemplate)
  console.log(template);
  if (template) {
    templateConfig.value.document.url = template.templateFileUrl
    templateConfig.value.document.title = template.templateFileName
    templateConfig.value.document.key = uuidv4()
    templateDialogVisible.value = true
  }
}

const handleChoose = () => {
  chooseFileRef.value.open()
}

const handleChooseSearch = () => {
  chooseSearchFileRef.value.open()
}

const getChooseList = (list) => {
  chooseList.value = list
}
const getChooseSearchList = (list) => { 
  if(list.length > 1) {
    message.warning('只能选择一个文件')
    return
  }
  chooseSearchList.value = list
}

const handleRemove = (file) => {
  const index = chooseList.value.findIndex(item => item.id === file.id)
  if (index !== -1) {
    chooseList.value.splice(index, 1)
  }
  const documentChooseList = JSON.parse(localStorage.getItem('documentChooseList'))
  const knowledgeChooseList = JSON.parse(localStorage.getItem('knowledgeChooseList'))
  const documentIndex = documentChooseList.findIndex((item) => item.id === file.id)
  const knowledgeIndex = knowledgeChooseList.findIndex((item) => item.id === file.id)
  if (documentIndex !== -1) {
    documentChooseList.splice(documentIndex, 1)
    localStorage.setItem('documentChooseList', JSON.stringify(documentChooseList))
  }
  if (knowledgeIndex !== -1) {
    knowledgeChooseList.splice(knowledgeIndex, 1)
    localStorage.setItem('knowledgeChooseList', JSON.stringify(knowledgeChooseList))
  }
}

const handleSearchRemove = (file) => { 
  const index = chooseSearchList.value.findIndex(item => item.id === file.id)
  if (index !== -1) {
    chooseSearchList.value.splice(index, 1)
  }
  const documentChooseList = JSON.parse(localStorage.getItem('documentSearchChooseList'))
  const knowledgeChooseList = JSON.parse(localStorage.getItem('knowledgeSearchChooseList'))
  const documentIndex = documentChooseList.findIndex((item) => item.id === file.id)
  const knowledgeIndex = knowledgeChooseList.findIndex((item) => item.id === file.id)
  if (documentIndex !== -1) {
    documentChooseList.splice(documentIndex, 1)
    localStorage.setItem('documentSearchChooseList', JSON.stringify(documentChooseList))
  }
  if (knowledgeIndex !== -1) {
    knowledgeChooseList.splice(knowledgeIndex, 1)
    localStorage.setItem('knowledgeSearchChooseList', JSON.stringify(knowledgeChooseList))
  }
}
const docId = ref('')
const open = async (row) => {
  dialogVisible.value = true
  loading.value = true
  
  try {
    const { id: userId, username: userName } = await getUserInfo()
    await getTemplateList()
    // 设置表单数据
    formData.title = row?.docTitle || ''
    formData.documentType = row?.filePrefix || ''
    formData.documentTemplate = row?.klTemplateId ||row?.templateId || ''
    docId.value = row?.id || ''
    chooseList.value = row.scope || []
    chooseSearchList.value = row.searchFile  ? [row.searchFile] : []
    // 设置编辑器配置
    docKey.value = uuidv4() + '-klDoc'
    const response = await TemplateAPI.updateFolder({
      id: row.id,
      fileKey: docKey.value,
    })
    docTitle.value = row.fileName ? row.fileName.includes('.docx') ? row.fileName : row.fileName + '.docx' : docKey.value + '.docx'
    config.value = {
      document: {
        ...config.value.document,
        key: docKey.value,
        title: row.fileName ? row.fileName : docTitle.value,
        url: row.fileUrl ? row.fileUrl : `${apikeys.file_server_url}/init.docx`,
        permissions: {
          comment: true,
          download: true,
          modifyContentControl: true,
          modifyFilter: true,
          print: false,
          edit: true,
          fillForms: true,
          review: true
        }
      },
      documentType: "word",
      editorConfig: {
        ...config.value.editorConfig,
        user: {
          id: userId,
          name: userName
        }
      }
    }

    title.value = row ? '编辑文档' : '新建文档'
  } catch (error) {
    console.error('初始化失败:', error)
    message.error('初始化失败')
  } finally {
    loading.value = false
  }
}

const getUserInfo = async () => {
  const response = await UserAPI.getUserProfileById(userStore.getUser.id)
  return response
}
const timer = ref(null)
const submitForm = async () => {
  console.log(formData);
  if(!formData.documentTemplate) {
    message.warning('请先选择模板')
    return
  }
  if(!formData.title) {
    message.warning('请输入文档主题')
    return
  }
  if(!chooseList.value.length) {
    message.warning('请选择文档范围')
    return
  }
  const params = {
    docTitle: formData.title,
    fileUrl: config.value.document.url,
    klTemplateId: formData.documentTemplate,
    filePrefix: formData.documentType,
    fileName: docTitle.value,
    fileKey: docKey.value,
    scope: chooseList.value,
    id: docId.value,
  }
  // searchFile: chooseSearchList.value ? chooseSearchList.value : {}
  if(chooseSearchList.value && chooseSearchList.value.length) {
    params.searchFile = chooseSearchList.value[0]
  }
  try {
    loading.value = true
    const response = await TemplateAPI.createDoc(params)
    if(response) {
      loading.value = false
      dialogVisible.value = false
    } else {
      message.error('生成失败')
    }
    // timer.value = setInterval(async () => {
    //   const resultResponse = await TemplateAPI.getDocByKey(response.data)
    //   const resultData = JSON.parse(resultResponse.data)
    //   if (resultData.status === 1) {
    //     await showAiDoc(resultData.msg)
    //     loading.value = false
    //     clearInterval(timer.value)
    //   } else if( resultData.status === 2) {
    //     message.error('生成失败')
    //     clearInterval(timer.value)
    //   }
    // }, 10000)
  } catch (error) {
    message.error('生成失败')
    loading.value = false
    clearInterval(timer.value)
    console.log('createDoc error', error);
  }
}

const showAiDoc = async (response) => {
  clearLocal()
  const { fileKey, fileUrl, fileName } = response
  const { id: userId, username: userName } = await getUserInfo()
  config.value = {
    document: {
      fileType: 'docx',
      key: fileKey,
      title: fileName,
      url: fileUrl,
      permissions: {
        comment: true,
        download: true,
        modifyContentControl: true,
        modifyFilter: true,
        print: false,
        edit: true,
        fillForms: true,
        review: true
      }
    },
    documentType: "word",
    editorConfig: {
      lang: "zh-CN",
      callbackUrl: `${apikeys.file_callback_url}`,
      customization: {
        commentAuthorOnly: false,
        comments: true,
        compactHeader: false,
        compactToolbar: true,
        feedback: false,
        plugins: true,
        autosave: true,
        forcesave: true
      },
      user: {
        id: userId,
        name: userName
      },
    }
  }
}

const handleBeforeClose = (done) => {
  // 关闭定时器
  if(timer.value) {
    clearInterval(timer.value)
  }
  emit('success')
  done()
}

const closeDialog = () => {
  if(timer.value) {
    clearInterval(timer.value)
  }
  emit('success')
  dialogVisible.value = false
}

onUnmounted(() => {
  if(timer.value) {
    clearInterval(timer.value)
  }
});

const clearLocal = () => {
  localStorage.removeItem('documentChooseList');
  localStorage.removeItem('knowledgeChooseList');
  localStorage.removeItem('documentChooseList');
  localStorage.removeItem('knowledgeChooseList');
}

onMounted(() => {
  window.onbeforeunload = function () {
    clearLocal()
  };
});

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.content-body {
  width: 100%;
  height: calc(100vh - 100px);
  position: relative;
}
.content {
  width: 100%;
  height: calc(100vh - 100px);
}

.scrollbar {
  height: calc(100vh - 130px);
}

.collapse-btn {
  position: absolute;
  right: 10px;
  top: 10px;
}

.form-col {
  background-color: #fff;
  padding: 10px;
  padding-top: 0;
  padding-bottom: 54px;
  position: relative;

  h4 {
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 10px;
  }
}

.button-group {
  position: absolute;
  margin-top: 20px;
  text-align: right;
  bottom: 10px;
  right: 10px;
}

.step-title {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  h4 {
    flex: 1;
    display: inline-block;
    margin: 0;
  }
}
.ismust::before {
  content: "*";
  color: var(--el-color-danger);
  margin-right: 4px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.file-icon {
  font-size: 18px;
  color: #409eff;
  margin-right: 8px;
}

.file-name {
  flex: 1;
  margin-right: 10px;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  margin: 5px 0;
  font-size: 14px;
  vertical-align: middle;
}
</style>
<style lang="scss">

</style>
