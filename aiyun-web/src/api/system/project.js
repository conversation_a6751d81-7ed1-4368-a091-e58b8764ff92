import request from '@/config/axios/requestRT'

// 查询角色列表
export function listRole(query) {
  return request.get({
    url: "/hours/role/list",
    params: query,
  });
}

// 修改角色
export function updateRole(data) {
  return request.put({
    url: "/hours/role",
    data: data,
  });
}

//查询工作类型下拉列表
export function getWorkTypeSelect() {
  return request.get({
    url: "/hours/project/work/list",
  });
}

// 删除角色
export function delRole(roleId) {
  return request.delete({
    url: "/hours/role/" + roleId,
  });
}

// 创建项目
export function createProject(data) {
  return request.post({
    url: "/hours/project/create",
    data: data,
  });
}

// 查询项目列表
export function listProject(query) {
  return request.get({
    url: "/hours/project/list",
    params: query,
  });
}
// 删除项目
export function delProject(roleId) {
  return request.delete({
    url: "/hours/project/remove?projectId=" + roleId,
  });
}
// 获取用户-项目经理
export function getBox() {
  return request.get({
    url: "/system/user/box",
  });
}
//获取状态字典
export function getDicttype(params) {
  return request.get({
    url: "/hours/dict/data/type/" + params,
  });
}
//按项目状态查询项目列表
export function getProjectStatus(params) {
  return request.get({
    url: "/hours/project/list?projectStatus=" + params,
  });
}
// 修改项目状态
export function updaProjectStatus(projectId, projectStatus) {
  const data = {
    projectId,
    projectStatus,
  };
  return request.put({
    url: "/hours/project/status",
    params: data,
  });
}
// 设置项目 暂停/启用 状态
export function enableProjectStatus(data) {
  return request.put({
    type: "form",
    url: "/hours/project/enable",
    data: data,
  });
}

//用户查询自己参与的项目
export function getMyProjectAll() {
  return request.get({
    url: "/hours/project/user/my/project/all",
  });
}
//用户查询按照状态自己参与的项目
export function getMyProjectStatus(params) {
  return request.get({
    url: "/hours/project/user/my/project/all?projectStatus=" + params,
  });
}
//查询我的工时列表
export function getMyHourList(endDate, startDate) {
  return request.get({
    url: `/hours/hour/list?endDate=${endDate}&startDate=${startDate}`,
  });
}
//查询查询用户缺报记录-分页
export function getMyHourListMiss(params) {
  return request.get({
    url: `/hours/hour/list/miss`,
    params,
  });
}
// 用户查询参与项目不包含已归档的项目
export function getMyActorProject(date = '') {
  return request.get({
    url: "/hours/project/user/my/project?date=" + date,
  });
}
// 用户填报提交工时
export function createHour(data) {
  return request.post({
    url: "/hours/hour/create",
    data: data,
  });
}

// 请假type为1/调休type为2接口
export function leaveHour(date, type) {
  return request.post({
    url: "/hours/hour/leave?leaveDate=" + date + "&leaveType=" + type,
  });
}
// 取消请假/调休接口
export function CancelleaveHour(id) {
  return request.post({
    url: "/hours/hour/unLeave?id=" + id,
  });
}
// 查询我的工时详情
export function getMyHourDetailt(id) {
  return request.get({
    url: "/hours/hour/detail?hourId=" + id,
  });
}
// 用户修改填报工时
export function updateHour(data) {
  return request.put({
    url: "/hours/hour/edit",
    data: data,
  });
}
// 我的统计
export function getHourStat(date) {
  return request.get({
    url: "/hours/hour/stat?date=" + date,
  });
}
// 我的统计-详细模式
export function getHourStatDetail(params) {
  return request.get({
    url: "/hours/hour/stat/detail",
    params,
  });
}
// 项目工时统计列表
export function projectHourStat(params) {
  return request.get({
    url: "/hours/project/hour/stat",
    // url:'/hours/project/hour/stat?projectStatus=',
    params,
  });
}
// 项目工时统计列表-按状态
export function projectHourStatStatus(params) {
  return request.get({
    url: "/hours/project/hour/stat",
    params,
  });
}
// 项目上报记录
export function projectHourStatFillDetail(params) {
  return request.get({
    url: `/hours/project/hour/stat/fill/detail?date=${params.date}&projectId=${params.projectId}`,
  });
}
// 项目工时统计列表详情-按月统计
export function projectHourMonth(id) {
  return request.get({
    url: "/hours/project/hour/stat/hour/month?projectId=" + id,
  });
}
// 项目工时统计列表详情-按月统计详情
export function projectHourMonthDetail(params) {
  return request.get({
    url: `/hours/project/hour/stat/hour/month/detail`,
    params,
  });
}
// 项目工时统计列表详情-按人统计
export function projectHourUser(id) {
  return request.get({
    url: `/hours/project/hour/stat/hour/user?projectId=${id}`,
  });
}
// 项目日报日历
export function getProjectHourdailyCalendar(params) {
  return request.get({
    url: "/hours/project/hour/daily/calendar",
    params,
  });
}
//项目日报日历详情
export function getProjectHourdailyCalendarDeatil(params) {
  return request.get({
    url: "/hours/project/hour/daily/calendar/detail",
    params,
  });
}
//项目日报-列表
export function getProjectHourDailyList(params) {
  return request.get({
    url: "/hours/project/hour/daily/list",
    params,
  });
}
//用户填报记录导出
export function getStatExport(params) {
  return request.get({
    url: "/hours/hour/stat/export",
    params,
  });
}
// 填报记录日历
export function getProjectHourCalendar(date) {
  return request.get({
    url: "/hours/hour/calendar?date=" + date,
  });
}
// 填报记录-按项目展示
export function getStatProject(date) {
  return request.get({
    url: "/hours/hour/stat/project?date=" + date,
  });
}

// 填报记录-按项目展示
export function getStatProjectUser(query) {
  return request.get({
    url: "/hours/hour/stat/project",
    params: query
  });
}

// 用户导出工时
export function getProjectworkHours(params) {
  return request.get({
    url: "hours/project/hour/stat/hour/month/detail/export",
    params,
    responseType: "blob",
  });
}

// 查询项目经理名称
export function getuserList() {
  return request.get({
    url: `hours/project/projectManager/list`,
  });
}

// 人才市场-查询列表
export function getPersonList() {
  return request.get({
    url: `hours/project/talentMarket`,
  });
}

// 项目管理 - 变更管理 - 获取列表
export function getChangeList(data) {
  return request.post({
    url: `project/change/list`,
    data,
  });
}
// 项目管理 - 变更管理 - 新增
export function getChange(data) {
  return request.post({
    url: `project/change`,
    data,
  });
}
// 项目管理 - 变更管理 - 删除
export function getChangeDelete(ids) {
  return request.delete({
    url: `project/change/${ids}`,
  });
}

// 项目管理 - 文档管理 - 获取列表
export function getFileList(data) {
  return request.post({
    url: `project/file/list`,
    data,
  });
}
// 项目管理 - 文档管理 - 新增
export function getFile(data) {
  return request.post({
    url: `project/file`,
    data,
  });
}
// 项目管理 - 文档管理 - 删除
export function getFileDelete(ids) {
  return request.delete({
    url: `project/file/${ids}`,
  });
}

// 项目管理 - 合同管理 - 获取合同金额
export function getCollectionInfoMoney(id) {
  return request.get({
    url: `project/collection/info/${id}`,
  });
}
// 项目管理 - 合同管理 - 合同金额修改
export function putCollectionInfo(data) {
  return request.put({
    url: `project/collection/info`,
    data,
  });
}
// 项目管理 - 合同管理 - 合同文件列表
export function getCollectionFileList(data) {
  return request.post({
    url: `project/collection/file/list`,
    data,
  });
}
// 项目管理 - 合同管理 - 合同文件删除
export function collectionFileDelete(ids) {
  return request.delete({
    url: `project/collection/file/${ids}`,
  });
}
// 项目管理 - 合同管理 - 合同文件新增
export function collectionFile(data) {
  return request.post({
    url: `project/collection/file`,
    data,
  });
}
// 项目管理 - 合同管理 - 收款计划新增
export function getCollectionPlanCreate(data) {
  return request.post({
    url: `project/collection/plan`,
    data,
  });
}
// 项目管理 - 合同管理 - 收款计划上传发票
export function uploadInvoice(data) {
  return request.post({
    url: `project/collection/plan/file`,
    data,
  });
}
// 项目管理 - 合同管理 - 收款计划修改
export function getCollectionPlanModiy(data) {
  return request.put({
    url: `project/collection/plan`,
    data,
  });
}
// 项目管理 - 合同管理 - 收款计划列表
export function getCollectionPlanList(data) {
  return request.post({
    url: `project/collection/plan/list`,
    data,
  });
}
// 项目管理 - 合同管理 - 收款计划删除
export function getCollectionPlanDelete(ids) {
  return request.delete({
    url: `project/collection/plan/${ids}`,
  });
}

//项目管理-项目费用导出
export function exportProjectCost(query) {
  return request.get({
    url: "/hours/cost/exportCost",
    params: query,
  });
}

//项目管理-部门费用导出
export function exportDeptCost(query) {
  return request.get({
    url: "/hours/cost/exportDeptCost",
    params: query,
  });
}
