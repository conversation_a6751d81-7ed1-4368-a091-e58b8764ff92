import request from '@/config/axios'

export interface AgentVO {
  id?: number
  name: string
  url: string
}


// 查询智能体列表/v1/kownledge/klAgentInfo
export const getAgentPage = async (params: PageParam) => {
  return await request.get({ url: '/v1/kownledge/klAgentInfo', params })
}

// 查询智能体详情/v1/kownledge/klAgentInfo/{id}
export const getAgent = async (id: number) => {
  return await request.get({ url: `/v1/kownledge/klAgentInfo/${id}` })
}

// 新增智能体v1/kownledge/klAgentInfo
export const createAgent = async (data: AgentVO) => {
  return await request.post({ url: '/v1/kownledge/klAgentInfo', data: data })
}

// 修改智能体/v1/kownledge/klAgentInfo
export const updateAgent = async (params: AgentVO) => {
  return await request.put({ url: '/v1/kownledge/klAgentInfo', data: params })
}

// 删除智能体/v1/kownledge/klAgentInfo/{id}
export const deleteAgent = async (id: number) => {
  return request.delete({ url: `/v1/kownledge/klAgentInfo/${id}` })
}
