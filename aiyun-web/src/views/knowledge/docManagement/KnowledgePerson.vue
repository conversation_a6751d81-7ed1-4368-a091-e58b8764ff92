<template>
	<base-knowledge-setting
		:isPermission="false"
		@click-knowledge="handleClickKnowledge"
		:getData="getData"
	/>
</template>

<script setup>
import { getKlDatasetPermission } from '@/api/knowledge/docmangement/index.js';
import BaseKnowledgeSetting from './components/BaseKnowledgeSetting.vue';

const router = useRouter() // 路由
const handleClickKnowledge = (item) => {
	router.push({
		path: '/knowledge/perosnldoc',
		query: {
			datasetId: item.datasetId,
			id: item.id,
			name: item.name
		}
	})
};

const getData = async () => {
	const res = await getKlDatasetPermission({});
	return res;
};

</script>

<style scoped lang="scss">
</style>