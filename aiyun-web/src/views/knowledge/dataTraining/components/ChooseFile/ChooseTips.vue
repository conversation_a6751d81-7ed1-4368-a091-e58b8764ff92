<template>
  <Dialog v-model="dialogVisible" :width="700" :scroll="true" title="选择提示词">
    <div class="choose-file-container">
			<el-collapse v-model="activeName" @change="handleChange" style="width: 600px" accordion>
				<el-collapse-item  v-for="(item, index) in list" :key="index" :name="index" :title="item.title">
					<template #icon="{ isActive }">
						<div class="choose-file-container__btn">
							<Icon :icon="isActive ? 'ep:arrow-down' : 'ep:arrow-up'" />
							<el-button type="primary" size="small" @click.stop @click="handleChoose(item.content)">选择</el-button>
						</div>
					</template>
					{{ item.content }}
				</el-collapse-item>
			</el-collapse>

    </div>
		<!-- <template #footer>
      <el-button  type="primary" @click="submitForm">确认</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template> -->
	</Dialog>
</template>

<script setup>

	const dialogVisible = ref(false)
	const activeName = ref('1')
	defineOptions({ name: 'ChooseTips' })
	const emit = defineEmits(['success'])
	const list = ref([
		{
			title: '电网项目分析报告大纲生成',
			content: '你是一位能源行业分析专家，擅长撰写电网项目研究报告。请为[某智能电网建设项目]创建结构化大纲，要求：\n1. 引言：说明项目背景（如双碳目标下的电网升级需求）、研究目的及行业价值\n2. 主体：\n• 技术应用：分析5G电力切片、数字孪生等核心技术实施方案\n• 经济效益：通过负荷预测模型测算投资回报率（需包含典型省份案例）\n• 社会效益：从清洁能源消纳、民生供电可靠性等维度展开\n3. 结论：提出项目推广建议及风险控制措施\n4. 标题建议：体现技术创新与战略价值的组合式标题，如《"数字赋能+绿色转型"：某省智能电网示范项目全景透视》'
		},
		{
			title: '电力技术文档大纲生成',
			content: '作为电力工程师文档助手，请为[柔性直流输电技术]编写技术说明文档框架：\n1. 引言：阐述技术发展背景（特高压电网建设需求）、应用场景及技术优势\n2. 技术解析：\n• 核心原理：换流器拓扑结构、控制策略等关键技术图解\n• 对比分析：与传统交流输电在损耗率、稳定性等方面的数据对比\n• 典型应用：张北柔直工程等示范项目运营数据\n3. 结论：展望在新型电力系统中的战略地位\n4. 标题建议：采用"技术特性+应用价值"结构，例如《柔性直流输电：破解新能源跨区消纳的"金钥匙"》'
		},
		{
			title: '电力安全宣传文案大纲生成',
			content: '你是有10年经验的电力安全传播专家，请为[迎峰度夏保供电]主题设计宣传\n1. 引言：用近期极端天气事件切入，强调电力安全保障的重要性\n2. 主体内容：\n• 备战措施：负荷预测、设备特巡、应急演练等具体部署\n• 科技赋能：智能巡检机器人、无人机巡查等新技术应用案例\n• 用户互动：错峰用电倡议及安全用电知识科普\n3. 结语：通过数据对比展现保障成果，发起全民护电倡议\n4. 标题建议：使用数字强化说服力，如《48小时特巡+3000台智能设备：看国网如何筑牢迎峰度夏"防护网"》'
		},
		{
			title: '新能源接入方案大纲生成',
			content: '作为电网规划专家，请为[分布式光伏大规模接入]课题构建分析框架：\n1. 引言：分析新能源发展政策与电网承载能力矛盾现状\n2. 核心议题：\n• 技术挑战：谐波治理、电压波动等关键技术问题及解决方案\n• 商业模式：隔墙售电、虚拟电厂等新型运营模式解析\n• 典型案例：浙江海宁"源网荷储一体化"示范区建设经验\n3. 结论：提出电网升级路径及标准体系建设建议\n4. 标题建议：采用问题导向式，如《破解"甜蜜负担"：分布式光伏高渗透率接入的系统性解决方案》'
		},
		{
			title: '电力服务白皮书大纲生成',
			content: '你是有政府工作报告撰写经验的智库专家，请为国网[优化电力营商环境]主题设计白皮书架构：\n1. 开篇：用世界银行"获得电力"指标排名提升切入主题\n2. 主体模块：\n• 改革举措："三零三省"服务具体实施路径及成效数据\n• 数字转型：办电e助手、电子签章等数字化工具应用场景\n• 用户见证：选取小微企业、重点项目的典型服务案例\n3. 展望：提出"电力获得感"评价体系构建计划\n4. 标题建议：突出成果与愿景，如《从"用上电"到"用好电"：国网打造国际一流电力营商环境的创新实践》'
		}
	])
	const open = async () => {
		dialogVisible.value = true
	}

	defineExpose({ open }) // 提供 open 方法，用于打开弹窗

	const submitForm = async () => {
		emit('success',)
		dialogVisible.value = false
	}

	const handleChange = (val) => {
		console.log(val)
	}

	const handleChoose = (content) => {
		emit('success', content)
		dialogVisible.value = false
	}

</script>

<style lang="scss" scoped>
.choose-file-container__btn {
	margin: 0 8px 0 auto;
	display: flex;
	align-items: center;
	width: 80px;
	justify-content: space-between;
}
</style>
