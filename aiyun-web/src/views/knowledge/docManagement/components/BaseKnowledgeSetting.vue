	<template>
		<div class="wrap" v-loading="loading">
			<div class="header">
				<el-button 
				type="primary" 
				@click="handleCreate"
				>
					新建知识库 <Icon icon="ep:plus" />
				</el-button>
			</div>
			<div class="content">
				<el-row :gutter="12">
					<el-col :span="6" v-for="(item, index) in knowledgeList" :key="index">
						<div class="knowledge-list" @click.stop="handleClickKnowledge(item)">
							<div class="list-header">
								<span>
									<Icon :icon="item.isSuperAdmin ? 'fa:user' : 'fa:user-o' " :size="24"/>
									{{ item.creator }}
								</span>
								<!-- isPermission 公共 -->
								<el-dropdown @command="handleCommand" v-if="isPermission || !item.isSuperAdmin">
										<Icon icon="ep:more-filled" :size="24" class="mr-4px"/>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item :icon="Tools" v-if="(isPermission && item.isSuperAdmin) || (!isPermission && !item.isSuperAdmin)" :command="['edit', item]">编辑</el-dropdown-item>
											<el-dropdown-item :icon="Delete" :command="['delete', item]">删除</el-dropdown-item>
											<el-dropdown-item v-if="isPermission && item.isSuperAdmin" :icon="Key" :command="['permission', item]">分配权限</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</div>
								<div class="title ">
									<el-tooltip
										:content="item.name"
										placement="top-start"
									>
										<p class="ellipsis">
											{{ item.name }}
										</p>
									</el-tooltip>
								</div>
							<div class="footer">
								<!-- <p class="mb-2px"><Icon icon="ep:document" class="mr-4px"/>文档数量: {{ item.fileNumber }}</p> -->
								<p><Icon icon="ep:clock" class="mr-4px"/>{{ item.createTime }}</p>
							</div>
						</div>
					</el-col>
				</el-row>
			</div>
		</div>
		<KnowledgeUpdate ref="KnowledgeUpdateRef" @success="handleSearch" />
		<AssignKnowledgeDialog ref="AssignKnowledgeDialogRef" />
	</template>
	
	<script setup>
	import { ref, onMounted, computed } from 'vue';
	import {
		Tools,
		Delete,
		Key
	} from '@element-plus/icons-vue'

	import { getKlDataset, deleteKlDataset } from '@/api/knowledge/docmangement'
	import { useUserStore } from '@/store/modules/user'
	import KnowledgeUpdate from './KnowledgeUpdate.vue';
	import AssignKnowledgeDialog from './AssignKnowledgeDialog.vue';

	defineOptions({ name: 'BaseKnowledgeSetting' })

	const props = defineProps({
		isPermission: { type: Boolean, default: false },
		getData: { type: Function, default: () => {} }
	})

	const emit = defineEmits(['clickKnowledge'])

	
	const message = useMessage() // 消息弹窗
	const router = useRouter() // 路由
	const isPermission = computed(() => {
		return props.isPermission
	})
	const userStore = useUserStore()
	const apikeys = userStore.getAppConfig
	const knowledgeList = ref([])

	const KnowledgeUpdateRef = ref()
	const AssignKnowledgeDialogRef = ref()
	const loading = ref(false)
	const handleCreate = () => {
		KnowledgeUpdateRef.value.open('create', {}, isPermission.value)
	}

	const handleDelete = async(item) => {
		try {
			await message.delConfirm('是否删除当前知识库？')
			loading.value = true
			await deleteKlDataset(item.id)
			loading.value = false
			message.success('删除成功')
			// 刷新列表
			await handleSearch()
		} catch {}
	}

	const handleSearch = async() => {
		loading.value = true
		const response = await props.getData()
		console.log('response', response);
		loading.value = false
		knowledgeList.value = response || []
	}

	const handlePermission = (item) => {
		console.log(item);
		AssignKnowledgeDialogRef.value.open(item)
	}
	const handleCommand = (command) => {
		switch (command[0]) {
			case 'edit':
				KnowledgeUpdateRef.value.open('edit', command[1], isPermission.value)
				break;
			case 'delete':
				handleDelete(command[1])
				break;
			case 'permission':
				handlePermission(command[1])
				break;
			default:
				break;
		}
	}

	const handleClickKnowledge = (item) => {
		// 跳转知识库页面
		emit('clickKnowledge', item)
		// router.push({
		// 	path: '/knowledge/management',
		// 	query: {
		// 		datasetId: item.datasetId,
		// 		id: item.id,
		// 		name: item.name
		// 	}
		// })
	}

	onMounted(() => {
		handleSearch()
	})
	
	</script>
	
	<style scoped lang="scss">
	.ellipsis {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		width: 100%;
		display: inline-block;
	}
	.wrap {
		position: relative;
		padding-top: 48px;
		.header {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			z-index: 1;
			padding: 0 20px;
			height: 48px;
			line-height: 48px;
			background: #fff;
			text-align: right;
			// border-bottom: 1px solid #ebeef5;
			// box-shadow: 0 1px 3px 0 rgb(0 0 0 / 5%);
		}
		.content {
			padding-top: 10px;
			.knowledge-list {
				height: 251px;
				display: flex;
				margin-bottom: 10px;
				flex-direction: column;
				justify-content: space-between;
				padding: 20px;
				border: 1px solid #ebeef5;
				border-radius: 12px;
				background-color: #fff;
				box-shadow: 0 1px 3px 0 rgb(0 0 0 / 5%);
				cursor: pointer;
				.list-header {
					display: flex;
					justify-content: space-between;
				}
				.title {
					color: #000000e0;
					margin: 10px 0;
					font-size: 24px;
					line-height: 32px;
					font-weight: 600;
					word-break: break-all
				}
				.footer {
					p {
						display: flex;
						align-items: center;
						font-weight: 500;
						font-size: 12px;
						line-height: 22px;
					}
				}
			}
		}
	}

	</style>