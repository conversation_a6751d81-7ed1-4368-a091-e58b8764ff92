<template>
    <Dialog 
      v-model="dialogVisible" 
      :scroll="true"
      width="700px"
      destroy-on-close
      title="选择路径">
      <div class="kl-content" v-if="isShowKL">
        <el-row :gutter="12">
					<el-col :span="8" v-for="(item, index) in knowledgeList" :key="index">
						<div class="knowledge-list" @click.stop="handleClickKnowledge(item)">
							<div class="list-header">
								<span>
									<Icon :icon="item.isSuperAdmin ? 'fa:user' : 'fa:user-o' " :size="24"/>
                  {{ item.creator }}
								</span>
                <span></span>
							</div>
							<div class="title ">
                <el-tooltip
                  :content="item.name"
                  placement="top-start"
                >
                  <p class="ellipsis">
                    {{ item.name }}
                  </p>
                </el-tooltip>
              </div>
							<div class="footer">
								<p><Icon icon="ep:clock" class="mr-4px"/>{{ item.createTime }}</p>
							</div>
						</div>
					</el-col>
				</el-row>
      </div>
      <div class="content" v-else>
        <div class="breadcrumb-wrapper">
          <!-- 返回上一级按钮 -->
          <el-button
            v-if="path.length > 2"
            @click="handleGoBack"
            type="primary"
            link
            class="back-button"
          >
            返回上一级
          </el-button>
          <el-divider direction="vertical" />
          <!-- 路径导航 -->
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="(item, index) in path"
              :key="index"
              @click="handleBreadcrumbClick(index)"
            >
              <span style="cursor: pointer;">{{ item.fileName }}</span>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
  
        <!-- 文件列表 -->
        <div>
          <div class="file-item flex align-center" @click="handleNameClick(item)" v-for="(item, index) in fileList" :key="index">
              <span>
                <el-icon class="mr-[4px]">
                  <Document v-if="item.filePrefix" />
                  <FolderOpened v-else />
                </el-icon>
              </span>
              <span>{{ item.fileName }}</span>
          </div>
          <Pagination
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="handleSearch"
          />
        </div>
      </div>
      <template #footer>
        <el-button type="primary" :disabled="isShowKL" :loading="submitLoading" @click="submit">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </Dialog>
  </template>
  <script setup>
  import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
  import * as FolderAPI from '@/api/knowledge/docmangement'
  import { getKlDataset, } from '@/api/knowledge/docmangement'
  import { pushKlCloud } from '@/api/knowledge/createTem'
  
  import { FolderOpened,  Document, } from '@element-plus/icons-vue'
  
  import { v4 as uuidv4 } from 'uuid'
  
  defineOptions({ name: 'FileWayDialog' })
  
  // const emit = defineEmits(['success'])
  
  const { t } = useI18n() // 国际化
  const message = useMessage() // 消息弹窗
  const knowledgeList = ref([])
  const isShowKL = ref(true)
  
  const dialogVisible = ref(false) // 弹窗的是否展示
  const innerVisible = ref(false)
  const formLoading = ref(false)
  const formRef = ref()
  const formData = ref({
    name: '',
  })
  const dialogTitle = ref('') // 弹窗的标题
  const dialogType = ref('')
  // 当前路径
  const path = ref([{ id: '', fileName: '知识库', level: '1' }]);
  // 当前文件夹下的文件列表
  const fileList = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    fileName: '',
    parentId: '',
    level: '',
  })
  const total = ref(0)
  const submitLoading = ref(false)
  
  // 加载文件夹内容
  const loadFolderContent = async (row) => {
    // fileList.value = fakeData[folderId] || [];
    const { id, level, datasetId } = row
    const response = await FolderAPI.getCloudPage({
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      fileName: queryParams.value.fileName,
      parentId: id,
      datasetId,
      level: id ? parseInt(level) + 1 : level,
    });
    total.value = response.total;
    fileList.value = response.records;
  };
  
  const handleSearch = () => {
    loadFolderContent(path.value[path.value.length - 1]);
  }

  const obj = {
    docx: 'createDoc',
    pptx: 'createPpt',
    xlsx: 'createXlxs',
    upload: 'upload'
  }
  // 判断权限
  const handleJudgeRole = async(item) => {
    const response = await FolderAPI.getUserKlDatasetPermission({
      datasetId: item.datasetId
    })
    if(!item.isSuperAdmin) {
      return true
    }
    const permission = response[0]?.permission ? response[0].permission : []
    const roleList = permission && permission.map((item) => item.buttonCode)
    if(!roleList.includes(obj[dialogType.value])) {
      message.error('您没有权限操作当前知识库！')
      return false
    }
    return true
  }

  const handleClickKnowledge = async(item) => {
    console.log(item);
    // 权限判断
    // const isHaveRole = await handleJudgeRole(item)
    // if(!isHaveRole) return
    isShowKL.value = false
    path.value.push({
      id: '',
      datasetId:item.datasetId,
      fileName: item.name,
      level: '1'
    })
    setTimeout(() => {
      handleSearch()
    }, 100);
  }
  
  // 点击名称文本进入下一级
  const handleNameClick = (row) => {
    if (!row.filePrefix) {
      path.value.push(row);
      loadFolderContent(row);
    }
  };
  
  // 返回上一级
  const handleGoBack = () => {
    if (path.value.length > 1) {
      path.value.pop(); // 移除最后一项
      loadFolderContent(path.value[path.value.length - 1]); // 加载上一级文件夹内容
    }
  };
  
  // 点击路径导航返回上一级
  const handleBreadcrumbClick = (index) => {
    total.value = 0
    if(index === 0) {
      isShowKL.value = true
      fileList.value = []
      path.value = [{ id: '', fileName: '知识库', level: '1' }]
    } else {
      path.value = path.value.slice(0, index + 1);
      loadFolderContent(path.value[index]);
    }
  };
  
  const fileId = ref('')
  const pushFile = ref({})
  /** 打开弹窗 */
  const open = async (row) => {
    // fileId.value = row.id
    handleSearchKlList()
    path.value = [{ id: '', fileName: '知识库', level: '1' }]
    isShowKL.value = true
    // dialogType.value = type
    pushFile.value = row
    submitLoading.value = false
    dialogVisible.value = true
  }
  defineExpose({ open }) // 提供 open 方法，用于打开弹窗

  const handleSearchKlList = async() => {
		const response = await FolderAPI.getKlDatasetPermission()
		knowledgeList.value = response || []
	}
  
  const submit = async() => {
    const way = path.value[path.value.length - 1]
    if(way.id) {
      const params = {
        ...pushFile.value,
        klCloudId:  way.id,
      }
      submitLoading.value = true
      const res = await pushKlCloud(params)
        message.success('推送成功')
        submitLoading.value = false
        dialogVisible.value = false
      // dialogVisible.value = false
      // emit('success', way, dialogType.value)
    } else {
      message.warning('根目录禁止推送文件！！！')
    }
  }
  
  </script>
  
  <style lang="scss" scoped>
  .ellipsis {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: inline-block;
    max-width: 170px;
	}
    .breadcrumb-wrapper {
      display: flex;
      height: 32px;
      align-items: center;
      padding: 0 20px 0 0;
      .back-button {
        padding: 0;
        font-size: 14px;
      }
    }
    .file-item {
      line-height: 32px;
      padding: 8px;
      border-bottom: 1px solid #ccc;
      cursor: pointer;
    }
    .kl-content {
			padding-top: 10px;
      padding: 10px 10px 0;
			.knowledge-list {
				height: 251px;
				display: flex;
				margin-bottom: 10px;
				flex-direction: column;
				justify-content: space-between;
				padding: 20px;
				border: 1px solid #ebeef5;
				border-radius: 12px;
				background-color: #fff;
				box-shadow: 0 1px 3px 0 rgb(0 0 0 / 5%);
				cursor: pointer;
				.list-header {
					display: flex;
					justify-content: space-between;
				}
				.title {
					color: #000000e0;
					margin: 10px 0;
					font-size: 24px;
					line-height: 32px;
					font-weight: 600;
					word-break: break-all
				}
				.footer {
					p {
						display: flex;
						align-items: center;
						font-weight: 500;
						font-size: 12px;
						line-height: 22px;
					}
				}
			}
		}
  </style>
  