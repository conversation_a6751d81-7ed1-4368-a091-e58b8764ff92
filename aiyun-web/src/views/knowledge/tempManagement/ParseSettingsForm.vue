<template>
  <Dialog 
    v-model="dialogVisible" 
    :scroll="true"
    :title="dialogTitle">
    <el-form
      ref="formRef"
      class="form-container"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      label-position="top"
    >
      <el-form-item label="解析方式" prop="analysisMethod">
        <el-select v-model="formData.analysisMethod" placeholder="请选择解析方式">
          <el-option
            v-for="item in getIntDictOptions(DICT_TYPE.analysis_method)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
         />
        </el-select>
      </el-form-item>
      <el-form-item label="块Token数" prop="tokenNum">
        <el-slider v-model="formData.tokenNum" :min="0" :max="1000" show-input />
      </el-form-item>
       <el-form-item label="分段标识符" prop="subsection">
        <el-input v-model="formData.subsection" placeholder="请输入分段标识符" />
      </el-form-item>
      <el-form-item label="自动关键词" prop="automaticCrux">
        <el-slider v-model="formData.automaticCrux" :min="0" :max="1000" show-input />
      </el-form-item>
      <el-form-item label="自动问题" prop="automaticProblem">
        <el-slider v-model="formData.automaticProblem" :min="0" :max="1000" show-input />
      </el-form-item>
       <el-form-item label="使用召回增强RAPTOR策略" prop="isRaptor">
        <el-switch v-model="formData.isRaptor" active-color="#13ce66" inactive-color="#ff4949" active-text="开启" inactive-text="关闭" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TemplateAPI from '@/api/knowledge/tempmangement'


defineOptions({ name: 'SystemUserForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

const formData = ref({
  analysisMethod: '',
  tokenNum: 0,
  subsection: '',
  automaticCrux: 0,
  automaticProblem: 0,
  isRaptor: null,
})
const formRules = reactive({
  analysisMethod: [{ required: true, message: '请选择解析方式', trigger: 'change' }],
  tokenNum: [{ required: true, message: '请输入块Token数', trigger: 'blur' }],
  subsection: [{ required: true, message: '请输入分段标识符', trigger: 'change' }],
  automaticCrux: [{ required: true, message: '请输入自动关键词', trigger: 'change' }],
  automaticProblem: [{ required: true, message: '请输入自动问题', trigger: 'blur' }],
  isRaptor: [{ required: true, message: '请选择是否使用召回增强RAPTOR策略', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref
const rowId = ref()

const klCloudAnalysisId = ref()
/** 打开弹窗 */
const open = async (row) => {
  dialogVisible.value = true
  resetForm()
  rowId.value = row.id
  klCloudAnalysisId.value = row.klCloudAnalysisId
  if(klCloudAnalysisId.value) {
    handleGetCloudAnalysis(klCloudAnalysisId.value)
  }
  dialogTitle.value = '解析设置'
}

// 获取解析设置
const handleGetCloudAnalysis = async (id) => {
  formLoading.value = true
  const response = await TemplateAPI.getCloudAnalysis(id)
  formData.value = { 
    ...response, 
    isRaptor: response.isRaptor === 'true' ? true : false,
    analysisMethod: Number(response.analysisMethod)
  }
  formLoading.value = false
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  console.log(formData.value);
  try {
    const data = {
      ...formData.value,
      klCloudId: rowId.value,
    }
    // 修改
    if (klCloudAnalysisId.value) {
      await TemplateAPI.updateCloudAnalysis(data)
      message.success(t('common.updateSuccess'))
    } else {
      // 新增
      await TemplateAPI.createCloudAnalysis(data)
      message.success(t('common.createSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const resetForm = () => {
  formData.value = {
    analysisMethod: '',
    tokenNum: 0,
    subsection: '',
    automaticCrux: 0,
    automaticProblem: 0,
    isRaptor: null,
  }
  formRef.value?.resetFields()
}



</script>

<style lang="scss" scoped>
  .form-container {
    padding: 0 20px;
  }

</style>
