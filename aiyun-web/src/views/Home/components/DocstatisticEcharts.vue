<template>
  <el-card class="chart-section" shadow="never">
    <template #header>
      <span class="section-title">近7日文档统计</span>
    </template>
    <div ref="docs" style="width: 100%; height: 329px"></div>
  </el-card>
</template>

<script setup>
import * as echarts from 'echarts'
import * as homeAPI from '@/api/home/<USER>'

const docs = ref(null)
onMounted(async() => {
  const response = await homeAPI.getHomeHebdomadCount()
  console.log(response);
  const { data, sdata, series  } = response
  
  // 图表初始化
  const chart = echarts.init(docs.value)

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: data,
      textStyle: {
        color: '#666'
      }
    },
    grid: {
      left: '3%',
      right: '5%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: sdata,
      axisLabel: {
        color: '#999'
      }
    },
    yAxis: {
      type: 'value',
      min: 1,
      minInterval: 1,
      axisLabel: {
        color: '#999'
      }
    },
    series: [
      {
        name: '点赞数',
        type: 'line',
        smooth: true,
        data: series[0].data,
        itemStyle: {
          color: '#5470C6'
        }
      },
      {
        name: '收藏数',
        type: 'line',
        smooth: true,
        data: series[1].data,
        itemStyle: {
          color: '#91CC75'
        }
      },
      {
        name: '推荐数',
        type: 'line',
        smooth: true,
        data: series[2].data,
        itemStyle: {
          color: '#d5662a'
        }
      },
      {
        name: '下载数',
        type: 'line',
        smooth: true,
        data: series[3].data,
        itemStyle: {
          color: '#FAC858'
        }
      }
    ]
  }
  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => chart.resize())
})
</script>

<style lang="less" scoped></style>
