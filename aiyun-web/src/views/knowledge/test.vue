<template>
  <div>
    <!-- 路径导航 -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item v-for="(item, index) in path" :key="index" @click="handleBreadcrumbClick(index)">
        {{ item.name }}
      </el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 文件列表 -->
    <el-table :data="fileList" @row-click="handleRowClick">
      <el-table-column prop="name" label="文件名" />
      <el-table-column prop="type" label="类型" />
      <el-table-column prop="size" label="大小" />
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      path: [{ id: 'root', name: '根目录' }], // 当前路径
      fileList: [], // 当前文件夹下的文件列表
      fakeData: {
        root: [
          { id: '1', name: '文件夹1', type: 'folder', size: 0 },
          { id: '2', name: '文件1.txt', type: 'file', size: 1024 },
        ],
        '1': [
          { id: '3', name: '子文件夹1', type: 'folder', size: 0 },
          { id: '4', name: '文件2.txt', type: 'file', size: 2048 },
        ],
        '3': [
          { id: '5', name: '文件3.txt', type: 'file', size: 512 },
        ],
      },
    };
  },
  methods: {
    // 加载文件夹内容
    loadFolderContent(folderId) {
      this.fileList = this.fakeData[folderId] || [];
    },
    // 点击文件夹进入下一级
    handleRowClick(row) {
      if (row.type === 'folder') {
        this.path.push({ id: row.id, name: row.name });
        this.loadFolderContent(row.id);
      }
    },
    // 点击路径导航返回上一级
    handleBreadcrumbClick(index) {
      this.path = this.path.slice(0, index + 1);
      this.loadFolderContent(this.path[index].id);
    },
  },
  mounted() {
    this.loadFolderContent('root'); // 初始化加载根目录
  },
};
</script>