<script setup lang="ts">
import { Handle, Position, useVueFlow } from "@vue-flow/core";
import { NodeToolbar } from '@vue-flow/node-toolbar'
import { ref, markRaw } from 'vue';
import { ArrowUp, ArrowDown, Delete } from '@element-plus/icons-vue';

const { updateNodeData, removeNodes  } = useVueFlow();

const isExpanded = ref(true);
defineOptions ({ name: 'HttpNode' })

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const props = defineProps<{
  id: string;
  data: {
    label: string;
    url?: string;
    method?: string;
    headers?: string;
    toolbarVisible?: boolean;
    inputParams?: Array<{ name: string; type: string }>;
    outputParams?: Array<{ name: string; type: string }>;
    bodyType?: string;
    description?: string;
  }
}>();

const updateData = (key: string, value: any) => {
  updateNodeData(props.id, {
    ...props.data,
    [key]: value
  });
};

const addInputParam = () => {
  const inputParams = [...(props.data.inputParams || [])];
  inputParams.push({ name: '', type: 'string' });
  updateData('inputParams', inputParams);
};

const addOutputParam = () => {
  const outputParams = [...(props.data.outputParams || [])];
  outputParams.push({ name: '', type: 'string' });
  updateData('outputParams', outputParams);
};

const updateParam = (index: number, key: string, value: string, isInput: boolean) => {
  const params = isInput ? [...(props.data.inputParams || [])] : [...(props.data.outputParams || [])];
  params[index] = { ...params[index], [key]: value };
  updateData(isInput ? 'inputParams' : 'outputParams', params);
};

const removeParam = (index: number, isInput: boolean) => {
  const params = isInput ? [...(props.data.inputParams || [])] : [...(props.data.outputParams || [])];
  params.splice(index, 1);
  updateData(isInput ? 'inputParams' : 'outputParams', params);
};

const Icons = {
  ArrowUp: markRaw(ArrowUp),
  ArrowDown: markRaw(ArrowDown),
  Delete: markRaw(Delete)
};

const emit = defineEmits(['updateNodeInternals']);
</script>

<template>
  <div class="node-wrapper">
    <NodeToolbar  
      :is-visible="data.toolbarVisible" 
      style="background-color: #fff;"
      :position="Position.Top"
      align="end"
      :offset="4"
    >
      <el-button
        @click.stop="removeNodes([props.id])"
      >
        <el-icon><component :is="Icons.Delete" /></el-icon>
      </el-button>
    </NodeToolbar>
    <div class="http-node">
      <div class="node-header">
        <span class="node-type">{{ data.label }}</span>
        <el-button
          type="primary"
          link
          @click="toggleExpand"
        >
          <el-icon>
            <component :is="isExpanded ? Icons.ArrowUp : Icons.ArrowDown" />
          </el-icon>
        </el-button>
      </div>
      <div v-show="isExpanded" class="node-content">
        <div class="form-item">
          <label>URL</label>
          <div class="form-control">
            <ElSelect
              :model-value="data.method"
              @update:model-value="(val) => updateData('method', val)"
              style="width: 120px;"
              placeholder="选择请求方法"
            >
              <ElOption label="GET" value="GET" />
              <ElOption label="POST" value="POST" />
              <ElOption label="PUT" value="PUT" />
              <ElOption label="DELETE" value="DELETE" />
            </ElSelect>
            <ElInput
              :model-value="data.url"
              @update:model-value="(val) => updateData('url', val)"
              placeholder="输入URL"
            />
          </div>
        </div>
        <div class="form-item">
          
        </div>
        
        <div class="params-section">
          <div class="params-header">
            <span>http头信息</span>
            <el-button type="primary" size="small" @click="addOutputParam">添加</el-button>
          </div>
          <div v-for="(param, index) in data.outputParams" :key="index" class="param-item">
            <ElInput
              :model-value="param.name"
              @update:model-value="(val) => updateParam(index, 'name', val, false)"
              placeholder="参数名"
            />
            <ElSelect
              :model-value="param.type"
              @update:model-value="(val) => updateParam(index, 'type', val, false)"
              placeholder="类型"
            >
              <ElOption label="字符串" value="string" />
              <ElOption label="数字" value="number" />
              <ElOption label="布尔值" value="boolean" />
            </ElSelect>
            <el-button type="danger" size="small" @click="() => removeParam(index, false)">删除</el-button>
          </div>
        </div>
        <div class="params-section">
          <div class="params-header">
            <span>输入参数</span>
            <el-button type="primary" size="small" @click="addInputParam">添加</el-button>
          </div>
          <div v-for="(param, index) in data.inputParams" :key="index" class="param-item">
            <ElInput
              :model-value="param.name"
              @update:model-value="(val) => updateParam(index, 'name', val, true)"
              placeholder="参数名"
            />
            <ElSelect
              :model-value="param.type"
              @update:model-value="(val) => updateParam(index, 'type', val, true)"
              placeholder="类型"
            >
              <ElOption label="字符串" value="string" />
              <ElOption label="数字" value="number" />
              <ElOption label="布尔值" value="boolean" />
            </ElSelect>
            <el-button type="danger" size="small" @click="() => removeParam(index, true)">删除</el-button>
          </div>
        </div>

        <div class="form-item">
          <label>Body</label>
          <el-radio-group :model-value="data.bodyType" @update:model-value="(val) => updateData('bodyType', val)">
            <el-radio :value="'none'">none</el-radio>
            <el-radio :value="'form-data'">form-data</el-radio>
            <el-radio :value="'x-www-form-urlencoded'">x-www-form-urlencoded</el-radio>
            <el-radio :value="'json'">json</el-radio>
            <el-radio :value="'raw'">raw</el-radio>
          </el-radio-group>
        </div>

        <div class="form-item">
          <label>返回格式</label>
          <el-input
            :model-value="data.description"
            @update:model-value="(val) => updateData('description', val)"
            placeholder="请输入返回格式"
            type="textarea"
            show-word-limit
            maxlength="200"
            :rows="4"
          />
        </div>
      </div>
      <Handle
        type="source"
        :position="Position.Bottom"
        :id="`${props.id}-bottom`"
        class="horizontal-handle"
      />

      <Handle
        type="source"
        :position="Position.Left"
        :id="`${props.id}-left`"
        class="vertical-handle"
      />
      <Handle
        type="source"
        :position="Position.Right"
        :id="`${props.id}-right`"
        class="vertical-handle"
      />
      <Handle
        type="target"
        :position="Position.Top"
        :id="`${props.id}-top`"
        class="horizontal-handle"
      />
    </div>
  </div>
</template>

<style scoped>
.http-node {
  width: 400px;
  background-color: white;
  border: 2px solid #f56c6c;
  border-radius: 8px;
  padding: 12px;
}

.node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.node-type {
  font-weight: bold;
  color: #f56c6c;
  text-align: left;
}

.node-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-item label {
  font-size: 14px;
  color: #606266;
}

.params-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.params-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.param-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.horizontal-handle {
  background-color: #f56c6c;
  width: 30px;
  height: 16px;
  border-radius: 5px;
}

.vertical-handle {
  background-color: #f56c6c;
  width: 16px;
  height: 30px;
  border-radius: 5px;
}

:deep(.el-button--text) {
  padding: 0;
  height: auto;
  color: #f56c6c;
}
.form-control {
  display: flex;
  gap: 8px;
  align-items: center;
}

</style> 