<template>
  
    <!-- 内容 -->
    <el-popover placement="left" width="100" trigger="click">
      <template #reference>
        <div class="tl-fixed">
          <p class="fixed-text">智能助手</p>
        </div>
      </template>
      <div class="pop-list">
        <el-row>
          <el-col v-for="(item ,index) in chatList" :key="index" :span="24">
            <div class="pop-item">
              <el-button @click="openDraw(item)" type="text"> 
                {{ item.name }}
              </el-button>
            </div>
          </el-col>
        </el-row>
        
      </div>
    </el-popover>
  <el-drawer
    v-model="drawer"
    :title="chatName"
    :direction="direction"
    :before-close="handleClose"
  >
      <el-container class="detail-container">
        <el-main class="main-container">
          <div>
            <div class="message-container">
              <!-- 情况四：消息列表不为空 -->
              <MessageList
                ref="messageRef"
                :conversation="activeConversation"
                :list="messageList"
              />
            </div>
          </div>
        </el-main>
        <!-- 底部 -->
        <el-footer class="footer-container">
          <form class="prompt-from">
            <textarea
              class="prompt-input"
              v-model="prompt"
              @keydown="handleSendByKeydown"
              placeholder="问我任何问题...（Shift+Enter 换行，按下 Enter 发送）"
            ></textarea>
            <div class="prompt-btns">
              <el-button
                type="primary"
                size="default"
                @click="conversationInProgress ?stopStream() :  handleSendByButton() "
                >
                <!-- :loading="conversationInProgress" -->
                {{ conversationInProgress ? '停止' : '发送' }}
              </el-button>
            </div>
          </form>
        </el-footer>
      </el-container>
  </el-drawer>
</template>
<script>

import { use } from 'echarts';
import MessageList from './message/MessageList.vue'
const message = useMessage() // 消息弹窗
import axios from 'axios';
// import { chatList } from './message/answer'
import * as CodegenApi from "@/api/infra/codegen";
import download from '@/utils/download'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { ChatListApi } from '@/api/ai/chatList'

export default {
  components: {
    MessageList,
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      chatType: '',
      chatList: [],
      api_key: '',
      chatName: '',
      agent_id: '',
      activeMessageListLoading: true,
      activeConversation: {},
      conversationInProgress: false,
      prompt: '',
      receiveMessageFullText: '',
      conversationInAbortController: null,
      messageList: [
        {
           type: 'ai',
           content: '你好，我是AI，请问有什么可以帮到您的？',
           createTime: new Date()
        },
      ],
    }
  },
  async created() {
      // const types = ["kefu", "caijin", "jiansuo", "fanyi", 'wenzhen', 'zhuli', 'yunwei', 'kaifa', 'zhuanyuan', 'ragflow.token']
      // const response = await ChatListApi.getChatList(types)
      // const tokenConfig = response.configDO
      // .find(item => item.configKey === 'ragflow.token')
      // const token = tokenConfig ? tokenConfig.value : ''
      this.chatList = [
        {
          name: '职称晋级评估报告'
        }
      ]
      // this.chatList = (response.configDO || [])
      //   .filter(item => item.configKey !== 'ragflow.token') 
      //   .map(item => ({
      //     api_key: token.replace('Bearer ', ''), 
      //     agent_id: item.value,
      //     type: item.category === 'chat' ? 'chat' : 'ai',
      //     name: item.name
      //   }))

        
  },
  methods: {
    handleClose(done) {
      this.stopStream()
      this.messageList = [
        {
           type: 'ai',
           content: '你好，我是AI，请问有什么可以帮到您的？',
           createTime: new Date()
        },
      ],
      this.prompt = ''
      done()
    },
    openDraw(chatItem) {
      this.drawer = true
      this.chatType = chatItem.type
      this.api_key = chatItem.api_key
      this.chatName = chatItem.name
      this.agent_id = chatItem.agent_id
      
    },
    /** 处理来自【发送】按钮的发送消息 */
    handleSendByButton() {
      this.doSendMessage(this.prompt.trim())
    },
    /** 处理来自 keydown 的发送消息 */
    async handleSendByKeydown(event) {
      
      // 进行中不允许发送
      if (this.conversationInProgress) {
        return
      }
      const content = this.prompt?.trim()
      if (event.key === 'Enter') {
        if (event.shiftKey) {
          // 插入换行
          this.prompt += '\r\n'
          event.preventDefault() // 防止默认的换行行为
        } else {
          // 发送消息
          await this.doSendMessage(content)
          event.preventDefault() // 防止默认的提交行为
        }
      }
    },
    removeMessageWithIdMinusOne() {
      this.messageList = this.messageList.filter(item => item.id !== -1);
    },
    /** 真正执行【发送】消息操作 */
    async doSendMessage(content) {
      // 校验
      if (content.length < 1) {
        message.error('发送失败，原因：内容为空！')
        return
      }
      const question = {
        type: 'user',
        content: content,
        createTime: new Date()
      }
      this.messageList.push(question)
      // 清空输入框
      this.prompt = ''
      // 执行发送
      await this.doSendMessageStream({
        content: content
      })
    },
    /** 真正执行【发送】消息操作 */
    async doSendMessageStream(userMessage) {
      // // 创建 AbortController 实例，以便中止请求
      // conversationInAbortController.value = new AbortController()
      this.conversationInAbortController = new AbortController()
      const thinking = {
        type: 'ai',
        content: '思考中...',
        createTime: new Date(),
        id: -1
      }
      this.messageList.push(thinking)
      // // 标记对话进行中
      this.conversationInProgress = true
      // 设置为空
      this.receiveMessageFullText = ''
      // ----------------------------
      try {
        let response = null
        let answerMsg = ''
        
        if (this.chatType === 'chat') {
          // const  auth = "ragflow-Y0YzM2MjQ1YWNhNzExZWZhNGFlMDI0Mm" 
          // const chat_id = "9a1d599faca611ef800b0242c0a83004"
          const documentContent = userMessage.content.split('技术规范书-')[1]
          const geneContent = userMessage.content.split('生成系统-')[1]
          if (documentContent) {
            // 技术规范书
            response = await CodegenApi.createJsgfs({
              projectName: documentContent
            });
            download.word07(response, userMessage.content)
            answerMsg = userMessage.content + '.docx 下载完成'
            this.messageList.pop()
            const tempMessage = {
              id: Date.now(), // 生成唯一ID
              content: answerMsg,
              createTime: new Date()
            };
            this.messageList.push(tempMessage);
            this.conversationInProgress = false
            await this.$nextTick(); // 触发Vue更新
            await this.scrollToBottom();
          } else if (geneContent) {
            // 生成系统
            response = await CodegenApi.createSystem();
            answerMsg = response
            this.messageList.pop()
            const tempMessage = {
              id: Date.now(), // 生成唯一ID
              content: answerMsg,
              createTime: new Date()
            };
            this.messageList.push(tempMessage);
            this.conversationInProgress = false
            await this.$nextTick(); // 触发Vue更新
            await this.scrollToBottom();
          } else {
            const Authorization = 'Bearer ' + this.api_key
            const url_one = 'http://rthl.88ic.cn:85/api/v1/chats/'+ this.agent_id+'/completions'
            response = await fetchEventSource(url_one, {
              method: 'post',
              headers: {
                Authorization: Authorization,
                Accept: '*/*',
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                "question":userMessage.content,
                "stream": true,
                'session_id': 'ea985dbac2ab11efa8300242c0a82002'
              }),
              signal: this.conversationInAbortController.signal,
              onmessage: async(msg) => {
                const { code, data, message } = JSON.parse(msg.data)
                if(data.id) {
                  this.messageList.pop()
                  const tempMessage = {
                    id: Date.now(), // 生成唯一ID
                    content: data.answer,
                    createTime: new Date()
                  };
                  this.messageList.push(tempMessage);
                  await this.$nextTick(); // 触发Vue更新
                  await this.scrollToBottom();
                }
              },
              onerror: (err) => {
                message.alert(`对话异常! ${err}`)
                this.stopStream()
              },
              onclose: () => {
                // 标记对话结束
                this.stopStream()
              },
            })
            // answerMsg = response.data.data.answer
          }
        } else {
          const _url = 'http://rthl.88ic.cn:85/api/v1/agents/'+this.agent_id+'/completions '
          response = await fetchEventSource(_url, {
            method: 'post',
            headers: {
              Authorization: 'Bearer ' + this.api_key,
              Accept: '*/*',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              "question":userMessage.content,
              "stream": true
            }),
            signal: this.conversationInAbortController.signal,
            onmessage: async(msg) => {
              const { code, data, message } = JSON.parse(msg.data)
              if(data.id) {
                this.messageList.pop()
                const tempMessage = {
                  id: Date.now(), // 生成唯一ID
                  content: data.answer,
                  createTime: new Date()
                };
                this.messageList.push(tempMessage);
                await this.$nextTick(); // 触发Vue更新
                await this.scrollToBottom();
              }
            },
            onerror: (err) => {
              message.alert(`对话异常! ${err}`)
              this.stopStream()
            },
            onclose: () => {
              // 标记对话结束
              this.stopStream()
            },
          })
          // answerMsg = response.data.data.answer
        }
      } catch (error) {
        message.alert(`对话异常! ${error}`)
        this.conversationInProgress = false
      }
      return
      // ----------------------------

      try {
        // 1.1 先添加两个假数据，等 stream 返回再替换
        activeMessageList.value.push({
          id: -1,
          type: 'user',
          content: userMessage.content,
          createTime: new Date()
        })
        activeMessageList.value.push({
          id: -2,
          type: 'assistant',
          content: '思考中...',
          createTime: new Date()
        })
        // 1.2 滚动到最下面
        await this.$nextTick()
        await this.scrollToBottom() // 底部
        // 1.3 开始滚动
        textRoll()

        // 2. 发送 event stream
        let isFirstChunk = true // 是否是第一个 chunk 消息段
        await ChatMessageApi.sendChatMessageStream(
          userMessage.conversationId,
          userMessage.content,
          conversationInAbortController.value,
          enableContext.value,
          async (res) => {
            const { code, data, msg } = JSON.parse(res.data)
            if (code !== 0) {
              message.alert(`对话异常! ${msg}`)
              return
            }

            // 如果内容为空，就不处理。
            if (data.receive.content === '') {
              return
            }
            // 首次返回需要添加一个 message 到页面，后面的都是更新
            if (isFirstChunk) {
              isFirstChunk = false
              // 弹出两个假数据
              activeMessageList.value.pop()
              activeMessageList.value.pop()
              // 更新返回的数据
              activeMessageList.value.push(data.send)
              activeMessageList.value.push(data.receive)
            }
            // debugger
            this.receiveMessageFullText = this.receiveMessageFullText + data.receive.content
            // 滚动到最下面
            await this.scrollToBottom()
          },
          (error) => {
            message.alert(`对话异常! ${error}`)
            stopStream()
          },
          () => {
            stopStream()
          }
        )
      } catch {}
    },
    // 停止stream 流式调用
    stopStream() {
      if (this.conversationInAbortController) {
        this.conversationInAbortController.abort()
      }
      this.conversationInProgress = false
    },
    /** 滚动到 message 底部 */
    async scrollToBottom(isIgnore) {
      await this.$nextTick()
      if (this.$refs.messageRef) {
        this.$refs.messageRef.scrollToBottom(isIgnore)
      }
    }

  }
}
  
</script>

<style lang="scss" scoped>
.qa-container {
  margin: 0 auto;
  height: 100%;
  min-height: 200px;
  border: 1px solid #ccc;
  border-radius: 8px;
}
.detail-container {
  height: 100%;
}
// main 容器
.main-container {
  margin: 0;
  padding: 0;
  position: relative;
  height: 100%;
  width: 100%;

  .message-container {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow-y: hidden;
    padding: 0;
    margin: 0;
  }
}

// 底部
.footer-container {
  display: flex;
  flex-direction: column;
  height: auto;
  margin: 0;
  padding: 0;

  .prompt-from {
    display: flex;
    flex-direction: column;
    height: auto;
    border: 1px solid #e3e3e3;
    border-radius: 10px;
    margin: 10px 20px 20px 20px;
    padding: 9px 10px;
  }

  .prompt-input {
    height: 80px;
    //box-shadow: none;
    border: none;
    box-sizing: border-box;
    resize: none;
    padding: 0 2px;
    overflow: auto;
  }

  .prompt-input:focus {
    outline: none;
  }

  .prompt-btns {
    display: flex;
    justify-content: space-between;
    padding-bottom: 0;
    padding-top: 5px;
  }

}
// 按钮
.tl-fixed {
    width: 50px;
    height: 130px;
    background-color: #5e60f3;
    position: fixed;
    z-index: 99;
    right: 30px;
    bottom: 10vh;
    cursor: pointer;
    border-radius: 25px;
    padding: 10px 0;
    .fixed-text {
      writing-mode: vertical-lr;
      font-size: 18px;
      font-weight: 700;
      color: #fff;
      line-height: 50px;
      display: inline-block;
      width: 100%;
      height: 100%;
      text-align: center;
      letter-spacing: 8px;
    }
}

</style>

<style lang="scss">
  .pop-list {
    width: 100%;
    .pop-item {
      text-align: center;
      > * {
        vertical-align: middle;
      }
    }
  }
  .el-popover {
    min-width: 50px;
  }
</style>
