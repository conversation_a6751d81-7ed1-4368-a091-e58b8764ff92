#!/bin/bash

# <PERSON><PERSON><PERSON> Plugin 本地开发启动脚本

echo "=== Yun<PERSON>hi Plugin 本地开发环境 ==="
echo ""

# 检查当前目录
if [ ! -f "config.json" ]; then
    echo "错误: 请在 yunzhi 插件目录下运行此脚本"
    exit 1
fi

echo "1. 检查插件文件..."
required_files=("config.json" "index.html" "scripts/main.js")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 缺失"
        exit 1
    fi
done

echo ""
echo "2. 启动本地开发服务器..."

# 检查是否安装了 Python
if command -v python3 &> /dev/null; then
    echo "使用 Python3 启动 HTTP 服务器..."
    echo "访问地址: http://localhost:8000"
    echo "测试页面: http://localhost:8000/test-local.html"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo ""
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    echo "使用 Python2 启动 HTTP 服务器..."
    echo "访问地址: http://localhost:8000"
    echo "测试页面: http://localhost:8000/test-local.html"
    echo ""
    echo "按 Ctrl+C 停止服务器"
    echo ""
    python -m SimpleHTTPServer 8000
elif command -v node &> /dev/null; then
    # 检查是否安装了 http-server
    if command -v http-server &> /dev/null; then
        echo "使用 Node.js http-server 启动服务器..."
        echo "访问地址: http://localhost:8000"
        echo "测试页面: http://localhost:8000/test-local.html"
        echo ""
        echo "按 Ctrl+C 停止服务器"
        echo ""
        http-server -p 8000
    else
        echo "Node.js 已安装，但缺少 http-server"
        echo "请运行: npm install -g http-server"
        echo "然后重新运行此脚本"
        exit 1
    fi
else
    echo "错误: 未找到 Python 或 Node.js"
    echo "请安装以下任一工具:"
    echo "- Python 3: https://www.python.org/"
    echo "- Node.js + http-server: npm install -g http-server"
    exit 1
fi
