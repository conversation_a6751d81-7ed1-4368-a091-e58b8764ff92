<template>
  <Dialog v-model="dialogVisible"  width="500" title="分配权限">
      <!-- 左侧角色列表 -->
      <el-card shadow="never" v-loading="searchLoading">
        <template #header>
          <div class="card-header">
            <span>分配角色</span>
          </div>
        </template>
        <el-scrollbar height="360px">
          <el-tree
            ref="treeRef"
            style="max-width: 460px"
            :data="roleTree"
            show-checkbox
            default-expand-all
            node-key="id"
            highlight-current
            :props="defaultProps"
          />
        </el-scrollbar>
      </el-card>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
    </template>
  </Dialog>
</template>

<script setup>
import { ref } from 'vue'
import { getRolePage } from '@/api/system/role'
import { useMessage } from '@/hooks/web/useMessage'
import { saveCloudButtonRole, getCloudButtonRole, deleteCloudButtonRole } from '@/api/knowledge/docmangement'
// const props = defineProps({ })

const emit = defineEmits(['success'])

const message = useMessage()
const dialogVisible = ref(false)
const roleList = ref([])
const selectedRoles = ref([])
const selectedOperations = ref([])
const currentRow = ref(null)
const loading = ref(false)

const operations = ref([])

const defaultProps = {
  children: 'children',
  label: 'name',
}

const searchLoading = ref(false)

const folderOperations = [
  { id: 'folderShow', name: '显示' , type: 'folder'},
  { id: 'renamefolder', name: '重命名' , type: 'folder'},
  { id: 'movefolder', name: '移动' , type: 'folder'},
  { id: 'deletefolder', name: '删除' , type: 'folder'},
]

const fileOperations = [
  // { id: 'fileShow', name: '显示', type: 'file' },
  { id: 'collect', name: '收藏', type: 'file' },
  { id: 'share', name: '分享', type: 'file' },
  { id: 'like', name: '喜欢', type: 'file' },
  { id: 'recommend', name: '推荐', type: 'file' },
  { id: 'view', name: '查看文档', type: 'file' },
  // { id: 'edit', name: '编辑文档', type: 'file' },
  { id: 'download', name: '下载', type: 'file' },
  // { id: 'rename', name: '重命名', type: 'file' },
  // { id: 'move', name: '移动', type: 'file' },
  // { id: 'delete', name: '删除', type: 'file' },
  // { id: 'parse', name: '解析', type: 'file' },
  // { id: 'parseSettings', name: '解析设置', type: 'file' },
  // { id: 'report', name: '查看报告', type: 'file' }
]

const headerOperations = [
  { id: 'upload', name: '上传文件', type: 'header' },
  { id: 'createFolder', name: '新建文件夹', type: 'header' },
]

// 获取角色列表
const getRoleList = async () => {
  try {
    const res = await getRolePage({
      pageNo: 1,
      pageSize: 100
    })
    roleList.value = res.list
  } catch (error) {
    console.error('获取角色列表失败:', error)
    message.error('获取角色列表失败')
  }
}

const getHeaderOptions = (role) => {
  return headerOperations.map((child) => ({
      ...child,
      id: `${role.id}-${child.id}-${child.type}-${currentRow.value? currentRow.value.id : ''}`,
      parentId: role.id,
      name: child.name,
      code: child.id,
      BottomName: child.name,
    }))
}

const getBottomOptions = (role) => {
  return [
    {
      id: 'fileOperation',
      name: '文件操作',
      parentId: role.id,
      parentName: role.name,
      type: 'operation',
      children: fileOperations.map((child ) => ({
        ...child,
        id: `${role.id}-${child.id}-${child.type}-${currentRow.value? currentRow.value.id : ''}`,
        code: child.id,
        parentId: role.id,
      }))
    },
    {
      id: 'folderOperation',
      name: '文件夹操作',
      parentId: role.id,
      parentName: role.name,
      type: 'operation',
      children: folderOperations.map((child ) => ({
        ...child,
        id: `${role.id}-${child.id}-${child.type}-${currentRow.value? currentRow.value.id : ''}`,
        code: child.id,
        parentId: role.id,
      }))
    }

  ]
}

const roleTree = computed(() => {
  return roleList.value.map((role) => {
    return {
      ...role,
      children: operationType.value === 'header' ? getHeaderOptions(role) : getBottomOptions(role)
    }
  })
})

const treeRef = ref(null)
const operationType = ref('')
// 打开弹窗
const open = async (row, type) => {
  currentRow.value = row ? row : { id: 0 }
  dialogVisible.value = true
  operationType.value = type
  
  // 重置选择
  selectedRoles.value = []
  selectedOperations.value = []
  searchLoading.value = true
  await getRoleList()
  await getRolePermission()
  searchLoading.value = false
}

const rolePermissionList = ref([])
const rolePermissionIds = ref([])
// 获取角色与权限关系
const getRolePermission = async () => {
  try {
    const res = await getCloudButtonRole({
      klCloudId: currentRow.value ? currentRow.value.id : ''
    })
    const filteredRes = res.filter(item => item?.roleContent?.length > 0)

    const newArr = filteredRes.flatMap(item => {
      return item?.roleContent?.map(content => ({
        ...content,
        klCloudId: item.klCloudId,
        roleId: item.roleId
      }))
    })
    console.log('newArr', newArr);
    rolePermissionList.value = newArr || []
    
    const checkIds = newArr?.map(item => `${item.roleId}-${item.code}-${item.type}-${item.klCloudId}`)
    rolePermissionIds.value = res.map(item => item.id)
    treeRef.value.setCheckedKeys(checkIds)
    // if (res.code === 0) {
    //   selectedRoles.value = res.data.roleIds || []
    //   selectedOperations.value = res.data.operations || []
    // }
  } catch (error) {
    console.error('获取角色权限失败:', error)
    message.error('获取角色权限失败')
  }
}

// 提交
const handleSubmit = async () => {
  const checkedNodes = treeRef.value.getCheckedNodes()
  console.log('checkedNodes', checkedNodes);
  const result = checkedNodes.filter((node) => ['header', 'file', 'folder'].includes(node.type))
  console.log('checkedNodes', result);
  try {
    loading.value = true
    // 构建扁平化的权限数组
    // 删除数据
    const deleteStr = rolePermissionIds.value.join(',')
    if (deleteStr) {
      await deleteCloudButtonRole(deleteStr)
    }
    const knowledgeId = currentRow.value ? currentRow.value.id : ''
    const groupedData = result.reduce((acc, current) => {
      const { parentId, ...rest } = current;
      // 检查是否已有该 parentId 的分组
      const existingGroup = acc.find(item => item.roleId === parentId);
      
        if (existingGroup) {
          existingGroup.roleContent.push(rest);
        } else {
          acc.push({
            roleId: parentId,
            klCloudId: knowledgeId,
            roleContent: [rest]
          });
        }
        
        return acc;
      }, []);
    // 调用保存接口
    const res = await saveCloudButtonRole(groupedData)
    console.log('res', res);
    if (res) {
      message.success('分配成功')
      dialogVisible.value = false
      emit('success') // 通知父组件刷新
    } else {
      message.error(res.message || '分配失败')
    }
  } catch (error) {
    console.error('保存权限失败:', error)
    message.error('保存权限失败')
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.card-col {
  height: 100%;
}

.el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-card__body) {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.checkbox-group {
  height: 100%;
  min-height: 200px;
  overflow-y: auto;
}

.checkbox-item {
  margin: 10px 0;
  display: flex;
  align-items: center;
}

.role-name {
  margin-left: 8px;
  flex: 1;
  font-size: 14px;
  cursor: pointer;
}
</style>